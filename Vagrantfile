# -*- mode: ruby -*-
# vi: set ft=ruby :

Vagrant.configure("2") do |config|
  config.vm.box = "fx-local"
  config.vm.box_url = "https://s3.us-east-2.amazonaws.com/fx-support/vagrant/local/boxes.json"
  config.vm.box_version = "~> 0.11.0"

  # For Parallels use this box:
  # config.vm.box_url = "https://s3.us-east-2.amazonaws.com/fx-support/vagrant/local/boxes/cxlratr_local_0.11.0.box"

  config.vm.network "private_network", ip: "*********"

  config.vm.synced_folder ".", "/vagrant", disabled: true
  config.vm.synced_folder ".", "/var/www"

 config.vm.provider "virtualbox" do |vb|
   vb.cpus = 2
   vb.memory = "3072"
 end

  config.vm.provider "parallels" do |prl|
    prl.cpus = 2
    prl.memory = 3072
  end
end