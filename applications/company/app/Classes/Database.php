<?php

namespace App\Classes;

use Core\StaticAccessors\Config;

use PDO;

/**
 * Class Database
 *
 * Legacy DB class required by older parts of the codebase, it is currently aliased to `Connection` in the global
 * namespace
 *
 * @package App\Classes
 */
class Database
{
    /**
     * PDO instance
     *
     * @var PDO
     */
    protected $dbh;

    /**
     * Database constructor
     */
    public function __construct()
    {
        $config = Config::get('database.connections.app');

        $dbn = 'mysql:host=' . $config['host'] . ';dbname=' . $config['database'];

        // set options
        $options = [
            PDO::ATTR_PERSISTENT => false,
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ];

        $this->dbh = new PDO($dbn, $config['username'], $config['password'], $options);
    }

    /**
     * Retrieve PDO instance
     *
     * @return PDO
     */
    public function dbConnect()
    {
        return $this->dbh;
    }
}
