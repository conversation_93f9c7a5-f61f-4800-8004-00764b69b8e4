<?php

namespace App\Classes\FX\API;

use PDO;

class GetAllWallBraces extends Base
{
    private $results;

    public function getAllWallBraces()
    {
        $st = $this->db->prepare("SELECT HEX(drawingWallBraces.drawingID) AS drawingID, drawingWallBraces.nodeID, drawingWallBraces.xPos, drawingWallBraces.yPos, drawingWallBraces.zRotation, drawingWallBraces.createdAt, drawingWallBraces.updatedAt FROM drawingWallBraces
                                  JOIN appDrawing ON appDrawing.drawingID = drawingWallBraces.drawingID
                                  JOIN evaluation ON evaluation.evaluationID = appDrawing.evaluationID
                                  JOIN project ON project.projectID = evaluation.projectID
                                  JOIN customer ON customer.customerID = project.customerID
                                  JOIN user ON customer.companyID = user.companyID
                                  JOIN companies ON user.companyID = companies.companyID
                                  WHERE user.token = :token AND companies.isActive = '1' AND user.userActive = '1' ");
        $st->bindParam(":token", $this->token);
        $st->execute();

        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $returnObject[] = $row;
            }
            $this->results = array('message' => 'success', 'wallBraces' => $returnObject);
        } else {
            $this->results = array('message' => 'No wallBraces found');
        }
    }

    public function getResults()
    {
        return $this->results;
    }
}
