<?php

namespace App\Classes\FX\API;

use PDO;

class GetAll<PERSON>allCracks extends Base
{
    private $results;

    public function getAllWallCracks()
    {
        $st = $this->db->prepare("SELECT HEX(drawingWallCracks.drawingID) AS drawingID, drawingWallCracks.nodeID, drawingWallCracks.xPos, drawingWallCracks.yPos, drawingWallCracks.createdAt, drawingWallCracks.updatedAt FROM drawingWallCracks
                                  JOIN appDrawing ON appDrawing.drawingID = drawingWallCracks.drawingID
                                  JOIN evaluation ON evaluation.evaluationID = appDrawing.evaluationID
                                  JOIN project ON project.projectID = evaluation.projectID
                                  JOIN customer ON customer.customerID = project.customerID
                                  JOIN user ON customer.companyID = user.companyID
                                  JOIN companies ON user.companyID = companies.companyID
                                  WHERE user.token = :token AND companies.isActive = '1' AND user.userActive = '1' ");
        $st->bindParam(":token", $this->token);
        $st->execute();

        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $returnObject[] = $row;
            }
            $this->results = array('message' => 'success', 'wallCracks' => $returnObject);
        } else {
            $this->results = array('message' => 'No wallCracks found');
        }
    }

    public function getResults()
    {
        return $this->results;
    }
}
