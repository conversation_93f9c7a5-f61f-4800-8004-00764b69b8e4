<?php

namespace App\Classes\FX\Reports;

use App\Classes\FX\Report;
use App\Traits\FX\Report\StartEndDateTrait;

/**
 * Class EmployeeSalesPerformanceReport
 *
 * Note: this was moved from old SalesReport class and modified slightly
 *
 * @package App\Classes\FX\Reports
 */
class EmployeeSalesPerformanceReport extends Report
{
    use StartEndDateTrait;

    /**
     * Run report
     *
     * @return array
     * @throws \Exception
     */
    public function run()
    {
        $sql =<<<SQL
SELECT
	CONCAT(`user`.`userFirstName`, ' ', `user`.`userLastName`) AS `employeeName`,
	IFNULL(`appointments`.`count`, 0) AS `appointments`,
	IFNULL(`appointments`.`bidsSent`, 0) AS `bidsSent`,
	IFNULL(`appointments`.`sales`, 0) AS `sales`,
	IF(IFNULL(`appointments`.`count`, 0) > 0, (IFNULL(`appointments`.`sales`, 0) / IFNULL(`appointments`.`count`, 0)) * 100, 0) as `closingPercentage`,
	IFNULL(`appointments`.`grossTotal`, 0) AS `grossTotal`,
	IFNULL(`appointments`.`grossSales`, 0) AS `grossSales`,
	IF(IFNULL(`appointments`.`count`, 0) > 0, IFNULL(`appointments`.`grossSales`, 0) / IFNULL(`appointments`.`count`, 0), 0) AS `avgSold`
FROM `user`
LEFT JOIN (
    SELECT
		`p`.`projectSalesperson` AS `userID`,
        COUNT(*) AS `count`,
        SUM(IF(`bids`.`projectID` IS NOT NULL OR `customBids`.`projectID` IS NOT NULL, 1, 0)) AS `bidsSent`,
        SUM(IF(IFNULL(`bids`.`sales`, 0) + IFNULL(`customBids`.`sales`, 0) > 0, 1, 0)) AS `sales`,
        SUM(IFNULL(`bids`.`grossTotal`, 0) + IFNULL(`customBids`.`grossTotal`, 0)) AS `grossTotal`,
        SUM(IFNULL(`bids`.`grossSales`, 0) + IFNULL(`customBids`.`grossSales`, 0)) AS `grossSales`
    FROM `projectSchedule` AS `ps`
    INNER JOIN `project` AS `p`
        ON `p`.`projectID` = `ps`.`projectID`
    LEFT JOIN (
        SELECT
            `p_1`.`projectID`,
            SUM(IF(`eb`.`bidAccepted` IS NOT NULL AND `eb`.`bidAccepted` >= :startDate AND `eb`.`bidAccepted` <= :endDate, 1, 0)) AS `sales`,
            SUM(eb.bidTotal + IFNULL(eb.bidScopeChangeTotal, 0)) AS `grossTotal`,
            SUM(IF(`eb`.`bidAccepted` IS NOT NULL AND `eb`.`bidAccepted` >= :startDate AND `eb`.`bidAccepted` <= :endDate, eb.bidTotal + IFNULL(eb.bidScopeChangeTotal, 0), 0)) AS `grossSales`
        FROM `evaluation` AS `e`
        LEFT JOIN `evaluationBid` AS `eb`
            ON `eb`.`evaluationID` = `e`.`evaluationID`
        INNER JOIN `project` AS `p_1`
            ON `p_1`.`projectID` = `e`.`projectID`
        INNER JOIN `property` AS `p_2`
            ON `p_2`.`propertyID` = `p_1`.`propertyID`
        INNER JOIN `customer` AS `c_1`
            ON `c_1`.`customerID` = `p_2`.`customerID`
        WHERE `c_1`.`companyID` = :companyID
            AND `p_1`.`projectCancelled` IS NULL
            AND `p_1`.`deletedAt` IS NULL
            AND `e`.`evaluationCancelled` IS NULL
            AND `e`.`deletedAt` IS NULL
            AND `eb`.`bidFirstSent` IS NOT NULL
            AND `eb`.`bidFirstSent` >= :startDate
            AND `eb`.`bidFirstSent` <= :endDate
        GROUP BY `e`.`projectID`
    ) AS `bids` ON `bids`.`projectID` = `p`.`projectID`
    LEFT JOIN (
        SELECT
            `p_1`.`projectID`,
            SUM(IF(`ec`.`bidAccepted` IS NOT NULL AND `ec`.`bidAccepted` >= :startDate AND `ec`.`bidAccepted` <= :endDate, 1, 0)) AS `sales`,
            SUM(ec.bidTotal + IFNULL(ec.bidScopeChangeTotal, 0)) AS `grossTotal`,
            SUM(IF(`ec`.`bidAccepted` IS NOT NULL AND `ec`.`bidAccepted` >= :startDate AND `ec`.`bidAccepted` <= :endDate, ec.bidTotal + IFNULL(ec.bidScopeChangeTotal, 0), 0)) AS `grossSales`
        FROM `evaluation` AS `e`
        LEFT JOIN `customBid` AS `ec`
            ON `ec`.`evaluationID` = `e`.`evaluationID`
        INNER JOIN `project` AS `p_1`
            ON `p_1`.`projectID` = `e`.`projectID`
        INNER JOIN `property` AS `p_2`
            ON `p_2`.`propertyID` = `p_1`.`propertyID`
        INNER JOIN `customer` AS `c_1`
            ON `c_1`.`customerID` = `p_2`.`customerID`
        WHERE `c_1`.`companyID` = :companyID
            AND `p_1`.`projectCancelled` IS NULL
            AND `p_1`.`deletedAt` IS NULL
            AND `e`.`evaluationCancelled` IS NULL
            AND `e`.`deletedAt` IS NULL
            AND `ec`.`bidFirstSent` IS NOT NULL
            AND `ec`.`bidFirstSent` >= :startDate
            AND `ec`.`bidFirstSent` <= :endDate
        GROUP BY `e`.`projectID`
    ) AS `customBids` ON `customBids`.`projectID` = `p`.`projectID`
    INNER JOIN `customer` AS `c`
        ON `c`.`customerID` = `p`.`customerID`
    INNER JOIN `companies` AS `c_1`
        ON `c_1`.`companyID` = `c`.`companyID`
    INNER JOIN `timezones` AS `t`
        ON `t`.`timezoneID` = `c_1`.`timezoneID`
    WHERE `c`.`companyID` = :companyID
        AND `ps`.`scheduleType` = 'Evaluation'
        AND CONVERT_TZ(`ps`.`scheduledStart`, `t`.`timezone`, 'UTC') < :endDate
        AND CONVERT_TZ(`ps`.`scheduledEnd`, `t`.`timezone`, 'UTC') >= :startDate
        AND `ps`.`cancelledAt` IS NULL
        AND `ps`.`replacedAt` IS NULL
        AND `ps`.`deletedAt` IS NULL
	GROUP BY `p`.`projectSalesperson`
) AS `appointments`
	ON `appointments`.`userID` = `user`.`userID`
WHERE `user`.`companyID` = :companyID 
AND `user`.`userActive` = '1' 
AND `user`.`sales` = 1
ORDER BY `user`.`userFirstName` ASC, `user`.`userLastName` ASC
SQL;

        $query = $this->getDb()->prepare($sql);
        $query->bindValue(':companyID', $this->getCompanyID());
        $query->bindValue(':startDate', $this->getStartDate()->toDateTimeString());
        $query->bindValue(':endDate', $this->getEndDate()->toDateTimeString());
        $query->execute();
        $headers = [
            'employeeName' => [
                'name' => 'Employee',
                'description' => ''
            ],
            'appointments' => [
                'name' => 'Appointments',
                'description' => 'Number of appointments scheduled'
            ],
            'bidsSent' => [
                'name' => 'Bids Sent',
                'description' => 'Number of bid sent from those appointments'
            ],
            'sales' => [
                'name' => 'Sales',
                'description' => 'Number of bids accepted of those sent'
            ],
            'closingPercentage' => [
                'name' => 'Closing %',
                'description' => 'Percentage of appointments which resulted in sale (Number of Sales / Number of Appointments)',
                'type' => Report::VALUE_TYPE_PERCENT
            ],
            'grossTotal' => [
                'name' => 'Gross Total',
                'description' => 'Gross total of all bids sent',
                'type' => Report::VALUE_TYPE_MONEY
            ],
            'grossSales' => [
                'name' => 'Gross Sales',
                'description' => 'Gross sales of all bids accepted',
                'type' => Report::VALUE_TYPE_MONEY
            ],
            'avgSold' => [
                'name' => 'Avg. Dollars Sold',
                'description' => 'Average dollar amount sold per appointment (Gross Sales Total / Number of Appointments)',
                'type' => Report::VALUE_TYPE_MONEY
            ]
        ];
        $employees = [];
        foreach ($this->fetchResults($query) as $employee) {
            $_employee = [];
            foreach ($headers as $key => $header) {
                $value =& $employee[$key];
                if (isset($header['type'])) {
                    $value = $this->formatValue($header['type'], $value);
                }
                $_employee[$key] = $value;
            }
            $employees[] = $_employee;
        }
        return [
            'headers' => $headers,
            'data' => $employees
        ];
    }
}
