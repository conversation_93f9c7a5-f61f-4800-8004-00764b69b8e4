<?php

namespace App\Classes\FX\Reports;

use App\Classes\FX\Report;
use App\Traits\FX\Report\StartEndDateTrait;

/**
 * Class LeadConversionReport
 *
 * Note: this was moved from old SalesReport class and modified slightly
 *
 * @package App\Classes\FX\Reports
 */
class LeadConversionReport extends Report
{
    use StartEndDateTrait;

    /**
     * Run report
     *
     * @return array
     * @throws \Exception
     */
    public function run()
    {
        $sql_1 = <<<SQL
SELECT
  COUNT(*) AS customerLeads,
  SUM(IF(projects_result_aggregate.sales > 0, 1, 0)) AS customerSales,
  (SUM(IF(projects_result_aggregate.sales > 0, 1, 0)) / COUNT(*)) * 100 AS customerClosing 
FROM customer
  INNER JOIN
  (SELECT
     MIN(projects_result.projectID) AS first_projectID,
     projects_result.customerID,
     SUM(projects_result.sales)     AS sales
   FROM
     (SELECT
        p_0.projectID,
        p_0.customerID,
        IFNULL(bids.sales, 0) + IFNULL(customBids.sales, 0) AS sales
      FROM project AS p_0
        LEFT JOIN
        (SELECT
           p_1.projectID,
           SUM(IF(eb.bidAccepted IS NULL, 0, eb.bidTotal + IFNULL(eb.bidScopeChangeTotal, 0))) AS sales,
           c_1.customerID
         FROM evaluation AS e
           LEFT JOIN evaluationBid AS eb
             ON eb.evaluationID = e.evaluationID
           JOIN project AS p_1
             ON p_1.projectID = e.projectID
           JOIN property AS p_2
             ON p_2.propertyID = p_1.propertyID
           JOIN customer AS c_1
             ON c_1.customerID = p_2.customerID
         WHERE c_1.companyID = :companyID
               AND p_1.projectCancelled IS NULL
               AND p_1.deletedAt IS NULL
               AND e.evaluationCancelled IS NULL
               AND e.deletedAt IS NULL
               AND eb.bidAccepted IS NOT NULL 
               AND eb.bidFirstSent IS NOT NULL
         GROUP BY e.projectID
        ) AS bids ON p_0.projectID = bids.projectID
        LEFT JOIN
        (SELECT
           p_1.projectID,
           SUM(IF(ec.bidAccepted IS NULL, 0, ec.bidTotal + IFNULL(ec.bidScopeChangeTotal, 0))) AS sales,
           c_1.customerID
         FROM evaluation AS e
           LEFT JOIN customBid AS ec
             ON ec.evaluationID = e.evaluationID
           JOIN project AS p_1
             ON p_1.projectID = e.projectID
           JOIN property AS p_2
             ON p_2.propertyID = p_1.propertyID
           JOIN customer AS c_1
             ON c_1.customerID = p_2.customerID
         WHERE c_1.companyID = :companyID
               AND p_1.projectCancelled IS NULL
               AND p_1.deletedAt IS NULL
               AND e.evaluationCancelled IS NULL
               AND e.deletedAt IS NULL
               AND ec.bidAccepted IS NOT NULL 
               AND ec.bidFirstSent IS NOT NULL
         GROUP BY e.projectID
        ) AS customBids ON p_0.projectID = customBids.projectID
        JOIN property AS p_2
          ON p_2.propertyID = p_0.propertyID
        JOIN customer AS c_1
          ON c_1.customerID = p_2.customerID
      WHERE c_1.companyID = :companyID
        AND p_0.deletedAt IS NULL
     ) AS projects_result
   GROUP BY customerID
  ) AS projects_result_aggregate ON projects_result_aggregate.customerID = customer.customerID
  JOIN project ON projects_result_aggregate.first_projectID = project.projectID
WHERE customer.companyID = :companyID
      AND customer.createdAt >= :startDate
      AND customer.createdAt <= :endDate
      AND customer.deletedAt IS NULL
SQL;
        $sql_2 = <<< SQL
SELECT
  COUNT(*) AS projectLeads,
  SUM(IF(projectSales > 0, 1, 0)) AS projectSales,
  (SUM(IF(projectSales > 0, 1, 0)) / COUNT(*)) * 100 AS projectClosing
FROM (
      SELECT 
        SUM(IF(eb.bidTotal IS NULL, IF(ec.bidAccepted IS NULL, 0, 1), IF(eb.bidAccepted IS NULL, 0, 1))) AS projectSales
      FROM project
        LEFT JOIN evaluation AS e
          ON e.projectID = project.projectID
        LEFT JOIN evaluationBid AS eb
          ON eb.evaluationID = e.evaluationID
        LEFT JOIN customBid AS ec
          ON ec.evaluationID = e.evaluationID
        LEFT JOIN property AS p_2
          ON p_2.propertyID = project.propertyID
        LEFT JOIN customer AS c_1
          ON c_1.customerID = p_2.customerID
      WHERE c_1.companyID = :companyID
            AND e.evaluationCancelled IS NULL
            AND e.deletedAt IS NULL
            AND project.createdAt >= :startDate
            AND project.createdAt <= :endDate
            AND project.projectCancelled IS NULL
            AND project.deletedAt IS NULL
      GROUP BY project.projectID) AS project_aggregate
SQL;

        $results = [];
        for ($i = 1; $i <= 2; $i++) {
            $query = $this->getDb()->prepare(${'sql_' . $i});
            $query->bindValue(':companyID', $this->getCompanyID());
            $query->bindValue(':startDate', $this->getStartDate()->toDateTimeString());
            $query->bindValue(':endDate', $this->getEndDate()->toDateTimeString());
            $query->execute();
            if ($query->rowCount() !== 1) {
                // @todo handle error here maybe?
                continue;
            }
            $results = array_merge($results, $this->fetchResult($query));
        }

        $columns = [
            'customerLeads' => [
                'name' => 'Customer Leads',
                'description' => 'Number of customers added to the system'
            ],
            'customerSales' => [
                'name' => 'Customer Sales',
                'description' => 'Number of customers with at least one accepted bid'
            ],
            'customerClosing' => [
                'name' => 'Customer Closing %',
                'description' => 'Customer Sales / Customer Leads',
                'type' => Report::VALUE_TYPE_PERCENT
            ],
            'projectLeads' => [
                'name' => 'Project Leads',
                'description' => 'Number of projects added to the system'
            ],
            'projectSales' => [
                'name' => 'Project Sales',
                'description' => 'Number of projects with at least one accepted bid'
            ],
            'projectClosing' => [
                'name' => 'Project Closing %',
                'description' => 'Project Sales / Project Leads',
                'type' => Report::VALUE_TYPE_PERCENT
            ]
        ];

        return $this->handleValues($columns, $results);
    }
}
