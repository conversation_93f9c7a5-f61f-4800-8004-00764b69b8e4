<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Classes\Acl;
use App\Resources\Company\CustomReport\ResultResource;
use App\Resources\Company\CustomReportResource;
use App\Services\CompanyCustomReportService;
use App\Traits\Console\{CompanyTrait, DataImportExportTrait, NowTrait, OutputExceptionTrait};
use Common\Models\CompanyCustomReport;
use Core\Components\Console\Commands\BaseCommand;
use Core\Components\Resource\Classes\Entity;
use Core\Exceptions\AppException;
use Throwable;

/**
 * Class CompanyCustomReportCommand
 *
 * @package App\Console\Commands
 */
class CompanyCustomReportCommand extends BaseCommand
{
    use CompanyTrait;
    use DataImportExportTrait;
    use NowTrait;
    use OutputExceptionTrait;

    /**
     * Create new report, setup templates for config and query
     *
     * @throws AppException
     */
    public function create(): void
    {
        $company = $this->getCompany();
        $name = $this->console->prompt('Name');
        $is_restricted = $this->console->confirm('Is this report restricted?', 'n');
        try {
            $id = CustomReportResource::make(Acl::make())->create(Entity::make([
                'company_id' => $company->getKey(),
                'name' => $name,
                'is_restricted' => $is_restricted,
                'status' => CustomReportResource::STATUS_SETUP
            ]))->run();
            $this->console->line('Report %s created', $id);
            $this->console->info('Done');
        } catch (Throwable $e) {
            $this->outputException($e);
        }
    }

    /**
     * Change status of a report
     *
     * @throws AppException
     */
    public function changeStatus(): void
    {
        $company = $this->getCompany();

        $reports = CompanyCustomReport::query()
            ->where('companyID', $company->getKey())
            ->orderBy('createdAt', 'desc')
            ->get()
            ->keyBy('companyCustomReportID');
        if (count($reports) === 0) {
            $this->console->info('Company has no reports');
            return;
        }
        $status_menu = [
            CompanyCustomReport::STATUS_SETUP => 'Setup',
            CompanyCustomReport::STATUS_ACTIVE => 'Active',
            CompanyCustomReport::STATUS_INACTIVE => 'Inactive'
        ];
        $report_menu = [];
        foreach ($reports as $id => $report) {
            $report_menu[$id] = "{$report->name} [{$status_menu[$report->status]}]";
        }
        $this->console->line('Choose a report:');
        $this->console->line();
        $report_id = $this->console->menu($report_menu, [
            'cancel' => true,
            'cancel_title' => '[cancel]'
        ]);
        if ($report_id === false) {
            $this->console->error('Aborted');
            return;
        }
        $report = $reports[$report_id];
        $this->console->line('Report: %s', $report->name);
        $this->console->line('Choose new status:');
        $this->console->line();
        unset($status_menu[$report->status]);
        $status = $this->console->menu($status_menu, [
            'cancel' => true,
            'cancel_title' => '[cancel]'
        ]);
        if ($status === false) {
            $this->console->error('Aborted');
            return;
        }
        $report->fill(['status' => $status])->save();
        $this->console->info('Status updated successfully');
    }

    /**
     * Run report without reporting any errors or recording results
     *
     * Useful for testing in setup mode.
     */
    public function run(): void
    {
        try {
            $company = $this->getCompany();

            $reports = CompanyCustomReport::query()
                ->where('companyID', $company->getKey())
                ->orderBy('createdAt', 'desc')
                ->get()
                ->keyBy('companyCustomReportID');
            if (count($reports) === 0) {
                $this->console->info('Company has no reports');
                return;
            }
            $statuses = [
                CompanyCustomReport::STATUS_SETUP => 'Setup',
                CompanyCustomReport::STATUS_ACTIVE => 'Active',
                CompanyCustomReport::STATUS_INACTIVE => 'Inactive'
            ];
            $report_menu = [];
            foreach ($reports as $id => $report) {
                $report_menu[$id] = "{$report->name} [{$statuses[$report->status]}]";
            }
            $this->console->line('Choose a report:');
            $this->console->line();
            $report_id = $this->console->menu($report_menu, [
                'cancel' => true,
                'cancel_title' => '[cancel]'
            ]);
            if ($report_id === false) {
                $this->console->error('Aborted');
                return;
            }
            $report = $reports[$report_id];
            $this->console->line('Report: %s', $report->name);

            $service = CompanyCustomReportService::fromModel($report);

            $inputs = $service->getInputs();
            $data = [];
            if (count($inputs) > 0) {
                foreach ($inputs as $name => $config) {
                    $data[$name] = $this->console->get($name, $config['label']);
                }
            }
            if (!$service->isQueryScoped()) {
                $this->console->error('WARNING: {{company_id}} variable not defined in query');
            }
            $file = $service->testRun($data);
            $file_path = $file->getPathname();
            unset($file);
            $this->outputCsv($file_path, (int) $this->args->get('sample', 100));
        } catch (Throwable $e) {
            $this->outputException($e);
        } finally {
            if (isset($file_path) && !unlink($file_path)) {
                throw new AppException('Unable to unlink file: %s', $file_path);
            }
        }
    }

    /**
     * Clean old report results and remove data
     */
    public function clean(): void
    {
        try {
            $days = (int) $this->console->get('days', 'Expiration Days');
            $now = $this->getNow();
            $count = ResultResource::clearExpired($days, $now);
            $this->console->info('Deleted %d expired results', $count);
        } catch (Throwable $e) {
            $this->outputException($e);
        }
    }
}
