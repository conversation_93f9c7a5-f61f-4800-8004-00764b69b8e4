<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Services\TrainingService;
use App\Traits\Console\{CompanyTrait, OutputExceptionTrait, UserTrait};
use Core\Components\Console\Commands\BaseCommand;
use Throwable;

/**
 * Class TrainingCommand
 *
 * @package App\Console\Commands
 */
class TrainingCommand extends BaseCommand
{
    use CompanyTrait;
    use UserTrait;
    use OutputExceptionTrait;

    /**
     * Enable training access for individual user
     */
    public function enableUser(): void
    {
        try {
            $user = $this->getUser();
            if ($user->isTraining) {
                $this->console->info('User already has training enabled');
                return;
            }
            if ($user->trainingSectionModules()->count() === 0) {
                $this->console->error('Training never enabled for user, ensure company is setup with training ability');
                return;
            }
            $user->isTraining = true;
            $user->trainingCompletedAt = null;
            $user->save();
            $this->console->info('Training enabled');
        } catch (Throwable $e) {
            $this->outputException($e);
        }
    }

    /**
     * Enable training for all users of a company
     */
    public function enableCompanyUsers(): void
    {
        try {
            $company = $this->getCompany();
            if (!$this->console->confirm('Are you sure you want to enable training for all users of this company?')) {
                $this->console->error('Aborted');
                return;
            }
            foreach ($company->users as $user) {
                $user->isTraining = true;
                $user->save();
            }
            $this->console->info('Done');
        } catch (Throwable $e) {
            $this->outputException($e);
        }
    }

    /**
     * Clear every type of cache for training
     */
    public function clearAllCaches(): void
    {
        TrainingService::clearAllCaches();
        $this->console->info('All caches cleared');
    }

    /**
     * Clear just static caches without user specific content
     */
    public function clearStaticCaches(): void
    {
        TrainingService::clearStaticCaches();
        $this->console->info('Static caches cleared');
    }

    /**
     * Clear all user specific caches
     */
    public function clearUserCaches(): void
    {
        TrainingService::clearUserCaches();
        $this->console->info('User caches cleared');
    }
}
