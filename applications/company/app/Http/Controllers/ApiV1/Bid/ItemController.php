<?php

namespace App\Http\Controllers\ApiV1\Bid;

use App\Exceptions\Api\BadRequestException;
use App\Exceptions\ServiceException;
use App\Resources\Bid\ItemResource;
use App\Services\BidItemUpgradeService;
use App\Traits\Resource\Controller\ActionTrait;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Http\Requests\ResourceRequest;
use Exception;

class ItemController
{
    use ActionTrait;

    protected $resource = ItemResource::class;

    public function __construct()
    {
        $this->registerFormat('bid-v1', 'application/vnd.adg.fx.bid-v1+json');
        $this->registerFormat('detail-v1', 'application/vnd.adg.fx.detail-v1+json');
        $this->registerFormat('detail-v1', 'application/vnd.adg.fx.detail-v1+json');
        $this->registerFormat(
            'report-financing-opportunities-v1',
            'application/vnd.adg.fx.report-financing-opportunities-v1+json'
        );
    }

    public function upgrade($id)
    {
        try {
            (new BidItemUpgradeService(Auth::acl(), $id))->run();
            return Response::create('', 204);
        } catch (ServiceException $e) {
            throw new BadRequestException(1011, $e->getMessage());
        } catch (Exception $e) {
            $this->handleException($e);
        }
    }

    public function customDrawingUpload($id, ResourceRequest $request)
    {
        try {
            $request = $this->handleRequest($request);
            $entity = $request->entity();
            $entity->set('item_id', $id);
            $entity = $this->getResource()->relationResource('custom_drawings')
                ->polyCreate($entity)
                ->scope($request->getScope(false))
                ->run();
            return Response::api()->fromEntity($entity)->statusCode(201);
        } catch (Exception $e) {
            $this->handleException($e);
        }
    }
}
