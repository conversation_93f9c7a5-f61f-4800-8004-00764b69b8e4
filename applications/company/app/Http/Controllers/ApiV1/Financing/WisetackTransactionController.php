<?php

namespace App\Http\Controllers\ApiV1\Financing;

use App\Resources\Financing\WisetackTransactionResource;
use App\Traits\Resource\Controller\ActionTrait;

class WisetackTransactionController
{
    use ActionTrait;

    protected $resource = WisetackTransactionResource::class;

    public function __construct()
    {
        $this->registerFormat('collection-v1', 'application/vnd.adg.fx.collection-v1+json');
        $this->registerFormat(
            'report-financing-transactions-v1',
            'application/vnd.adg.fx.report-financing-transactions-v1+json'
        );
    }
}
