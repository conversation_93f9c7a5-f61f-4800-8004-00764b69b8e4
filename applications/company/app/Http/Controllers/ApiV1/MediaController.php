<?php

namespace App\Http\Controllers\ApiV1;

use App\Exceptions\Api\ForbiddenException;
use App\Resources\MediaResource;
use App\Traits\Resource\Controller\PolyActionTrait;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\Classes\URLBuilder;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Classes\Scope;
use Throwable;

class MediaController
{
    use PolyActionTrait;

    protected $resource = MediaResource::class;

    public function __construct()
    {
        $this->registerFormat('bid-list-v1', 'application/vnd.adg.fx.bid-list-v1+json');
        $this->registerFormat('collection-v1', 'application/vnd.adg.fx.collection-v1+json');
    }

    public function generatePublicUrl($id)
    {
        try {
            $scope = Scope::make()
                ->with(['file_media_urls']);
            $media = MediaResource::make(Auth::acl())->entity($id)->scope($scope)->run();
            $builder = URLBuilder::fromString($media['file_media_urls']['original']);
            return Response::api([
                'media_url' => $builder->csm()->build()
            ]);
        } catch (Throwable $e) {
            $this->handleException($e);
        }
    }
}
