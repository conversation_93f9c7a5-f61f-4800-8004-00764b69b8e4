<?php

namespace App\Http\Controllers\App\API;

use App\Resources\CustomerResource;
use App\Resources\CompanyResource;
use App\Resources\Bid\ItemResource;
use App\Resources\FileResource;
use App\Resources\Project\EventResource;
use App\Resources\Project\FileResource as ProjectFileResource;
use App\Resources\ProjectResource;
use Core\Components\Http\Exceptions\HttpResponseException;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Classes\Scope;
use App\Classes\Acl;
use Ramsey\Uuid\Uuid;
use Carbon\Carbon;
use Core\Components\DB\StaticAccessors\DB;

if (!defined('PATH_CLASSES')) {
    define('PATH_CLASSES', PATH_ROOT . 'app-legacy/includes/classes/');
}

class CustomerPortalController
{
    /**
     * Render the customer portal page
     *
     * @param string $token
     * @return mixed
     * @throws HttpResponseException
     */
    public function render($token)
    {
        // Validate UUID format
        if (!$this->isValidUuid($token)) {
            throw new HttpResponseException(404);
        }

        // Find customer by UUID
        $customer = $this->findCustomerByUuid($token);

        if (!$customer) {
            throw new HttpResponseException(404);
        }

        $companyResource = CompanyResource::make(Acl::make());
        $companyResource->setMediaCompanyID($customer->companyID);
        $logoUrl = $companyResource->getMedia()->get('logo')->getVariant('original')->getUrl($customer->companyID)->csm()->build();

        $company_data = $this->getCompanyData($customer->companyID);

        $salesperson_data = $this->getSalespersonData($customer->customerID, $customer->companyID);

        // Prepare data for the view
        $customer_data = [
            'customerUUID' => Uuid::fromBytes($customer->customerUUID)->toString(),
            'businessName' => $customer->businessName,
            'firstName' => $customer->firstName,
            'lastName' => $customer->lastName,
            'company' => $company_data,
            'company_logo' => $logoUrl,
            'salesperson' => $salesperson_data,
        ];

        return Response::view('pages.customer-portal', [
            'customer_data' => $customer_data,
        ])->share('include_layout', false)
          ->share('layout-header', false);
    }

    /**
     * Validate if the provided string is a valid UUID
     *
     * @param string $uuid
     * @return bool
     */
    private function isValidUuid($uuid)
    {
        try {
            Uuid::fromString($uuid);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Find customer by UUID
     *
     * @param string $uuid
     * @return \Common\Models\Customer|null
     */
    private function findCustomerByUuid($uuid)
    {
        try {
            $acl = Acl::make();

            $customer = CustomerResource::make($acl)
                ->newScopedQuery()
                ->where('customerUUID', Uuid::fromString($uuid)->getBytes())
                ->whereNull('deletedAt')
                ->first();

            return $customer;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get company data for customer portal
     *
     * @param int $companyID
     * @return array|null
     */
    private function getCompanyData($companyID)
    {
        try {
            $acl = Acl::make()->setCompanyID($companyID);

            $company_resource = CompanyResource::make($acl);

            $company_scope = Scope::make()
                ->fields([
                    'name', 'address', 'address_2', 'city', 'state', 'zip',
                    'website', 'email_from', 'logo_file_id'
                ])
                ->with([
                    'phones' => [
                        'fields' => ['description', 'number', 'is_primary']
                    ],
                    'logo_media_urls' => []
                ]);

            $company = $company_resource
                ->entity($companyID)
                ->scope($company_scope)
                ->run();

            return $company;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get salesperson data for customer portal
     *
     * @param int $customerID
     * @param int $companyID
     * @return array|null
     */
    private function getSalespersonData($customerID, $companyID)
    {
        try {
            $acl = Acl::make()->setCompanyID($companyID);
            $projectResource = ProjectResource::make($acl);

            // Find the most recent project for this customer to get the salesperson using Resource pattern
            $project = $projectResource->newScopedQuery()
                ->ofCompany($companyID)
                ->select([
                    'project.projectID',
                    'project.projectSalesperson',
                    'user.userID',
                    'user.userFirstName',
                    'user.userLastName',
                    'user.userEmail',
                    'userPhone.phoneNumber',
                    'userPhone.phoneDescription'
                ])
                ->leftJoin('user', 'user.userID', '=', 'project.projectSalesperson')
                ->leftJoin('userPhone', function($join) {
                    $join->on('userPhone.userID', '=', 'user.userID')
                         ->where('userPhone.isPrimary', 1)
                         ->whereNull('userPhone.deletedAt');
                })
                ->where('customer.customerID', $customerID)
                ->whereNotNull('project.projectSalesperson')
                ->whereNull('project.deletedAt')
                ->whereNull('property.deletedAt')
                ->whereNull('customer.deletedAt')
                ->orderBy('project.createdAt', 'desc')
                ->first();

            if ($project && $project->userID) {
                $firstName = $project->userFirstName ?: '';
                $lastName = $project->userLastName ?: '';
                $initials = strtoupper(substr($firstName, 0, 1) . substr($lastName, 0, 1));

                return [
                    'id' => $project->userID,
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'full_name' => trim($firstName . ' ' . $lastName),
                    'initials' => $initials,
                    'email' => $project->userEmail,
                    'phone' => $project->phoneNumber,
                    'phone_description' => $project->phoneDescription ?: 'Primary'
                ];
            }

            return null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get upcoming appointments for a customer
     *
     * @param string $customerUUID
     * @return \Core\Components\Http\Responses\JSONResponse
     * @throws HttpResponseException
     */
    public function getUpcomingAppointments($customerUUID)
    {
        if (!$this->isValidUuid($customerUUID)) {
            throw new HttpResponseException(404);
        }

        try {
            $customer = $this->findCustomerByUuid($customerUUID);

            if (!$customer) {
                throw new HttpResponseException(404);
            }

            $acl = Acl::make()->setCompanyID($customer->companyID);
            $eventResource = EventResource::make($acl);

            $appointments = $eventResource->newScopedQuery()
                ->ofCompany($customer->companyID)
                ->select([
                    'projectSchedule.projectScheduleID',
                    'projectSchedule.scheduleType',
                    'projectSchedule.scheduledStart',
                    'projectSchedule.scheduledEnd',
                    'projectSchedule.isAllDay',
                    'projectSchedule.description',
                    'project.projectID',
                    'project.projectDescription',
                ])
                ->where('customer.customerUUID', Uuid::fromString($customerUUID)->getBytes())
                ->where('projectSchedule.status', EventResource::STATUS_ACTIVE)
                ->where('projectSchedule.scheduledStart', '>=', Carbon::now('UTC'))
                ->where('projectSchedule.sendNotifications', '=', 1)
                ->whereNull('projectSchedule.deletedAt')
                ->whereNull('project.deletedAt')
                ->whereNull('property.deletedAt')
                ->whereNull('customer.deletedAt')
                ->orderBy('projectSchedule.scheduledStart', 'asc')
                ->get();

            $formattedAppointments = $appointments->map(function ($appointment) {
                return [
                    'id' => $appointment->projectScheduleID,
                    'type' => $appointment->scheduleType,
                    'scheduledStart' => $appointment->scheduledStart,
                    'scheduledEnd' => $appointment->scheduledEnd,
                    'isAllDay' => (bool) $appointment->isAllDay,
                    'description' => $appointment->description,
                ];
            });

            return Response::json([
                'success' => true,
                'data' => $formattedAppointments,
                'count' => $formattedAppointments->count()
            ]);

        } catch (\Exception $e) {
            return Response::json([
                'success' => false,
                'error' => 'Unable to fetch appointments',
            ], 500);
        }
    }

    /**
     * Get open invoices for a customer
     *
     * @param string $customerUUID
     * @return \Core\Components\Http\Responses\JSONResponse
     * @throws HttpResponseException
     */
    public function getOpenInvoices($customerUUID)
    {
        if (!$this->isValidUuid($customerUUID)) {
            throw new HttpResponseException(404);
        }

        try {
            $customer = $this->findCustomerByUuid($customerUUID);

            if (!$customer) {
                throw new HttpResponseException(404);
            }

            $acl = Acl::make()->setCompanyID($customer->companyID);
            $projectResource = ProjectResource::make($acl);

            $allInvoices = collect();

            // Type 3: Evaluation Invoice - from evaluationInvoice table
            $evaluationInvoices = $projectResource->newScopedQuery()
                ->ofCompany($customer->companyID)
                ->select([
                    DB::raw('3 as invoiceType'),
                    'evaluation.evaluationID',
                    'evaluationInvoice.invoiceSort',
                    'evaluationInvoice.invoiceName',
                    'evaluationInvoice.invoiceSplit',
                    'evaluationInvoice.invoiceAmount',
                    'evaluationInvoice.invoiceNumber',
                    'evaluationInvoice.invoicePaid',
                    'evaluationInvoice.invoiceLastSent',
                    DB::raw('NULL as bidAcceptanceName'),
                    DB::raw('NULL as bidAcceptanceAmount'),
                    DB::raw('NULL as bidAcceptanceNumber'),
                    DB::raw('NULL as bidAcceptanceSplit'),
                    DB::raw('NULL as projectCompleteName'),
                    DB::raw('NULL as projectCompleteAmount'),
                    DB::raw('NULL as projectCompleteNumber'),
                    DB::raw('NULL as bidScopeChangeTotal'),
                    DB::raw('NULL as bidScopeChangeNumber'),
                    DB::raw('NULL as bidTotal'),
                    DB::raw('NULL as bidFirstSent'),
                    DB::raw('NULL as bidAccepted'),
                    'evaluation.evaluationDescription',
                    'evaluation.customEvaluation',
                    DB::raw('0 as invoicePaidAccept'),
                    DB::raw('0 as invoicePaidComplete')
                ])
                ->join('evaluation', 'evaluation.projectID', '=', 'project.projectID')
                ->join('evaluationInvoice', 'evaluationInvoice.evaluationID', '=', 'evaluation.evaluationID')
                ->where('customer.customerUUID', Uuid::fromString($customerUUID)->getBytes())
                ->where(function($query) {
                    $query->whereNull('evaluationInvoice.invoicePaid')
                          ->orWhere('evaluationInvoice.invoicePaid', 0);
                })
                ->whereNull('project.deletedAt')
                ->whereNull('property.deletedAt')
                ->whereNull('customer.deletedAt')
                ->whereNull('evaluation.deletedAt')
                ->get();

            $allInvoices = $allInvoices->merge($evaluationInvoices);

            // Type 2: Bid Acceptance - from evaluationBid table
            $bidAcceptanceInvoices = $projectResource->newScopedQuery()
                ->ofCompany($customer->companyID)
                ->select([
                    DB::raw('2 as invoiceType'),
                    'evaluation.evaluationID',
                    DB::raw('NULL as invoiceSort'),
                    DB::raw('"Bid Acceptance" as invoiceName'),
                    DB::raw('NULL as invoiceSplit'),
                    'evaluationBid.bidAcceptanceAmount as invoiceAmount',
                    'evaluationBid.bidAcceptanceNumber as invoiceNumber',
                    'evaluationBid.invoicePaidAccept as invoicePaid',
                    DB::raw('NULL as invoiceLastSent'),
                    DB::raw('"Bid Acceptance" as bidAcceptanceName'),
                    'evaluationBid.bidAcceptanceAmount',
                    'evaluationBid.bidAcceptanceNumber',
                    DB::raw('NULL as bidAcceptanceSplit'),
                    DB::raw('NULL as projectCompleteName'),
                    DB::raw('NULL as projectCompleteAmount'),
                    DB::raw('NULL as projectCompleteNumber'),
                    'evaluationBid.bidScopeChangeTotal',
                    'evaluationBid.bidScopeChangeNumber',
                    'evaluationBid.bidTotal',
                    'evaluationBid.bidFirstSent',
                    'evaluationBid.bidAccepted',
                    'evaluation.evaluationDescription',
                    'evaluation.customEvaluation',
                    'evaluationBid.invoicePaidAccept',
                    DB::raw('0 as invoicePaidComplete')
                ])
                ->join('evaluation', 'evaluation.projectID', '=', 'project.projectID')
                ->join('evaluationBid', 'evaluationBid.evaluationID', '=', 'evaluation.evaluationID')
                ->where('customer.customerUUID', Uuid::fromString($customerUUID)->getBytes())
                ->where(function($query) {
                    $query->whereNull('evaluationBid.invoicePaidAccept')
                          ->orWhere('evaluationBid.invoicePaidAccept', 0);
                })
                ->whereNotNull('evaluationBid.bidAcceptanceAmount')
                ->where('evaluationBid.bidAcceptanceAmount', '>', 0)
                ->whereNull('project.deletedAt')
                ->whereNull('property.deletedAt')
                ->whereNull('customer.deletedAt')
                ->whereNull('evaluation.deletedAt')
                ->get();

            $allInvoices = $allInvoices->merge($bidAcceptanceInvoices);

            // Type 5: Project Complete - from evaluationBid table
            $projectCompleteInvoices = $projectResource->newScopedQuery()
                ->ofCompany($customer->companyID)
                ->select([
                    DB::raw('5 as invoiceType'),
                    'evaluation.evaluationID',
                    DB::raw('NULL as invoiceSort'),
                    DB::raw('"Project Complete" as invoiceName'),
                    DB::raw('NULL as invoiceSplit'),
                    'evaluationBid.projectCompleteAmount as invoiceAmount',
                    'evaluationBid.projectCompleteNumber as invoiceNumber',
                    'evaluationBid.invoicePaidComplete as invoicePaid',
                    DB::raw('NULL as invoiceLastSent'),
                    DB::raw('NULL as bidAcceptanceName'),
                    DB::raw('NULL as bidAcceptanceAmount'),
                    DB::raw('NULL as bidAcceptanceNumber'),
                    DB::raw('NULL as bidAcceptanceSplit'),
                    DB::raw('"Project Complete" as projectCompleteName'),
                    'evaluationBid.projectCompleteAmount',
                    'evaluationBid.projectCompleteNumber',
                    'evaluationBid.bidScopeChangeTotal',
                    'evaluationBid.bidScopeChangeNumber',
                    'evaluationBid.bidTotal',
                    'evaluationBid.bidFirstSent',
                    'evaluationBid.bidAccepted',
                    'evaluation.evaluationDescription',
                    'evaluation.customEvaluation',
                    DB::raw('0 as invoicePaidAccept'),
                    'evaluationBid.invoicePaidComplete'
                ])
                ->join('evaluation', 'evaluation.projectID', '=', 'project.projectID')
                ->join('evaluationBid', 'evaluationBid.evaluationID', '=', 'evaluation.evaluationID')
                ->where('customer.customerUUID', Uuid::fromString($customerUUID)->getBytes())
                ->where(function($query) {
                    $query->whereNull('evaluationBid.invoicePaidComplete')
                          ->orWhere('evaluationBid.invoicePaidComplete', 0);
                })
                ->whereNotNull('evaluationBid.projectCompleteAmount')
                ->where('evaluationBid.projectCompleteAmount', '>', 0)
                ->whereNull('project.deletedAt')
                ->whereNull('property.deletedAt')
                ->whereNull('customer.deletedAt')
                ->whereNull('evaluation.deletedAt')
                ->get();

            $allInvoices = $allInvoices->merge($projectCompleteInvoices);

            // Type 7: Credit Memo/Invoice - from customBid table (using bidAcceptance and projectComplete data)
            $creditMemoInvoices = $projectResource->newScopedQuery()
                ->ofCompany($customer->companyID)
                ->select([
                    DB::raw('7 as invoiceType'),
                    'evaluation.evaluationID',
                    DB::raw('NULL as invoiceSort'),
                    DB::raw('"Custom Bid" as invoiceName'),
                    DB::raw('NULL as invoiceSplit'),
                    'customBid.bidTotal as invoiceAmount',
                    'customBid.bidAcceptanceNumber as invoiceNumber',
                    DB::raw('CASE WHEN customBid.invoicePaidAccept = 1 AND customBid.invoicePaidComplete = 1 THEN 1 ELSE 0 END as invoicePaid'),
                    'customBid.bidLastSent as invoiceLastSent',
                    'customBid.bidAcceptanceName',
                    'customBid.bidAcceptanceAmount',
                    'customBid.bidAcceptanceNumber',
                    'customBid.bidAcceptanceSplit',
                    'customBid.projectCompleteName',
                    'customBid.projectCompleteAmount',
                    'customBid.projectCompleteNumber',
                    'customBid.bidScopeChangeTotal',
                    'customBid.bidScopeChangeNumber',
                    'customBid.bidTotal',
                    'customBid.bidFirstSent',
                    'customBid.bidAccepted',
                    'evaluation.evaluationDescription',
                    'evaluation.customEvaluation',
                    'customBid.invoicePaidAccept',
                    'customBid.invoicePaidComplete'
                ])
                ->join('evaluation', 'evaluation.projectID', '=', 'project.projectID')
                ->join('customBid', 'customBid.evaluationID', '=', 'evaluation.evaluationID')
                ->where('customer.customerUUID', Uuid::fromString($customerUUID)->getBytes())
                ->where(function($query) {
                    $query->where(function($subQuery) {
                        // Either bid acceptance is unpaid
                        $subQuery->whereNotNull('customBid.bidAcceptanceAmount')
                                ->where('customBid.bidAcceptanceAmount', '>', 0)
                                ->where(function($acceptQuery) {
                                    $acceptQuery->whereNull('customBid.invoicePaidAccept')
                                              ->orWhere('customBid.invoicePaidAccept', 0);
                                });
                    })->orWhere(function($subQuery) {
                        // Or project complete is unpaid
                        $subQuery->whereNotNull('customBid.projectCompleteAmount')
                                ->where('customBid.projectCompleteAmount', '>', 0)
                                ->where(function($completeQuery) {
                                    $completeQuery->whereNull('customBid.invoicePaidComplete')
                                                ->orWhere('customBid.invoicePaidComplete', 0);
                                });
                    });
                })
                ->whereNull('project.deletedAt')
                ->whereNull('property.deletedAt')
                ->whereNull('customer.deletedAt')
                ->whereNull('evaluation.deletedAt')
                ->get();

            $allInvoices = $allInvoices->merge($creditMemoInvoices);

            $formattedInvoices = $allInvoices->map(function ($invoice) {
                return [
                    'invoiceType' => $invoice->invoiceType,
                    'evaluationId' => $invoice->evaluationID,
                    'invoiceSort' => $invoice->invoiceSort,
                    'invoiceName' => $invoice->invoiceName,
                    'invoiceSplit' => $invoice->invoiceSplit,
                    'invoiceAmount' => $invoice->invoiceAmount,
                    'invoiceNumber' => $invoice->invoiceNumber,
                    'invoicePaid' => (bool) $invoice->invoicePaid,
                    'invoicePaidAccept' => (bool) $invoice->invoicePaidAccept,
                    'invoicePaidComplete' => (bool) $invoice->invoicePaidComplete,
                    'invoiceLastSent' => $invoice->invoiceLastSent,
                    'bidAcceptanceName' => $invoice->bidAcceptanceName,
                    'bidAcceptanceAmount' => $invoice->bidAcceptanceAmount,
                    'bidAcceptanceNumber' => $invoice->bidAcceptanceNumber,
                    'bidAcceptanceSplit' => $invoice->bidAcceptanceSplit,
                    'projectCompleteName' => $invoice->projectCompleteName,
                    'projectCompleteAmount' => $invoice->projectCompleteAmount,
                    'projectCompleteNumber' => $invoice->projectCompleteNumber,
                    'bidScopeChangeTotal' => $invoice->bidScopeChangeTotal,
                    'bidScopeChangeNumber' => $invoice->bidScopeChangeNumber,
                    'bidTotal' => $invoice->bidTotal,
                    'bidFirstSent' => $invoice->bidFirstSent,
                    'bidAccepted' => $invoice->bidAccepted,
                    'evaluation' => [
                        'description' => $invoice->evaluationDescription,
                        'customEvaluation' => $invoice->customEvaluation
                    ],
                    'remainingBalance' => $this->calculateRemainingBalance($invoice)
                ];
            });

            return Response::json([
                'success' => true,
                'data' => $formattedInvoices,
                'count' => $formattedInvoices->count()
            ]);

        } catch (\Exception $e) {
            return Response::json([
                'success' => false,
                'error' => 'Unable to fetch open invoices'
            ], 500);
        }
    }

    /**
     * Calculate remaining balance for an invoice
     *
     * @param object $invoice
     * @return float|null
     */
    private function calculateRemainingBalance($invoice)
    {
        if ($invoice->invoicePaid) {
            return 0;
        }

        if (is_numeric($invoice->bidTotal) && is_numeric($invoice->bidAcceptanceAmount)) {
            return $invoice->bidTotal - $invoice->bidAcceptanceAmount;
        }

        if (is_numeric($invoice->bidTotal) && is_numeric($invoice->projectCompleteAmount)) {
            return $invoice->bidTotal - $invoice->projectCompleteAmount;
        }

        return null;
    }

    /**
     * Get open bids for a customer
     *
     * @param string $customerUUID
     * @return \Core\Components\Http\Responses\JSONResponse
     * @throws HttpResponseException
     */
    public function getOpenBids($customerUUID)
    {
        // Validate UUID format
        if (!$this->isValidUuid($customerUUID)) {
            throw new HttpResponseException(404);
        }

        try {
            // Find customer by UUID using the reusable method
            $customer = $this->findCustomerByUuid($customerUUID);

            if (!$customer) {
                throw new HttpResponseException(404);
            }

            $acl = Acl::make()->setCompanyID($customer->companyID);
            $bidItemResource = ItemResource::make($acl);

            // Select this from customBid Model (not Resource)
            $openBids = $bidItemResource->newScopedQuery()
                ->ofCompany($customer->companyID)
                ->select([
                    'bidItems.bidItemID',
                    'bidItems.referenceID',
                    'bidItems.status',
                    'bidItems.total',
                    'bidItems.submittedAt',
                    'bidItems.finalizedAt',
                    'bidItems.updatedAt',
                    'bidItems.cancelledAt',
                    'bidItems.acceptedAt',
                    'bidItems.lockedAt',
                    'bidItems.createdAt',
                    'project.projectID',
                    'project.projectDescription',
                    'property.address',
                    'property.address2',
                    'property.city',
                    'property.state',
                    'property.zip'
                ])
                ->where('customer.customerUUID', Uuid::fromString($customerUUID)->getBytes())
                ->where('bidItems.status', '=', ItemResource::STATUS_FINALIZED)
                ->whereNull('bidItems.deletedAt')
                ->whereNull('project.deletedAt')
                ->whereNull('property.deletedAt')
                ->whereNull('customer.deletedAt')
                ->orderBy('bidItems.createdAt', 'desc')
                ->get();

            // Format the response
            $formattedBids = $openBids->map(function ($bid) {
                return [
                    'bidItemID' => bin2hex($bid->bidItemID),
                    'referenceID' => $bid->referenceID,
                    'status' => $bid->status,
                    'status_summary' => $this->getBidSummaryStatus($bid),
                    'statusName' => $this->getBidStatusName($bid->status),
                    'total' => $bid->total ? (float) $bid->total : null,
                    'dates' => [
                        'createdAt' => $bid->createdAt,
                        'updatedAt' => $bid->updatedAt,
                        'submittedAt' => $bid->submittedAt,
                        'acceptedAt' => $bid->acceptedAt,
                        'finalizedAt' => $bid->finalizedAt,
                        'cancelledAt' => $bid->cancelledAt,
                        'lockedAt' => $bid->lockedAt
                    ],
                    'project' => [
                        'projectID' => $bid->projectID,
                        'description' => $bid->projectDescription,
                        'property' => [
                            'address' => $bid->address,
                            'address2' => $bid->address2,
                            'city' => $bid->city,
                            'state' => $bid->state,
                            'zip' => $bid->zip
                        ]
                    ]
                ];
            });

            return Response::json([
                'success' => true,
                'data' => $formattedBids,
                'count' => $formattedBids->count()
            ]);

        } catch (\Exception $e) {
            return Response::json([
                'success' => false,
                'error' => 'Unable to fetch open bids'
            ], 500);
        }
    }

    /**
     * Get human-readable status name for bid status
     *
     * @param int $status
     * @return string
     */
    private function getBidStatusName($status)
    {
        $statusNames = [
            ItemResource::STATUS_INCOMPLETE => 'Incomplete',
            ItemResource::STATUS_SUBMITTED => 'Submitted',
            ItemResource::STATUS_FINALIZED => 'Finalized',
            ItemResource::STATUS_ACCEPTED => 'Accepted',
            ItemResource::STATUS_CANCELLED => 'Cancelled',
            ItemResource::STATUS_EXPIRED => 'Expired'
        ];

        return $statusNames[$status] ?? 'Unknown';
    }

    /**
     * Get bid summary status with respective date
     * @param $bidItem
     * @return array
     */
    private function getBidSummaryStatus($bidItem) {
        $date = null;
        switch ($bidItem->status) {
            case ItemResource::STATUS_INCOMPLETE:
                $status = 'Incomplete';
                break;
            case ItemResource::STATUS_SUBMITTED:
                $status = 'Submitted';
                $date = $bidItem->submittedAt;
                break;
            case ItemResource::STATUS_FINALIZED:
                $status = 'Finalized';
                $date = $bidItem->finalizedAt;
                break;
            case ItemResource::STATUS_ACCEPTED:
                $status = 'Accepted';
                $date = $bidItem->acceptedAt;
                break;
            case ItemResource::STATUS_CANCELLED:
                $status = 'Cancelled';
                $date = $bidItem->cancelledAt;
                break;
            case ItemResource::STATUS_EXPIRED:
                $status = 'Expired';
                $date = $bidItem->expiredAt;
                break;
        }

        return [
            'status' => $status,
            'date' => $date
        ];
    }

    /**
     * Get recent image uploads for projects associated with a customer
     *
     * @param string $customerUUID
     * @return \Core\Components\Http\Responses\JSONResponse
     * @throws HttpResponseException
     */
    public function getRecentImages($customerUUID)
    {
        // Validate UUID format
        if (!$this->isValidUuid($customerUUID)) {
            throw new HttpResponseException(404);
        }

        try {
            $customer = $this->findCustomerByUuid($customerUUID);

            if (!$customer) {
                throw new HttpResponseException(404);
            }

            $imageContentTypes = [
                'image/jpeg',
                'image/jpg',
                'image/png',
                'image/gif',
                'image/webp',
                'image/bmp',
                'image/tiff',
                'image/svg+xml'
            ];

            $acl = Acl::make()->setCompanyID($customer->companyID);
            $projectFileResource = ProjectFileResource::make($acl);

            $projectFiles = $projectFileResource->newScopedQuery()
                ->join('files', 'files.fileID', '=', 'projectFiles.fileID')
                ->select([
                    'projectFiles.projectFileID',
                    'projectFiles.name',
                    'files.fileID',
                    'files.contentType',
                    'files.size',
                    'files.createdAt',
                    'files.type',
                    DB::raw("'project' as source"),
                    DB::raw('NULL as propertyID'),
                    DB::raw('NULL as address'),
                    DB::raw('NULL as city'),
                    DB::raw('NULL as state'),
                    'project.projectID',
                    'project.projectDescription'
                ])
                ->where('customer.customerUUID', Uuid::fromString($customerUUID)->getBytes())
                ->where('files.type', FileResource::TYPE_PROJECT_FILE)
                ->whereIn('files.contentType', $imageContentTypes)
                ->where('files.status', FileResource::STATUS_FINISHED)
                ->whereNull('files.deletedAt')
                ->whereNull('projectFiles.deletedAt')
                ->whereNull('project.deletedAt')
                ->whereNull('property.deletedAt')
                ->whereNull('customer.deletedAt')
                ->orderBy('files.createdAt', 'desc')
                ->get();

            $formattedImages = $projectFiles->map(function ($image) use ($acl, $customer) {
                $projectFileResource = \App\Resources\Project\FileResource::make($acl);
                $projectFileResource->setMediaCompanyID($customer->companyID);
                $imageUrl = $projectFileResource->getMedia()->get('file')->getVariant('original')->getUrl(bin2hex($image->projectFileID))->csm()->build();


                return [
                    'projectFileID' => bin2hex($image->projectFileID),
                    'fileID' => bin2hex($image->fileID),
                    'name' => $image->name,
                    'contentType' => $image->contentType,
                    'url' => $imageUrl,
                    'size' => $image->size,
                    'createdAt' => $image->createdAt,
                    'type' => $image->type,
                    'typeName' => $this->getFileTypeName($image->type),
                    'source' => $image->source,
                    'property' => $image->propertyID ? [
                        'propertyID' => $image->propertyID,
                        'address' => $image->address,
                        'city' => $image->city,
                        'state' => $image->state
                    ] : null,
                    'project' => $image->projectID ? [
                        'projectID' => $image->projectID,
                        'description' => $image->projectDescription
                    ] : null
                ];
            });

            return Response::json([
                'success' => true,
                'data' => $formattedImages,
                'count' => $formattedImages->count()
            ]);

        } catch (\Exception $e) {
            return Response::json([
                'success' => false,
                'error' => 'Unable to fetch recent images'
            ], 500);
        }
    }

    /**
     * Get human-readable file type name
     *
     * @param int $type
     * @return string
     */
    private function getFileTypeName($type)
    {
        $typeNames = [
            FileResource::TYPE_DRAWING => 'Drawing',
            FileResource::TYPE_REPAIR_PLAN => 'Repair Plan',
            FileResource::TYPE_USER_IMAGE => 'User Image',
            FileResource::TYPE_COMPANY_LOGO => 'Company Logo',
            FileResource::TYPE_FORM_UPLOAD => 'Form Upload',
            FileResource::TYPE_BID => 'Bid',
            FileResource::TYPE_SCOPE_OF_WORK => 'Scope of Work',
            FileResource::TYPE_BID_CUSTOM_DRAWING => 'Bid Custom Drawing',
            FileResource::TYPE_MEDIA => 'Media',
            FileResource::TYPE_INVOICE_STATEMENT => 'Invoice Statement',
            FileResource::TYPE_PROPERTY_IMAGE => 'Property Image',
            FileResource::TYPE_PROJECT_FILE => 'Project File',
            FileResource::TYPE_CUSTOM_REPORT_RESULT => 'Custom Report Result'
        ];

        return $typeNames[$type] ?? 'Unknown';
    }
}
