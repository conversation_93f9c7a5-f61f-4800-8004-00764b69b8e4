<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Classes\{Log, Security};
use App\Exceptions\{Api\UnprocessableEntityException, MessageException};
use App\Services\{AuthService, DomainService, TrainingService};
use Carbon\Carbon;
use Common\Models\{Company, User, UserAccessToken};
use Core\Components\Auth\Classes\Auth;
use Core\Components\Http\Classes\URLBuilder;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\Responses\{RedirectResponse, ViewResponse};
use Core\Components\Http\StaticAccessors\{Response, View};
use Core\Components\RequestValidation\Classes\Validation;
use Core\StaticAccessors\App;
use Monolog\Logger;
use Throwable;

/**
 * Class AuthController
 *
 * @package App\Http\Controllers
 */
class AuthController
{
    /**
     * @var Logger|null
     */
    protected ?Logger $logger = null;

    /**
     * AuthController constructor
     *
     * @param Auth $auth
     */
    public function __construct(protected Auth $auth)
    {}

    /**
     * Get logger
     *
     * @return Logger
     */
    protected function getLog(): Logger
    {
        if ($this->logger === null) {
            $this->logger = Log::create('auth', [
                'email' => [
                    'subject' => 'Auth'
                ],
                'slack' => [
                    'username' => 'auth'
                ],
                'file' => 'auth.log',
                // ignore input since it can contain sensitive data
                'app_processor' => function (Log\AppProcessor $processor) {
                    return $processor->withInput(false);
                }
            ]);
        }
        return $this->logger;
    }

    /**
     * Show auth module
     *
     * @return ViewResponse
     */
    public function handle(): ViewResponse
    {
        $view = View::fetch('pages.auth')->share('legacy_assets', false);
        return Response::view($view);
    }

    /**
     * Redirect user to proper location based on session data
     *
     * @param RequestInterface $request
     * @param User $user
     * @return URLBuilder
     * @throws \Core\Components\Http\Exceptions\URIException
     */
    protected function getRedirectUrl(RequestInterface $request, User $user): URLBuilder
    {
        if (TrainingService::isUserInTraining($user)) {
            $return_url = $request->uri()->route('page.app.training', ['path' => '']);
        } else {
            $session = $request->session();
            if (($return_url = $session->get('auth.return_url')) !== null) {
                $session->remove('auth.return_url');
                $return_url = URLBuilder::fromString($return_url);
            } else {
                $return_url = $request->uri()->create()->path('/');
            }
        }
        // if the current domain isn't the same as the primary domain for this user's company reseller primary domain
        // then transfer session to the primary
        /** @var DomainService $domain_service */
        $domain_service = App::get(DomainService::class);
        if ($user->systemPrimaryDomain !== $domain_service->current()->domain) {
            $return_url = $request->uri()->route('page.auth.transfer')->host($user->systemPrimaryDomain)->query([
                'id' => session_id(),
                'path' => $return_url->build(URLBuilder::PART_NO_HOST)
            ]);
        }
        return $return_url;
    }

    /**
     * Process login data and determine if credentials are valid
     *
     * @param RequestInterface $request
     * @param Validation $validation
     * @return \Core\Components\Http\Classes\Response
     * @throws \Core\Components\Http\Exceptions\URIException
     * @throws \Core\Components\Validation\Exceptions\ValidationException
     */
    public function handleLogin(RequestInterface $request, Validation $validation): \Core\Components\Http\Classes\Response
    {
        $validation->config([
            'email' => [
                'label' => 'Email',
                'rules' => 'trim|required|email'
            ],
            'password' => [
                'label' => 'Password',
                'rules' => 'trim|required'
            ]
        ]);
        $validator = $validation->run();
        $errors = $validator->errors();
        if ($validator->valid()) {
            $user = User::withSystemData(true)->where('userEmail', $validator->data('email'))->first();
            if (
                $user === null ||
                $user->userActive !== '1' ||
                $user->userPassword === null ||
                !Security::verifyPassword($validator->data('password'), $user->userPassword)
            ) {
                $errors->add('auth', 'Email and/or password incorrect');
            } elseif (
                !in_array((int) $user->companyStatus, [Company::STATUS_SIGNUP, Company::STATUS_TRIAL, Company::STATUS_ACTIVE]) &&
                !$user->primary
            ) {
                $errors->add('auth', 'Account is inactive');
            }
        }
        if ($errors->count() > 0) {
            throw UnprocessableEntityException::fromValidator($validator);
        }
        (new AuthService())->loginUser($user, $request);

        $location = $this->getRedirectUrl($request, $user)->build();
        return Response::create('', 204)->header('Location', $location);
    }

    /**
     * Log user in via access token
     *
     * @param string $token
     * @param RequestInterface $request
     * @return RedirectResponse
     */
    public function accessToken(string $token, RequestInterface $request): RedirectResponse
    {
        try {
            if (
                !URLBuilder::verifyCsm($request->uri()->current()) ||
                ($access_token = UserAccessToken::where('token', hex2bin($token))->active()->first()) === null
            ) {
                throw new MessageException('Link is invalid');
            }
            /** @var UserAccessToken $access_token */
            $now = Carbon::now('UTC');
            if ($access_token->expiresAt !== null && $access_token->expiresAt->lt($now)) {
                throw new MessageException('Link has expired');
            }
            $user = User::withSystemData(true)->whereKey($access_token->userID)->first();
            if (
                $user === null ||
                $user->userActive !== '1' ||
                (!in_array((int) $user->companyStatus, [Company::STATUS_SIGNUP, Company::STATUS_TRIAL, Company::STATUS_ACTIVE]))
            ) {
                throw new MessageException('Link is invalid');
            }
            (new AuthService())->loginUser($user, $request);

            $access_token->lastLoginAt = $now;
            $access_token->save();

            if ($user->isUserInvited) {
                return Response::redirect()->toRoute('page.app.set-user-details', null, function (URLBuilder $uri) use ($access_token) {
                    return $uri->query([
                       'access_token' => $access_token->getUuidKey()->toString()
                    ])->csm();
                });
            } else if ($user->userPassword === null) {
                return Response::redirect()->toRoute('page.app.set-password', null, function (URLBuilder $uri) use ($access_token) {
                    return $uri->query([
                        'access_token' => $access_token->getUuidKey()->toString()
                    ])->csm();
                });
            }

            return Response::redirect()->toURL($this->getRedirectUrl($request, $user)->build());
        } catch (Throwable $e) {
            // not great practice to use exceptions for flow control, but it's cleaner
            $message = $e instanceof MessageException ? $e->getMessage() : 'Access token is not valid';
            $request->session()->setFlash('auth_message', [
                'type' => 'alert',
                'message' => $message
            ]);
            if (!($e instanceof MessageException)) {
                $this->getLog()->error('Access token is not valid', [
                    'exception' => $e
                ]);
            }
            return Response::redirect()->toRoute('page.auth.login');
        }
    }

    /**
     * Transfer session between domains
     *
     * @param RequestInterface $request
     * @return RedirectResponse
     */
    public function transfer(RequestInterface $request): RedirectResponse
    {
        if (
            ($session_id = $request->get('id', '')) === '' ||
            preg_match('#^[-,a-zA-Z0-9]{1,128}$#', $session_id) !== 1
        ) {
            return Response::redirect()->toRoute('page.auth.login');
        }
        // set session id from query string
        session_id($session_id);

        // start session so PHP sends cookie
        // @todo use better session management instead of default PHP handlers and abstract this to session component
        session_start();

        return Response::redirect()->toURL($request->uri()->create()->path($request->get('path', '/'))->build());
    }

    /**
     * Return no content response to show app is reachable
     *
     * @param RequestInterface $request
     */
    public function ping(RequestInterface $request): \Core\Components\Http\Classes\Response
    {
        $session = $request->session();
        if (!$session->get('userID')) {
            return Response::create('', 403);
        }
        return Response::create('', 204);
    }

    /**
     * Destroy session data to log user out of system
     *
     * @param RequestInterface $request
     * @return RedirectResponse
     */
    public function logout(RequestInterface $request): RedirectResponse
    {
        (new AuthService())->logoutUser($request);
        $request->session()->setFlash('auth_message', [
            'type' => 'success',
            'message' => 'You have been logged out successfully'
        ]);
        return Response::redirect()->toRoute('page.auth.login');
    }
}
