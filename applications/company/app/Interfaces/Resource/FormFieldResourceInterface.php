<?php

namespace App\Interfaces\Resource;

use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Requests\UpdateOrCreateRequest;

/**
 * Interface FormFieldResourceInterface
 *
 * @package App\Interfaces\Resource
 */
interface FormFieldResourceInterface
{
    public const TYPE_TEXT = 1;
    public const TYPE_TEXTAREA = 2;
    public const TYPE_SELECT = 3;
    public const TYPE_RADIO = 4;
    public const TYPE_CHECKBOX = 5;
    public const TYPE_FILE = 6;
    public const TYPE_PRODUCT_LIST = 7;

    /**
     * Update or create entity
     *
     * @param Entity $entity
     * @return UpdateOrCreateRequest
     */
    public function updateOrCreate(Entity $entity): UpdateOrCreateRequest;
}
