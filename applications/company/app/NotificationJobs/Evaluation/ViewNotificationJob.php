<?php

declare(strict_types=1);

namespace App\NotificationJobs\Evaluation;

use App\Attributes\JobAttribute;
use App\Services\Email\Types\Customer\LegacyBidViewType;
use App\Traits\Job\NotificationTrait;
use Common\Models\Customer;
use Common\Models\Evaluation;
use Common\Models\Notification;
use Core\Components\Queue\Classes\Job;
use Core\Components\Queue\Exceptions\JobFailedException;
use Ramsey\Uuid\Uuid;

/**
 * Class ViewNotificationJob
 *
 * @package App\NotificationJobs\Evaluation
 */
#[JobAttribute(type: 16)]
class ViewNotificationJob extends Job
{
    use NotificationTrait;

    /**
     * ViewNotificationJob constructor
     *
     * @param int $evaluation_id
     */
    public function __construct(protected int $evaluation_id)
    {}

    /**
     * Handle notification
     *
     * Send legacy evaluation to customer
     *
     * @return void
     * @throws JobFailedException
     * @throws \Core\Exceptions\AppException
     */
    public function handle(): void
    {
        $evaluation = Evaluation::withProperty()->where<PERSON><PERSON>($this->evaluation_id)
            ->first(['evaluation.evaluationID', 'evaluation.evaluationUUID', 'property.customerID']);
        if ($evaluation === null) {
            throw new JobFailedException('Unable to find evaluation: %d', $this->evaluation_id);
        }

        $customer = Customer::find($evaluation->customerID);
        if ($customer->canEmail()) {
            $notification = $this->createNotification(Notification::TYPE_EVALUATION_VIEW, Uuid::fromBytes($evaluation->evaluationUUID));
            LegacyBidViewType::send([
                'notification_id' => $notification->getUuidKey()->toString()
            ]);
        }

        // send other types of notifications (sms, push, etc.) here
    }
}
