<?php

declare(strict_types=1);

namespace App\NotificationJobs\User;

use App\Attributes\JobAttribute;
use App\Services\Email\Types\User\AccessLinkType;
use App\Traits\Job\NotificationTrait;
use Common\Models\Notification;
use Common\Models\UserAccessToken;
use Core\Components\Queue\Classes\Job;
use Core\Components\Queue\Exceptions\JobFailedException;
use Ramsey\Uuid\UuidInterface;

/**
 * Class AccessLinkNotificationJob
 *
 * @package App\NotificationJobs\User
 */
#[JobAttribute(type: 26)]
class AccessLinkNotificationJob extends Job
{
    use NotificationTrait;

    /**
     * AccessLinkNotificationJob constructor
     *
     * @param UuidInterface $user_access_token_id
     */
    public function __construct(protected UuidInterface $user_access_token_id)
    {}

    /**
     * Handle access link notification
     *
     * Send email to user with info to login into site via link. This usually accompanies setting their initial password
     * or resetting if administrator forced it.
     *
     * @return void
     * @throws JobFailedException
     * @throws \Core\Exceptions\AppException
     */
    public function handle()
    {
        if (($access_token = UserAccessToken::withTrashed()->whereKey($this->user_access_token_id->getBytes())->first()) === null) {
            throw new JobFailedException('Unable to find user access token: %s', $this->user_access_token_id->toString());
        }

        $notification = $this->createNotification(Notification::TYPE_USER_ACCESS_LINK, $access_token->getUuidKey());

        AccessLinkType::send([
            'notification_id' => $notification->getUuidKey()->toString()
        ]);

        // send other types of notifications (sms, push, etc.) here
    }
}
