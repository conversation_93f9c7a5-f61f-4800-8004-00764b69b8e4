<?php

declare(strict_types=1);

namespace App\NotificationJobs\User;

use App\Attributes\JobAttribute;
use App\Services\AppNotification\Types\User\BidAcceptedType as AppNotificationBidAccepted;
use App\Services\Email\Types\User\LegacyBidAcceptedType;
use App\Services\UserSettingService;
use App\Traits\Job\NotificationTrait;
use Common\Models\Evaluation;
use Common\Models\Notification;
use Core\Components\Queue\Classes\Job;
use Core\Components\Queue\Exceptions\JobFailedException;
use Ramsey\Uuid\Uuid;

/**
 * Class BidAcceptedNotificationJob
 *
 * @package App\NotificationJobs\Bid\Item
 */
#[JobAttribute(type: 58)]
class BidAcceptedNotificationJob extends Job
{
    use NotificationTrait;

    /**
     * @param int $evaluation_id
     */
    public function __construct(protected int $evaluation_id)
    {
    }

    /**
     * Handle notification
     *
     * @return void
     * @throws \Core\Exceptions\AppException
     * @throws JobFailedException
     */
    public function handle(): void
    {
        $errors = [];
        $evaluation_item = $this->fetchEvaluationItem();

        $user_id = $evaluation_item->projectSalesperson ?? $evaluation_item->evaluationCreatedByID;
        $setting_service = new UserSettingService($user_id);

        if ($setting_service->get('email_notification_bid_accepted', false)) {
            try {
                $notification = $this->createNotification(
                    Notification::TYPE_USER_BID_ACCEPTED_NOTIFICATION,
                    UUID::fromBytes($evaluation_item['evaluationUUID']),
                );

                LegacyBidAcceptedType::send([
                    'evaluation_id' => $evaluation_item['evaluationID'],
                    'evaluation_uuid' => $notification->getUuidKey()->toString(),
                ]);
            } catch (\Throwable $e) {
                $errors[] = "Email notification failed [User: {$user_id}]: {$e->getMessage()}";
            }
        }

        if ($setting_service->get('app_notification_bid_accepted', false)) {
            try {
                AppNotificationBidAccepted::create($evaluation_item);
            } catch (\Throwable $e) {
                $errors[] = "App notification failed [User: {$user_id}]: {$e->getMessage()}";
            }
        }

        if (!empty($errors)) {
            throw new JobFailedException(
                'BidAcceptedNotificationJob encountered issues: ' . implode(' | ', $errors)
            );
        }
    }

    /**
     * Fetch the Evaluation (new or legacy) from the DB.
     *
     * @return Evaluation
     * @throws JobFailedException
     */
    private function fetchEvaluationItem(): Evaluation
    {
        $evaluation_item = Evaluation::query()
            ->withBidItem()
            ->withCustomer()
            ->whereKey($this->evaluation_id)
            ->first([
                'bidItems.*',
                'customBid.bidNumber as bidReferenceNumber',
                'customBid.bidTotal as bidTotal',
                'customer.companyID as companyID',
                'project.projectID as project_id',
                'project.projectSalesperson as projectSalesperson',
                'evaluation.evaluationFinalizedByID as evaluationCreatedByID',
                'evaluation.evaluationID as evaluationID',
                'evaluation.evaluationUUID as evaluationUUID',
                'evaluation.evaluationDescription as description'
            ]);

        if ($evaluation_item === null) {
            throw new JobFailedException(
                sprintf('Unable to find bid for ID: %d', $this->evaluation_id)
            );
        }

        return $evaluation_item;
    }
}