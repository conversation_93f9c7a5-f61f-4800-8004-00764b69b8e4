<?php

declare(strict_types=1);

namespace App\NotificationJobs\User;

use App\Attributes\JobAttribute;
use App\Services\Email\Exceptions\TypeException;
use App\Services\Email\Types\User\TaskAssignedType;
use App\Services\UserSettingService;
use App\Traits\Job\NotificationTrait;
use Common\Models\Notification;
use Common\Models\Task;
use Core\Components\Queue\Classes\Job;
use Core\Components\Queue\Exceptions\JobFailedException;
use Ramsey\Uuid\UuidInterface;
use App\Services\AppNotification\Types\User\TaskAssignedType as AppNotificationTaskAssigned;

/**
 * Class TaskAssignmentNotificationJob
 *
 * @package App\NotificationJobs\User
 */
#[JobAttribute(type: 50)]
class TaskAssignmentNotificationJob extends Job
{
    use NotificationTrait;

    /**
     * TaskAssignmentNotificationJob constructor
     *
     * @param UuidInterface $task_id
     */
    public function __construct(protected UuidInterface $task_id)
    {}

    /**
     * Handle task assignment notification
     *
     * Send notification to user when a task has been created and assigned to them
     *
     * @return void
     * @throws JobFailedException
     * @throws \Core\Exceptions\AppException
     */
    public function handle(): void
    {
        $errors = [];
        $task = Task::whereKey($this->task_id->getBytes())
            ->first(['taskID', 'assignedToUserID', 'title', 'companyID']);
        if ($task === null) {
            throw new JobFailedException('Unable to find task: %d', $this->task_id);
        }

        $task_id = $this->task_id;
        $setting_service = new UserSettingService($task->assignedToUserID);


        if ($setting_service->get('email_notification_task_assigned', false)) {
            try {
                $notification = $this->createNotification(Notification::TYPE_TASK_ASSIGNMENT_NOTIFICATION, $task->getUuidKey());

                TaskAssignedType::send([
                    'notification_id' => $notification->getUuidKey()->toString()
                ]);
            } catch(TypeException $e) {
                $errors[] = "Email notification failed [Task: {$task_id}]: ".$e->getMessage();
            }
        }


        if ($setting_service->get('app_notification_task_assigned', false)) {
            try {
                AppNotificationTaskAssigned::create($task);
            } catch (\Throwable $e) {
                $errors[] = "App notification failed [Task: {$task_id}]: ".$e->getMessage();
            }
        };

        if (!empty($errors)) {
            throw new JobFailedException(
                'TaskAssignmentNotificationJob encountered issues: ' . implode(' | ', $errors)
            );
        }
    }
}
