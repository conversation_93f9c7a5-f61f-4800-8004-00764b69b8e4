<?php

declare(strict_types=1);

namespace App\ResourceDelegates\Company\Form\Item\Field;

use App\Resources\Company\Form\Item\{Field\OptionResource, FieldResource};
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Core\Components\Resource\Classes\{Entity, Field, FieldList, RelationList, Scope};
use Core\Components\Validation\Classes\Rules;
use Ramsey\Uuid\UuidInterface;

/**
 * Class OptionDelegate
 *
 * @package App\ResourceDelegates\Company\Form\Item\Field
 */
class OptionDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    /**
     * Build relation list shared by all resource instances
     *
     * @param RelationList $list
     * @return RelationList
     */
    public function buildRelations(RelationList $list): RelationList
    {
        $list->oneOrMany('field')->resource(FieldResource::class);

        return $list;
    }

    /**
     * Build field list shared by all resource instances
     *
     * @param FieldList $list
     * @return FieldList
     */
    public function buildFields(FieldList $list): FieldList
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('companyFormItemFieldOptionID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([OptionResource::ACTION_CREATE, OptionResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $list->field('field_id')
            ->typeUuid()
            ->column('companyFormItemFieldID', true)
            ->validation('Field Id', 'required|uuid|check_field_id')
            ->onAction(OptionResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $list->field('alias')
            ->validation('Alias', 'nullable|optional|alias');

        $list->field('label')
            ->validation('Label', 'required|max_length[100]');

        $list->field('order')
            ->validation('Order', 'required|type[int]')
            ->onAction([OptionResource::ACTION_CREATE, OptionResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('nullable|optional|type[int]');
            });

        $this->timestampFields($list);

        return $list;
    }

    /**
     * Register custom rules for resource fields
     *
     * @param Rules $rules
     * @param OptionResource $resource
     * @return Rules
     */
    public function validationRules(Rules $rules, OptionResource $resource): Rules
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_field_id', function (UuidInterface $id) use ($resource) {
            if ($resource->relationResource('field')->entityExists($id->toString())) {
                return true;
            }
            return 'check_field_id';
        }, [
            'check_field_id' => 'Unable to find field'
        ]);

        return $rules;
    }

    public function anyCreateValidateAfter(Entity $entity): Entity
    {
        if (!$entity->has('order')) {
            $entity->set('order', 1);
        }
        return $entity;
    }

    public function queryScopeGlobal(object $query, OptionResource $resource): object
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function actionAllowed(int $action, OptionResource $resource): bool
    {
        if (in_array($action, [OptionResource::ACTION_GET_COLLECTION, OptionResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        return false;
    }

    public function scopeBuildBefore(Scope $scope): void
    {
        switch ($scope->getFormat()) {
            case 'form-v1':
                $scope->fields(['id', 'alias', 'label', 'order'], true);
                $scope->query(fn($query) => $query->ordered());
                break;
        }
    }
}
