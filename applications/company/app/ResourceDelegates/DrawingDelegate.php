<?php

declare(strict_types=1);

namespace App\ResourceDelegates;

use App\Classes\Acl;
use App\ResourceMediaHandlers\Drawing\{
    Image\AppEventThumbnailHandler,
    Image\BidThumbnailHandler,
    Image\EvaluationThumbnail<PERSON><PERSON>ler,
    ImageHandler,
    RepairPlanHandler
};
use App\Resources\{Drawing\NodeResource, DrawingResource, FileResource, ProjectResource, UserResource};
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\{TimestampFieldsTrait, TrainingActionTrait};
use Carbon\Carbon;
use Common\Models\{Customer, CustomerPhone, Drawing, Project, Property, TrainingAction};
use Core\Components\Resource\Classes\{
    Collection,
    Entity,
    Field,
    FieldList,
    MediaList,
    MediaType,
    RelationList,
    Request,
    Scope,
    ScopeBuilder
};
use Core\Components\Resource\Exceptions\{
    EntityConflictException,
    ImmutableEntityException,
    ImmutableRelationException,
    ValidationException
};
use Core\Components\Resource\Requests\{CollectionRequest, CreateRequest, DeleteRequest, UpdateRequest};
use Core\Components\Validation\Classes\{FieldConfig, Rules, Validator};
use Ramsey\Uuid\UuidInterface;

class DrawingDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;
    use TrainingActionTrait;

    public function buildRelations(RelationList $list): RelationList
    {
        $list->oneOrMany('created_by_user')->modelRelation('createdByUser')->resource(UserResource::class);
        $list->oneOrMany('image')->resource(FileResource::class);
        $list->oneOrMany('nodes')->resource(NodeResource::class);
        $list->oneOrMany('project')->resource(ProjectResource::class);
        $list->oneOrMany('repair_plan')->modelRelation('repairPlan')->resource(FileResource::class);

        return $list;
    }

    public function buildFields(FieldList $list): FieldList
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('drawingID')
            ->validation('Drawing Id', 'required|uuid')
            ->noSave()
            ->onAction([DrawingResource::ACTION_CREATE, DrawingResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')->save();
            })
            ->onAction(DrawingResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $list->field('version')
            ->immutable()
            ->onAction(DrawingResource::ACTION_FILTER, function (Field $field) {
                return $field->mutable()->validationRules('required|numeric');
            });

        $list->field('status')
            ->validation('Status', 'required|type[int]|in_array[statuses]')
            ->onAction([DrawingResource::ACTION_CREATE, DrawingResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('nullable|optional|type[int]|in_array[statuses]');
            })
            ->onAction(DrawingResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('project_id')
            ->column('projectID', true)
            ->validation('Project Id', 'nullable|required_if[is_finalized]|type[int]|check_project_id')
            ->onAction(DrawingResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('name')
            ->validation('Name', 'trim|required|max_length[100]');

        $list->field('image_file_id')
            ->typeUuid()
            ->column('imageFileID', true)
            ->accessLevelPrivate()
            ->validation('Image File Id', 'nullable|required_if[is_finalized]|uuid|check_image_file_id');

        $list->field('repair_plan_file_id')
            ->typeUuid()
            ->column('repairPlanFileID', true)
            ->accessLevelPrivate()
            ->validation('Repair Plan File Id', 'nullable|optional|uuid|check_repair_plan_file_id');

        $list->field('is_repair_plan_valid')
            ->column('isRepairPlanValid')
            ->accessLevelPrivate()
            ->validation('Is Repair Plan Valid', 'required|type[bool]');

        $list->field('finalized_at')
            ->typeDateTime()
            ->column('finalizedAt')
            ->immutable()
            ->enableAction(DrawingResource::ACTION_SORT);

        $list->field('finalized_by_user_id')
            ->column('finalizedByUserID')
            ->immutable();

        return $list;
    }

    public function buildVersionFields(FieldList $list, int $version): FieldList
    {
        switch ($version) {
            case 1:
                $list->field('zoom_level')
                    ->column('zoomLevel')
                    ->validation('Zoom Level', 'required|numeric');

                $list->field('grid_unit')
                    ->column('gridUnit')
                    ->validation('Grid Unit', 'required|type[int]|in_array[grid_units]');

                $list->field('show_wall_lengths')
                    ->column('showWallLengths')
                    ->validation('Show Wall Lengths', 'required|type[bool]');

                $list->mediaField('image')
                    ->validation('Image', [
                        'mimes' => ['jpeg'],
                        'max_size' => '5MB'
                    ]);

                $list->field('is_locked')
                    ->column('isLocked')
                    ->validation('Is Locked', 'required|type[bool]')
                    ->onAction(DrawingResource::ACTION_FILTER, function (Field $field) {
                        return $field->validationRules('required|cast[bool]');
                    });

                $list->field('locked_at')
                    ->typeDateTime()
                    ->column('lockedAt')
                    ->immutable();

                $list->field('locked_by_user_api_token_id')
                    ->typeUuid()
                    ->column('lockedByUserApiTokenID')
                    ->immutable();
                break;
            case 2:
                $list->field('subversion')
                    ->validationRules('nullable|optional|type[int]|check_subversion');

                $list->mediaField('image')
                    ->validation('Image', [
                        'mimes' => ['svg'],
                        'max_size' => '5MB'
                    ]);

                $list->field('config')
                    ->requireColumn()
                    ->validation('Config', 'required|type[array]'); // @todo add validation to config

                $list->field('started_at')
                    ->column('startedAt')
                    ->typeDateTime()
                    ->immutable()
                    ->onAction(DrawingResource::ACTION_CREATE, function (Field $field) {
                        return $field->mutable()->validation('Started At', 'required|iso8601_date|to_carbon|to_utc');
                    });

                $list->field('last_modified_at')
                    ->column('lastModifiedAt')
                    ->typeDateTime()
                    ->immutable()
                    ->onAction([DrawingResource::ACTION_UPDATE, DrawingResource::ACTION_PARTIAL_UPDATE, DrawingResource::ACTION_DELETE], function (Field $field) {
                        return $field->mutable()->validation('Last Modified At', 'nullable|optional|iso8601_date|to_carbon|to_utc');
                    });

                $list->field('is_deleted')
                    ->onDemand()
                    ->column('deletedAt')
                    ->outputMutator(function ($value) {
                        return $value !== null;
                    })
                    ->immutable();
                break;
        }

        $this->timestampFields($list);

        return $list;
    }

    public function validationFields(FieldList $list): FieldList
    {
        $list->field('nodes')
            ->validation('Nodes', 'nullable|optional|type[array]');

        return $list;
    }

    public function buildMedia(MediaList $list): MediaList
    {
        $list->type('repair_plan')
            ->directoryName('repair-plans', true)
            ->versions(function (MediaType $type) {
                $type->original()->handler(RepairPlanHandler::class);
            });
        $list->type('image')
            ->directoryName('drawing-images', true)
            ->versions(function (MediaType $type) {
                $type->original()->handler(ImageHandler::class);
                $type->variant('app-event-thumbnail')
                    ->directoryName('app-event-thumbnail', true)
                    ->handler(AppEventThumbnailHandler::class);
                $type->variant('bid_thumbnail')
                    ->directoryName('bid-thumbnail', true)
                    ->handler(BidThumbnailHandler::class);
                $type->variant('evaluation_thumbnail')
                    ->directoryName('evaluation-thumbnail', true)
                    ->handler(EvaluationThumbnailHandler::class);
            });
        return $list;
    }

    public function validationRules(Rules $rules, DrawingResource $resource): Rules
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_subversion', function ($version, $param, Validator $validator) {
            if ($version < 1 || $version > DrawingResource::CURRENT_SUBVERSION) {
                return 'version_invalid';
            }
            $model = $validator->getConfig()->storage('_model');
            if ($model !== null && $version < $model->subversion) {
                return 'version_decrease';
            }
            return true;
        }, [
            'version_invalid' => 'Subversion is not valid',
            'version_decrease' => 'Subversion cannot be decreased'
        ]);

        $rules->register('check_project_id', function ($id) use ($resource) {
            $project_resource = $resource->relationResource('project');
            if (($project = $project_resource->find($id)) === null) {
                return 'project_not_found';
            }
            return true;
        }, [
            'project_not_found' => 'Project not found'
        ]);

        $rules->register('check_image_file_id', function (UuidInterface $id, $params, Validator $validator) use ($resource) {
            $image = $resource->relationResource('image');
            if (($file = $image->find($id)) === null) {
                return 'file_not_found';
            }
            if ($file->type !== FileResource::TYPE_DRAWING) {
                return 'file_invalid_type';
            }
            $model = $validator->getConfig()->storage('_model');
            $file_id = $id->getBytes();
            if (($model === null || $model->imageFileID !== $file_id) && Drawing::withTrashed()->where('imageFileID', $file_id)->count() !== 0) {
                return 'file_in_use';
            }
            return true;
        }, [
            'file_not_found' => '{label} does not exist',
            'file_invalid_type' => '{label} is not a drawing image',
            'file_in_use' => '{label} is already in use with another drawing'
        ]);

        $rules->register('check_repair_plan_file_id', function (UuidInterface $id, $params, Validator $validator) use ($resource) {
            $repair_plan = $resource->relationResource('repair_plan');
            if (($file = $repair_plan->find($id)) === null) {
                return 'file_not_found';
            }
            if ($file->type !== FileResource::TYPE_REPAIR_PLAN) {
                return 'file_invalid_type';
            }
            $model = $validator->getConfig()->storage('_model');
            $file_id = $id->getBytes();
            if (($model === null || $model->repairPlanFileID !== $file_id) && Drawing::withTrashed()->where('repairPlanFileID', $file_id)->count() !== 0) {
                return 'file_in_use';
            }
            return true;
        }, [
            'file_not_found' => '{label} does not exist',
            'file_invalid_type' => '{label} is not a repair plan',
            'file_in_use' => '{label} is already in use with another drawing'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config): FieldConfig
    {
        $config->store('statuses', DrawingResource::getStatuses());
        $config->store('grid_units', DrawingResource::getGridUnits());
        $config->store('is_finalized', function ($field, Validator $validator) {
            return $validator->data('status') === DrawingResource::STATUS_FINALIZED;
        });
        return $config;
    }

    /**
     * Update find config for resource request
     *
     * If format is drawing-v2, we ensure deleted drawings can be found since we send them to the app
     *
     * @param array $config
     * @param Entity $entity
     * @param $request
     * @return array
     */
    public function findConfig(array $config, Entity $entity, $request): array
    {
        if ($request->getScope()->getFormat() === 'drawing-v2') {
            $config['query'] = function ($query) {
                return $query->withTrashed();
            };
        }
        return $config;
    }

    public function anyCreateValidateAfter(Entity $entity, CreateRequest $request): Entity
    {
        // if drawing is marked finalized, we check to see if an image file id was assigned to the request. if not,
        // we throw a validation exception since an image is required in this scenario
        if (
            $entity->get('status') === DrawingResource::STATUS_FINALIZED &&
            $request->storage('image_file_id') === null
        ) {
            throw new ValidationException('Image is required to finalize drawing');
        }

        return $entity;
    }

    public function anyUpdateValidateAfter(Entity $entity, UpdateRequest $request): Entity
    {
        $model = $request->getModel();
        if ($model->version === 2) {
            if ($entity->last_modified_at === null) {
                throw new EntityConflictException('No last modification date provided in entity');
            }
            if ($model->lastModifiedAt->gt($entity->last_modified_at)) {
                throw new EntityConflictException('Drawing on server modified after last modification date in entity');
            }
        }
        // if status is being changed to finalized
        if (
            isset($entity['status']) &&
            $entity['status'] !== $model->status &&
            $entity['status'] === DrawingResource::STATUS_FINALIZED
        ) {
            // if no image file id exists and the image isn't being uploaded via a poly relation, then we throw a
            // validation exception
            $poly_request = $request->getParentPolyRequest();
            if ($model->imageFileID === null && ($poly_request === null || $poly_request->getMediaEntity('image') === null)) {
                throw new ValidationException('Image is required to finalize drawing');
            }
        }
        return $entity;
    }

    public function deleteValidateAfter(Entity $entity, DeleteRequest $request): Entity
    {
        $model = $request->getModel();
        if ($model->version === 2 && $model->lastModifiedAt->gt($entity->last_modified_at)) {
            throw new EntityConflictException('Drawing on server modified after last modification date in entity');
        }
        return $entity;
    }

    /**
     * Handle setting of fields related to status
     *
     * @param array $model_data
     * @param CreateRequest|UpdateRequest $request
     * @return array
     */
    protected function handleStatus(array $model_data, $request): array
    {
        switch ($model_data['status']) {
            case DrawingResource::STATUS_IN_PROGRESS:
                $model_data['finalizedAt'] = null;
                $model_data['finalizedByUserID'] = null;
                break;
            case DrawingResource::STATUS_FINALIZED:
                if ($request->resource()->version() === 1) {
                    $model_data['isLocked'] = false;
                    $model_data['lockedAt'] = null;
                    $model_data['lockedByUserApiTokenID'] = null;
                }
                $model_data['finalizedAt'] = Carbon::now('UTC');
                $model_data['finalizedByUserID'] = $request->resource()->acl()->user()->getKey();
                $request->store('is_finalizing', true);
                break;
        }

        return $model_data;
    }

    /**
     * Handle setting of lock related fields
     *
     * @param array $model_data
     * @param CreateRequest|UpdateRequest $request
     * @return array
     */
    protected function handleLocking(array $model_data, $request): array
    {
        if ($model_data['isLocked']) {
            $model_data['lockedAt'] = Carbon::now('UTC');
            $model_data['lockedByUserApiTokenID'] = $request->resource()->acl()->userApiToken()->userApiTokenID;
        } else {
            $model_data['lockedAt'] = null;
            $model_data['lockedByUserApiTokenID'] = null;
        }

        return $model_data;
    }

    public function createModelDataAfter(array $model_data, CreateRequest $request): array
    {
        $model_data['version'] = $request->resource()->version();
        if ($model_data['status'] === null) {
            $model_data['status'] = DrawingResource::STATUS_IN_PROGRESS;
        }
        $model_data = $this->handleStatus($model_data, $request);
        $model_data['imageFileID'] = $request->resource()->getFields()->get('image_file_id')
            ->saveValue($request->storage('image_file_id'));
        if ($model_data['version'] === 1) {
            $model_data = $this->handleLocking($model_data, $request);
        }
        if (!isset($model_data['subversion'])) {
            $model_data['subversion'] = 1;
        }
        $model_data['isRepairPlanValid'] = false;
        $model_data['lastModifiedAt'] = Carbon::now('UTC');

        return $model_data;
    }

    public function anyUpdateModelDataAfter(array $model_data, UpdateRequest $request): array
    {
        $model = $request->getModel();
        $allow_image_modify = true;
        if (isset($model_data['status']) && $model_data['status'] !== $model->status) {
            $model_data = $this->handleStatus($model_data, $request);
        } else {
            $allow_image_modify = $model->status === Drawing::STATUS_IN_PROGRESS;
        }
        // store this for the image media handler to check when adding/updating image via media field system
        $request->store('allow_image_modify', $allow_image_modify);

        if ($model->version === 1 && isset($model_data['isLocked']) && $model_data['isLocked'] !== $model->isLocked) {
            $model_data = $this->handleLocking($model_data, $request);
        }

        if (!isset($model_data['subversion'])) {
            $model_data['subversion'] = 1;
        }
        $model_data['lastModifiedAt'] = Carbon::now('UTC');
        return $model_data;
    }

    public function deleteModelDataAfter(array $model_data): array
    {
        $model_data['lastModifiedAt'] = Carbon::now('UTC');
        return $model_data;
    }

    protected function createNestedNodes(CreateRequest $request): void
    {
        $nodes = $request->getValidatedEntity()->get('nodes', []);
        if (count($nodes) === 0) {
            return;
        }
        try {
            $drawing_id = $request->getFields()->primaryField()->outputValueFromModel($request->getModel());
            $request->resource()->relationResource('nodes')
                ->batchCreate(Collection::fromArray($nodes))
                ->nested()
                ->attach('entity', function (Entity $entity) use ($drawing_id) {
                    $entity->set('drawing_id', $drawing_id);
                    return $entity;
                })
                ->run();
        } catch (ValidationException $e) {
            throw (new ValidationException('Unable to create nodes'))->setErrors([
                'nodes' => $e->getErrors()
            ]);
        }
    }

    public function anyCreateSaveAfter(CreateRequest $request): void
    {
        $this->createNestedNodes($request);

        $this->completeTrainingAction($request, TrainingAction::CREATE_DRAWING);
    }

    protected function updateNestedNodes(UpdateRequest $request): void
    {
        $nodes = $request->getValidatedEntity()->get('nodes', []);
        if (!is_array($nodes)) {
            return;
        }
        try {
            $drawing_id = $request->getFields()->primaryField()->outputValueFromModel($request->getModel());
            /** @var \App\Resources\Drawing\NodeResource $node_resource */
            $node_resource = $request->resource()->relationResource('nodes');
            $ids = [];
            $is_finalizing = $request->storage('is_finalizing', false);
            if (count($nodes) > 0) {
                $ids = $node_resource->batchUpdateOrCreate(Collection::fromArray($nodes))
                    ->nested()
                    ->attach('entity', function (Entity $entity) use ($drawing_id) {
                        $entity->set('drawing_id', $drawing_id);
                        return $entity;
                    })
                    ->attach('request', function ($request) use ($is_finalizing) {
                        // since update or create requests are proxies you have to apply your request specific changes via
                        // another hook
                        $request->attach('request', function ($request) use ($is_finalizing) {
                            if ($request instanceof UpdateRequest) {
                                $request->findConfig(['check_mutability' => !$is_finalizing]);
                            }
                            return $request;
                        });
                        return $request;
                    })
                    ->run();
            }
            $node_resource->deleteMissingNodesByDrawingID($drawing_id, $ids, $is_finalizing);
        } catch (ValidationException $e) {
            throw (new ValidationException('Unable to update nodes'))->setErrors([
                'nodes' => $e->getErrors()
            ]);
        }
    }

    public function anyUpdateSaveAfter(UpdateRequest $request): void
    {
        $this->updateNestedNodes($request);
    }

    public function deleteSaveBefore(DeleteRequest $request): void
    {
        $resource = $request->resource();
        $model = $request->getModel();
        $drawing_id = $request->getFields()->primaryField()->outputValueFromModel($request->getModel());

        try {
            /** @var NodeResource $node_resource */
            $node_resource = $resource->relationResource('nodes');
            $node_resource->deleteByDrawingID($drawing_id, $request->isForced());

            if ($model->imageFileID !== null) {
                /** @var FileResource $image_resource */
                $image_resource = $resource->relationResource('image');
                $image_resource->delete(Entity::make([
                    'id' => $resource->getFields()->get('image_file_id')->outputValueFromModel($model)
                ]))->run();
            }
            if ($model->repairPlanFileID !== null) {
                // delete using no user since file can be generated by job which will cause deletion failures due
                // to missing user info for global query scope
                FileResource::make(Acl::make())->delete(Entity::make([
                    'id' => $resource->getFields()->get('repair_plan_file_id')->outputValueFromModel($model)
                ]))->run();
            }
        } catch (ImmutableEntityException | ImmutableRelationException $e) {
            throw (new ImmutableRelationException('Unable to delete due to immutable relation'))->setLastException($e);
        }
    }

    public function close(Request $request)
    {
        $this->recordCompletedTrainingActions($request);
    }

    public function queryScopeGlobal(object $query, DrawingResource $resource): object
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function queryScopeSearch(object $query, $term): object
    {
        return $query->search($term);
    }

    /**
     * Determines if drawing is able to be mutated (updated or deleted)
     *
     * @param Drawing $model
     * @param DrawingResource $resource
     * @return bool
     * @throws ImmutableEntityException
     */
    public function modelIsMutable(Drawing $model, DrawingResource $resource): bool
    {
        if ($model->version !== $resource->version()) {
            throw new ImmutableEntityException('Drawing version does not match resource version');
        }
        if ($model->version === 1 && $model->isLocked && $model->lockedByUserApiTokenID !== $resource->acl()->userApiToken()->userApiTokenID) {
            throw new ImmutableEntityException('Drawing is locked');
        }
        return true;
    }

    /**
     * Determines if drawing is allowed to be updated
     *
     * We do not allow finalized drawings to be changed.
     *
     * @param Drawing $model
     * @return bool
     * @throws ImmutableEntityException
     */
    public function modelIsUpdatable(Drawing $model): bool
    {
        if ($model->status === Drawing::STATUS_FINALIZED) {
            throw new ImmutableEntityException('Drawing is finalized');
        }
        return true;
    }

    /**
     * Determines if drawing is allowed to be deleted
     *
     * We currenly only allow drawings to be deleted if they are not finalized or they are not attached to bid.
     *
     * @param Drawing $model
     * @param DrawingResource $resource
     * @return bool
     * @throws ImmutableEntityException
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     */
    public function modelIsDeletable(Drawing $model, DrawingResource $resource): bool
    {
        if ($model->status === Drawing::STATUS_FINALIZED && $resource->isInUse($model->getUuidKey()->toString())) {
            throw new ImmutableEntityException('Drawing is in use on bid');
        }
        return true;
    }

    public function requestCollectionScope(Scope $scope, CollectionRequest $request)
    {
        if ($scope->isSearching() && $scope->getFormat() === 'drawing-v2-search') {
            $resource = $request->resource();
            $term = $scope->getSearch();

            $project_query = $resource->newScopedQuery()->withProject();
            (new Project())->scopeSearch($project_query, $term);
            $request->unionQuery($project_query);

            $property_query = $resource->newScopedQuery()->withProperty();
            (new Property())->scopeSearch($property_query, $term);
            $request->unionQuery($property_query);

            $customer_query = $resource->newScopedQuery()->withCustomer();
            (new Customer())->scopeSearch($customer_query, $term);
            $request->unionQuery($customer_query);

            $customer_phone_query = $resource->newScopedQuery()
                ->withCustomer()
                ->join('customerPhone', 'customerPhone.customerID', '=', 'customer.customerID');
            (new CustomerPhone())->scopeSearch($customer_phone_query, $term);
            $request->unionQuery($customer_phone_query);

            $request->enableChunking()->chunkLimit(100);
        }
    }

    public function scopeBuildBefore(Scope $scope, ScopeBuilder $builder): void
    {
        $format = $scope->getFormat();
        switch ($format) {
            case 'bid-v1':
                $scope->fields(['id', 'name', 'finalized_at'], true);
                $scope->filter('status', 'eq', DrawingResource::STATUS_FINALIZED);
                $scope->with(['image_media_urls', 'repair_plan_media_urls']);
                $scope->sort('finalized_at', 'asc');
                break;
            case 'drawing-app-v1':
            case 'drawing-app-v1-sync':
                $scope->filter('version', 'eq', 1);
                $scope->with(['nodes', 'image_media_urls']);
                break;
            case 'drawing-v2':
            case 'drawing-v2-search':
                $scope->fields([
                    'id', 'subversion', 'status', 'project_id', 'name', 'config', 'started_at', 'last_modified_at',
                    'created_by_user_id', 'is_deleted'
                ], true);
                $scope->filter('version', 'eq', 2);
                $scope->with(['project']);
                $scope->query(function ($query) use ($format, $builder) {
                    $query->where('drawings.createdByUserID', $builder->getResource()->acl()->user()->getKey());
                    if ($format === 'drawing-v2') {
                        $query->withTrashed();
                    }
                    return $query;
                });
                if ($format === 'drawing-v2-search') {
                    $scope->with(['created_by_user']);
                }
                break;
            case 'detail-v1':
                $scope->fields(['name'], true);
                $scope->with(['repair_plan_media_urls']);
                break;
        }
    }
}
