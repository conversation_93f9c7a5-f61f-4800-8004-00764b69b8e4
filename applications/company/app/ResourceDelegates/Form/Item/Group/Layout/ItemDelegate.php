<?php

namespace App\ResourceDelegates\Form\Item\Group\Layout;

use App\Resources\Form\Item\Group\FieldResource;
use App\Resources\Form\Item\Group\Layout\ItemResource;
use App\Resources\Form\Item\Group\LayoutResource;
use App\Resources\Form\Item\Group\TemplateResource;
use App\Resources\Form\Item\GroupResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Relations\PolyRelation;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Core\Components\Validation\Classes\Validator;
use Ramsey\Uuid\UuidInterface;

class ItemDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('layout')->resource(LayoutResource::class);
        $list->polymorphic('item')->typeField('item_type')
            ->types(function (PolyRelation $relation) {
                $relation->type(ItemResource::TYPE_GROUP)->resource(GroupResource::class);
                $relation->type(ItemResource::TYPE_FIELD)->resource(FieldResource::class);
                $relation->type(ItemResource::TYPE_TEMPLATE)->resource(TemplateResource::class);
            });

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('formItemGroupLayoutItemID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([ItemResource::ACTION_CREATE, ItemResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $list->field('layout_id')
            ->typeUuid()
            ->column('formItemGroupLayoutID', true)
            ->validation('Field Id', 'required|uuid|check_layout_id')
            ->onAction(ItemResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $type_map = ItemResource::getTypeMap();
        $list->field('item_type')
            ->column('itemType', true)
            ->validation('Item Type', 'required|type[int]|in_array[types]')
            ->saveMutator(function ($value) use ($type_map) {
                return array_search($value, $type_map);
            })
            ->outputMutator(function ($value) use ($type_map) {
                return $type_map[$value];
            });

        $list->field('item_id')
            ->typeUuid()
            ->column('itemID', true)
            ->validation('Item Id', 'required|uuid|check_item_id');

        $list->field('size')
            ->validation('Size', 'required|type[int]');

        $list->field('order')
            ->validation('Order', 'required|type[int]');

        $list->field('is_last_in_row')
            ->column('isLastInRow')
            ->validation('Is Last In Row', 'required|type[bool]');

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, ItemResource $resource)
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_layout_id', function (UuidInterface $id) use ($resource) {
            if ($resource->relationResource('layout')->entityExists($id->toString())) {
                return true;
            }
            return 'check_layout_id';
        }, [
            'check_layout_id' => 'Unable to find layout'
        ]);

        $rules->register('check_item_id', function (UuidInterface $id, $params, Validator $validator) use ($resource) {
            if ($validator->errors()->has('item_type')) {
                return Rules::STOP;
            }
            $type = $validator->data('item_type');
            $type_resource = $resource->polyRelationResource('item', $type);

            if (!$type_resource->entityExists($id->toString())) {
                return 'check_item_id';
            }
            return true;
        }, [
            'check_item_id' => 'Unable to find item'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('types', ItemResource::getTypes());
        return $config;
    }

    public function queryScopeGlobal($query, ItemResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function actionAllowed($action, ItemResource $resource)
    {
        if (in_array($action, [ItemResource::ACTION_GET_COLLECTION, ItemResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        return false;
    }

    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getFormat()) {
            case 'form-v1':
                $scope->fields(['id', 'item_type', 'item_id', 'size', 'order', 'is_last_in_row'], true);
                $scope->query(fn($query) => $query->ordered());
                break;
        }
    }
}
