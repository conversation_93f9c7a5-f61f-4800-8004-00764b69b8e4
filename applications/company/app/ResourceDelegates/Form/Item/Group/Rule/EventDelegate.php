<?php

namespace App\ResourceDelegates\Form\Item\Group\Rule;

use App\Resources\Form\Item\Group\Rule\EventResource;
use App\Resources\Form\Item\Group\RuleResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Ramsey\Uuid\UuidInterface;

class EventDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('rule')->resource(RuleResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('formItemGroupRuleEventID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([EventResource::ACTION_CREATE, EventResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $list->field('rule_id')
            ->typeUuid()
            ->column('formItemGroupRuleID', true)
            ->validation('Rule Id', 'required|uuid|check_rule_id')
            ->onAction(EventResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $list->field('alias')
            ->validation('Alias', 'nullable|optional|alias');

        $list->field('type')
            ->validation('Type', 'required|type[int]|in_array[types]');

        $list->field('state')
            ->validation('State', 'required|type[int]|in_array[states]');

        $list->field('params')
            ->validation('Params', 'required|type[array]');

        $list->field('order')
            ->validation('Order', 'required|type[int]')
            ->onAction([EventResource::ACTION_CREATE, EventResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('nullable|optional|type[int]');
            });

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, EventResource $resource)
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_rule_id', function (UuidInterface $id) use ($resource) {
            if ($resource->relationResource('rule')->entityExists($id->toString())) {
                return true;
            }
            return 'check_rule_id';
        }, [
            'check_rule_id' => 'Unable to find rule'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('types', EventResource::getTypes());
        $config->store('states', EventResource::getStates());
        return $config;
    }

    public function anyCreateValidateAfter(Entity $entity)
    {
        if ($entity->get('order') === null) {
            $entity->set('order', 1);
        }
        return $entity;
    }

    public function queryScopeGlobal($query, EventResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function actionAllowed($action, EventResource $resource)
    {
        if (in_array($action, [EventResource::ACTION_GET_COLLECTION, EventResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        return false;
    }

    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getFormat()) {
            case 'form-v1':
                $scope->fields(['id', 'alias', 'type', 'state', 'params', 'order'], true);
                $scope->query(fn($query) => $query->ordered());
                break;
        }
    }
}
