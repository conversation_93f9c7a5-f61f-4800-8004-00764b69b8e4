<?php

namespace App\ResourceDelegates\Form\Item\Group;

use App\Resources\Form\Item\Group\TemplateResource;
use App\Resources\Form\Item\GroupResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Common\Models\FormItemGroupTemplate;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Ramsey\Uuid\UuidInterface;

class TemplateDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('group')->resource(GroupResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('formItemGroupTemplateID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([TemplateResource::ACTION_CREATE, TemplateResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $list->field('group_id')
            ->typeUuid()
            ->column('formItemGroupID', true)
            ->validation('Group Id', 'required|uuid|check_group_id')
            ->onAction(TemplateResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $list->field('alias')
            ->validation('Alias', 'nullable|optional|alias');

        $list->field('type')
            ->validation('Type', 'required|type[int]|in_array[types]');

        $list->field('styles')
            ->validation('Styles', 'nullable');

        $list->field('content')
            ->validation('Content', 'wysiwyg_trim|clean_html|nullable|optional|minify_html');

        $list->field('scripts')
            ->validation('Scripts', 'nullable');

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, TemplateResource $resource)
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_group_id', function (UuidInterface $id) use ($resource) {
            if ($resource->relationResource('group')->entityExists($id->toString())) {
                return true;
            }
            return 'check_group_id';
        }, [
            'check_group_id' => 'Unable to find group'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('types', TemplateResource::getTypes());
        return $config;
    }

    public function modelDataAfter($model_data)
    {
        if ($model_data['type'] === FormItemGroupTemplate::TYPE_CLIENT) {
            $model_data['styles'] = null;
            $model_data['scripts'] = null;
        }
        return $model_data;
    }

    public function queryScopeGlobal($query, TemplateResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function actionAllowed($action, TemplateResource $resource)
    {
        if (in_array($action, [TemplateResource::ACTION_GET_COLLECTION, TemplateResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        return false;
    }

    public function scopeBuildBefore(Scope $scope)
    {
        $format = $scope->getFormat();
        switch ($format) {
            case 'form-v1':
                $scope->fields(['id', 'alias', 'type', 'content', 'styles'], true);
                break;
        }
    }
}
