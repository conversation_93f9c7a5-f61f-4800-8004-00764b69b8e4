<?php

namespace App\ResourceDelegates\Form;

use App\Resources\Form\Item\DependencyResource;
use App\Resources\Form\Item\MetaResource;
use App\Resources\Form\Item\OverrideResource;
use App\Resources\Form\Item\GroupResource;
use App\Resources\Form\ItemResource;
use App\Resources\Company\Form\ItemResource as CompanyFormItemResource;
use App\Resources\System\Form\ItemResource as SystemFormItemResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Relations\PolyRelation;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Core\Components\Validation\Classes\Validator;
use Ramsey\Uuid\UuidInterface;

class ItemDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('dependencies')->resource(DependencyResource::class);
        $list->oneOrMany('meta')->resource(MetaResource::class);
        $list->oneOrMany('groups')->resource(GroupResource::class);
        $list->polymorphic('owner')->typeField('owner_type')->idField('owner_id')
            ->types(function (PolyRelation $relation) {
                $relation->type(ItemResource::OWNER_TYPE_SYSTEM)->resource(SystemFormItemResource::class);
                $relation->type(ItemResource::OWNER_TYPE_COMPANY)->resource(CompanyFormItemResource::class);
            });
        $list->oneOrMany('overrides')->resource(OverrideResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('formItemID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([ItemResource::ACTION_CREATE, ItemResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $owner_type_map = ItemResource::getOwnerTypeMap();
        $list->field('owner_type')
            ->column('ownerType', true)
            ->validation('Owner Type', 'required|type[int]|in_array[owner_types]')
            ->saveMutator(function ($value) use ($owner_type_map) {
                return array_search($value, $owner_type_map);
            })
            ->outputMutator(function ($value) use ($owner_type_map) {
                return $owner_type_map[$value];
            });

        $list->field('owner_id')
            ->typeUuid()
            ->column('ownerID', true)
            ->validation('Owner Id', 'required|uuid|check_owner_id')
            ->onAction(ItemResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $type_map = ItemResource::getTypeMap();
        $list->field('type')
            ->validation('Type', 'required|type[int]|in_array[types]')
            ->requireColumn()
            ->saveMutator(function ($value) use ($type_map) {
                return array_search($value, $type_map);
            })
            ->outputMutator(function ($value) use ($type_map) {
                return $type_map[$value];
            });

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, ItemResource $resource)
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_owner_id', function ($id, $params, Validator $validator) use ($resource) {
            if ($validator->errors()->has('owner_type')) {
                return Rules::STOP;
            }
            $type = $validator->data('owner_type');
            $owner_type_resource = $resource->polyRelationResource('owner', $type);

            if (!$owner_type_resource->entityExists($id)) {
                return 'check_owner_id';
            }
            return true;
        }, [
            'check_owner_id' => 'Unable to find owner'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('owner_types', ItemResource::getOwnerTypes());
        $config->store('types', ItemResource::getTypes());
        return $config;
    }

    public function actionAllowed($action, ItemResource $resource)
    {
        if (in_array($action, [ItemResource::ACTION_GET_COLLECTION, ItemResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        return $user === null;
    }

    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getFormat()) {
            case 'form-v1':
                $scope->fields(['id', 'owner_type', 'owner_id', 'type'], true);
                $scope->with(['dependencies', 'groups', 'overrides', 'meta']);
                break;
        }
    }
}
