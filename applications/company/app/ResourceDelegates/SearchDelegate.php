<?php

declare(strict_types=1);

namespace App\ResourceDelegates;

use App\Resources\SearchResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Core\Components\Resource\Classes\{Field, FieldList};
use Core\Components\Validation\Classes\FieldConfig;

class SearchDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildFields(FieldList $list): FieldList
    {
        $list->field('id', true)
            ->typeUuid()
            ->column('searchID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction(SearchResource::ACTION_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid|pk_available')
                    ->save();
            });

        $list->field('source')
            ->validation('Source', 'required|type[int]|in_array[sources]');

        $list->field('term')
            ->validation('Term', 'required|max_length[200]');

        $list->field('result_count')
            ->column('resultCount')
            ->validation('Result Count', 'required|type[int]');

        $list->field('cache_status')
            ->column('cacheStatus')
            ->validation('Cache Status', 'required|type[int]|in_array[cache_statuses]');

        $list->field('time')
            ->validation('Time', 'required|type[int]');

        $this->timestampFields($list);

        return $list;
    }

    public function validationFieldConfig(FieldConfig $config): FieldConfig
    {
        $config->store('sources', SearchResource::getSources());
        $config->store('cache_statuses', SearchResource::getCacheStatuses());
        return $config;
    }

    public function queryScopeGlobal(object $query, SearchResource $resource): object
    {
        return $query->ofUser($resource->acl()->user());
    }
}
