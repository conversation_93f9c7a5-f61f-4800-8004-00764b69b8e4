<?php

declare(strict_types=1);

namespace App\ResourceDelegates\System\Form;

use App\Resources\System\Form\{CategoryItemResource, CategoryResource, ItemResource};
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Core\Components\Resource\Classes\{Entity, Field, FieldList, RelationList, Request};
use Core\Components\Resource\Exceptions\ValidationException;
use Core\Components\Validation\Classes\Rules;
use Ramsey\Uuid\UuidInterface;

class CategoryItemDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list): RelationList
    {
        $list->oneOrMany('category')->resource(CategoryResource::class);
        $list->oneOrMany('item')->resource(ItemResource::class);

        return $list;
    }

    public function buildFields(FieldList $list): FieldList
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('systemFormCategoryItemID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([CategoryItemResource::ACTION_CREATE, CategoryItemResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $list->field('category_id')
            ->typeUuid()
            ->column('systemFormCategoryID', true)
            ->validation('Category Id', 'required|uuid|check_category_id');

        $list->field('item_id')
            ->typeUuid()
            ->column('systemFormItemID', true)
            ->validation('Category Id', 'required|uuid|check_item_id');

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, CategoryItemResource $resource)
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_category_id', function (UuidInterface $id) use ($resource) {
            if ($resource->relationResource('category')->entityExists($id->toString())) {
                return true;
            }
            return 'check_category_id';
        }, [
            'check_category_id' => 'Unable to find category'
        ]);

        $rules->register('check_item_id', function (UuidInterface $id) use ($resource) {
            if ($resource->relationResource('item')->entityExists($id->toString())) {
                return true;
            }
            return 'check_item_id';
        }, [
            'check_item_id' => 'Unable to find item'
        ]);

        return $rules;
    }

    protected function checkEntityExists(Entity $entity, Request $request): void
    {
        $resource = $request->resource();
        $table_alias = $resource->getTableAlias();
        $count = $resource->newScopedQuery()
            ->where("{$table_alias}.systemFormCategoryID", $entity->get('category_id')->getBytes())
            ->where("{$table_alias}.systemFormItemID", $entity->get('item_id')->getBytes())
            ->count();
        if ($count !== 0) {
            throw new ValidationException('Entity with this category and item combination already exists');
        }
    }

    public function anyCreateValidateAfter(Entity $entity, Request $request): Entity
    {
        $this->checkEntityExists($entity, $request);
        return $entity;
    }

    public function anyUpdateValidateAfter(Entity $entity, Request $request): Entity
    {
        $this->checkEntityExists($entity, $request);
        return $entity;
    }

    public function actionAllowed(int $action, CategoryItemResource $resource): bool
    {
        if (in_array($action, [CategoryItemResource::ACTION_GET_COLLECTION, CategoryItemResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        return false;
    }
}
