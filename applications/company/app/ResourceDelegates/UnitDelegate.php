<?php

namespace App\ResourceDelegates;

use App\Resources\CompanyResource;
use App\Resources\UnitResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Carbon\Carbon;
use Common\Models\Unit;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Request;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Exceptions\ImmutableEntityException;
use Core\Components\Resource\Relations\PolyRelation;
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Core\Components\Validation\Classes\Validator;
use Core\Exceptions\AppException;
use Ramsey\Uuid\UuidInterface;

class UnitDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->polymorphic('owner')->typeField('owner_type')->idField('owner_id')
            ->types(function (PolyRelation $relation) {
                $relation->type(UnitResource::OWNER_TYPE_COMPANY)->resource(CompanyResource::class);
                // @todo figure out manufacturer
            });

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('unitID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction(UnitResource::ACTION_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $owner_type_map = UnitResource::getOwnerTypeMap();
        $list->field('owner_type')
            ->column('ownerType', true)
            ->validation('Owner Type', 'required|type[int]|in_array[owner_types]')
            ->saveMutator(function ($value) use ($owner_type_map) {
                return array_search($value, $owner_type_map);
            })
            ->outputMutator(function ($value) use ($owner_type_map) {
                return $owner_type_map[$value];
            });

        $list->field('owner_id')
            ->column('ownerID', true)
            ->validation('Owner Id', 'required|type[int]|check_owner_id');

        $list->field('alias')
            ->validation('Alias', 'trim|nullable|optional|max_length[100]|check_alias');

        $list->field('name')
            ->validation('Name', 'required|max_length[100]');

        $list->field('abbreviation')
            ->validation('Abbreviation', 'required|max_length[100]');

        $list->field('status')
            ->validation('Status', 'required|type[int]|in_array[statuses]')
            ->disableAction([UnitResource::ACTION_CREATE, UnitResource::ACTION_NESTED_CREATE])
            ->onAction(UnitResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('archived_at')
            ->typeDateTime()
            ->column('archivedAt')
            ->immutable();

        $list->field('archived_by_user_id')
            ->column('archivedByUserID')
            ->immutable();

        $this->timestampFields($list);

        $list->modify(['name', 'abbreviation'], function (Field $field) {
            return $field->enableAction(UnitResource::ACTION_SORT);
        });

        return $list;
    }

    public function configureFields(FieldList $list, UnitResource $resource)
    {
        if ($resource->acl()->user() !== null) {
            $list->get('owner_type')->disable();
            $list->get('owner_id')->disable();
        }

        return $list;
    }

    public function validationRules(Rules $rules, UnitResource $resource)
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_owner_id', function ($id, $params, Validator $validator) use ($resource) {
            if ($validator->errors()->has('owner_type')) {
                return Rules::STOP;
            }
            $type = $validator->data('owner_type');
            $owner_type_resource = $resource->polyRelationResource('owner', $type);

            if (!$owner_type_resource->entityExists($id)) {
                return 'check_owner_id';
            }
            return true;
        }, [
            'check_owner_id' => 'Unable to find owner'
        ]);

        $rules->register('check_alias', function ($alias, $params, Validator $validator) use ($resource) {
            if (preg_match('#^(?!-)[a-z0-9\-]+(?<!-)$#', $alias) !== 1) {
                return 'alias_not_valid';
            }
            if ($validator->errors()->has('owner_type') || $validator->errors()->has('owner_id')) {
                return Rules::STOP;
            }
            if (
                ($owner_type = $validator->data('owner_type')) === null ||
                ($owner_id = $validator->data('owner_id')) === null
            ) {
                $user = $resource->acl()->user();
                if ($user === null) {
                    throw new AppException('User is required to check alias for unit');
                }
                $owner_type = UnitResource::OWNER_TYPE_COMPANY;
                $owner_id = $user->companyID;
            }
            $model = $validator->getconfig()->storage('_model');
            if (
                ($model === null || $model->alias !== $alias) &&
                $resource->isAliasInUse($owner_type, $owner_id, $alias)
            ) {
                return 'alias_in_use';
            }
            return true;
        }, [
            'alias_not_valid' => 'Alias is not in the proper format',
            'alias_in_use' => 'Alias already in use'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('owner_types', UnitResource::getOwnerTypes());
        $config->store('statuses', UnitResource::getStatuses());
        return $config;
    }

    public function queryScopeGlobal($query, UnitResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function queryScopeSearch($query, $term)
    {
        return $query->searchWithRank($term);
    }

    public function createModelDataAfter($model_data, Request $request)
    {
        $user = $request->resource()->acl()->user();
        if ($user !== null) {
            $model_data['ownerType'] = Unit::OWNER_COMPANY;
            $model_data['ownerID'] = $user->companyID;
        }
        $model_data['status'] = Unit::STATUS_ACTIVE;
        return $model_data;
    }

    public function anyUpdateModelDataAfter($model_data, UpdateRequest $request)
    {
        $resource = $request->resource();
        $model = $request->getModel();
        if (isset($model_data['status']) && $model_data['status'] !== $model->status) {
            switch ($model_data['status']) {
                case Unit::STATUS_ACTIVE:
                    $model_data['archivedAt'] = null;
                    $model_data['archivedByUserID'] = null;
                    break;
                case Unit::STATUS_ARCHIVED:
                    $model_data['archivedAt'] = Carbon::now('UTC');
                    $model_data['archivedByUserID'] = $resource->acl()->user()->getKey();
                    break;
            }
        }
        return $model_data;
    }

    public function actionAllowed($action, UnitResource $resource)
    {
        if (in_array($action, [UnitResource::ACTION_GET_COLLECTION, UnitResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        return false;
    }

    public function modelIsDeletable(Unit $model)
    {
        if ($model->productItems()->count() > 0) {
            throw new ImmutableEntityException('Cannot delete unit which is still in use by a product item');
        }
        return true;
    }

    public function scopeBuildBefore(Scope $scope)
    {
        if ($scope->getFormat() === 'bid-v1') {
            $scope->fields(['name', 'abbreviation'], true);
        }
    }
}
