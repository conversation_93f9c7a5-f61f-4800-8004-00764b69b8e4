<?php

declare(strict_types=1);

namespace App\ResourceJobs\Company\Invoice;

use App\Attributes\JobAttribute;
use App\Classes\{Acl, Log};
use App\Resources\Company\{InvoiceResource, PaymentMethodResource};
use App\Services\Email\Types\User\PaymentFailedType;
use App\Services\PaymentService;
use App\Services\Payment\Exceptions\{PaymentException, TransactionDeclinedException};
use Carbon\Carbon;
use Core\Components\Queue\Classes\Job;
use Core\Components\Queue\Exceptions\{JobFailedException, MaxTriesExceededException};
use Core\Components\Resource\Classes\{Entity, Scope};
use Core\Components\Resource\Exceptions\EntityNotFoundException;
use Core\StaticAccessors\App;
use Throwable;

/**
 * Class PayJob
 *
 * Takes an invoice id, grabs related information, and tries to pay the total via the PaymentService
 *
 * @package App\ResourceJobs\Company\Invoice
 */
#[JobAttribute(type: 1, max_tries: 2)]
class PayJob extends Job
{
    /**
     * PayJob constructor
     *
     * @param int $invoice_id
     */
    public function __construct(protected int $invoice_id)
    {}

    /**
     * Handle job
     *
     * Verify invoice is ready to be paid, find default payment method for company, and send the transaction.
     *
     * @throws JobFailedException
     * @throws PaymentException
     */
    public function handle(): void
    {
        try {
            $acl = Acl::make();

            $invoice_resource = InvoiceResource::make($acl);
            $invoice_scope = Scope::make()
                ->fields(['id', 'type', 'company_id', 'description', 'status', 'total', 'payment_method_id'])
                ->with([
                    'company' => [
                        'fields' => ['name']
                    ]
                ]);
            $invoice = $invoice_resource->entity($this->invoice_id)->scope($invoice_scope)->run();

            // if the invoice is not in pending status, just fail the job immediately
            if ($invoice['status'] !== InvoiceResource::STATUS_PENDING) {
                throw new JobFailedException('Invoice not in pending status');
            }

            $payment_method_resource = PaymentMethodResource::make($acl);
            if (($payment_method_id = $invoice['payment_method_id']) === null) {
                $payment_method_id = $payment_method_resource->getDefaultByCompanyID($invoice['company_id']);
            }

            $payment_method_scope = Scope::make()->fields(['payment_profile_id'])->with([
                'company' => [
                    'fields' => ['customer_profile_id']
                ]
            ]);
            $payment_method = $payment_method_resource->entity($payment_method_id)->scope($payment_method_scope)->run();

            /** @var PaymentService $payment_service */
            $payment_service = App::get(PaymentService::class);
            try {
                $transaction = $payment_service->chargePaymentProfile(
                    $payment_method->get('company.customer_profile_id'),
                    $payment_method['payment_profile_id'],
                    [
                        'invoice_id' => $invoice['id'], // @todo currently not used, figure out how to add to transaction
                        'amount' => $invoice['total']
                    ]
                );
            } catch (PaymentException $e) {
                // @todo handle payment exceptions
                throw $e;
            }

            $invoice_resource->partialUpdate(Entity::make([
                'id' => $invoice['id'],
                'status' => InvoiceResource::STATUS_PAID,
                'paid_by_payment_method_id' => $payment_method_id,
                'transaction_id' => $transaction['id']
            ]))->run();

            if ($invoice['type'] === InvoiceResource::TYPE_GENERAL) {
                Log::support('pay_job')->info('Invoice paid successfully', [
                    'id' => $invoice['id'],
                    'company_name' => $invoice->get('company.name'),
                    'description' => $invoice['description'],
                    'total' => $invoice['total']
                ]);
            }
        } catch (EntityNotFoundException $e) {
            throw new JobFailedException('Invoice %d not found', $this->invoice_id);
        }
    }

    /**
     * Retry after an hour
     *
     * @return Carbon|null
     */
    public function retryAt(): ?Carbon
    {
        return Carbon::now('UTC')->addHour();
    }

    /**
     * Handle payment failure
     *
     * If transaction declined, an email is sent to company.
     *
     * @param Throwable $e
     * @param int $invoice_id
     * @param bool $suspension_warning
     * @throws \Core\Exceptions\AppException
     */
    public static function handleFailure(Throwable $e, int $invoice_id, bool $suspension_warning): void
    {
        InvoiceResource::make(Acl::make())->partialUpdate(Entity::make([
            'id' => $invoice_id,
            'status' => InvoiceResource::STATUS_FAILED
        ]))->run();

        if ($e instanceof MaxTriesExceededException) {
            $e = $e->getLastException();
        }
        if ($e instanceof TransactionDeclinedException) {
            PaymentFailedType::send([
                'invoice_id' => $invoice_id,
                'error' => $e->getMessage(),
                'suspension_warning' => $suspension_warning
            ]);
        }
    }

    /**
     * Handle failed job
     *
     * @param Throwable $e
     * @param int $tries
     * @throws \Core\Exceptions\AppException
     */
    public function failed(Throwable $e, int $tries): void
    {
        self::handleFailure($e, $this->invoice_id, true);
    }
}
