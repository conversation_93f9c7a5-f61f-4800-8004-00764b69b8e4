<?php

declare(strict_types=1);

namespace App\ResourceJobs\User;

use App\Attributes\JobAttribute;
use App\Services\GoogleApi\Exceptions\UserNotFoundException;
use App\Services\GoogleApiService;
use Core\Components\Queue\Classes\Job;
use Core\Components\Queue\Exceptions\JobFailedException;

/**
 * Class GoogleDisconnectJob
 *
 * @package App\ResourceJobs\User
 */
#[JobAttribute(type: 34, timeout: 300)]
class GoogleDisconnectJob extends Job
{
    /**
     * GoogleDisconnectJob constructor
     *
     * @param int $user_id
     */
    public function __construct(protected int $user_id)
    {}

    /**
     * Handle job
     *
     * @throws \Core\Exceptions\AppException
     */
    public function handle(): void
    {
        try {
            $google_api = GoogleApiService::makeFromUserID($this->user_id);
            if (!$google_api->isConnected()) {
                return;
            }
            $google_api->queueDisconnect();
        } catch (UserNotFoundException $e) {
            throw (new JobFailedException('Unable to find user'))->setLastException($e);
        }
    }
}
