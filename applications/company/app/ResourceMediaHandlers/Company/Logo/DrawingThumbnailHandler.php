<?php

declare(strict_types=1);

namespace App\ResourceMediaHandlers\Company\Logo;

use App\Classes\Func;
use App\ResourceMediaHandlers\BaseCompanyFileVariantHandler;
use Core\Components\Http\Responses\FileResponse;
use Core\Components\Resource\Classes\Entity;
use App\Resources\File\VariantResource;
use App\Traits\ResourceMedia\ImageTrait;
use Core\Components\Http\Classes\Http;
use Core\Components\Http\StaticAccessors\Response;
use Ramsey\Uuid\UuidInterface;

/**
 * Class DrawingThumbnailHandler
 *
 * @package App\ResourceMediaHandlers\Company\Logo
 */
class DrawingThumbnailHandler extends BaseCompanyFileVariantHandler
{
    use ImageTrait;

    /**
     * Generate thumbnail image
     *
     * @param Entity $entity
     * @return array
     * @throws \Core\Exceptions\AppException
     */
    public function generate(Entity $entity): array
    {
        try {
            $width = 450;
            $height = 400;

            $manager = $this->newImageManager();
            $temp_file = Func::createTempFile(null, false);
            $image = $manager->make($this->getOriginalPathFromEntity($entity))->opacity(10);
            $image->width() > $image->height() ? $height = null : $width = null;
            $image->resize($width, $height, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            })
                ->save($temp_file);
            $extension = Http::getExtensionByMimeType($image->mime());
            $data = $this->saveFileVariant($entity, "thumbnail.{$extension}", $temp_file);

            $image->destroy();
            return $data;
        } finally {
            if (isset($temp_file) && file_exists($temp_file)) {
                unlink($temp_file);
            }
        }
    }

    /**
     * Get file info from id or modal
     *
     * If file id is provided, additional lookups are not required.
     *
     * @param mixed $id
     * @param string|UuidInterface|null $file_id
     * @return array
     * @throws \Core\Components\Resource\Exceptions\MediaNotFoundException
     */
    protected function getFileData(mixed $id, string|UuidInterface $file_id = null): array
    {
        $this->resource->setMediaCompanyID((int) $id);
        if ($file_id === null) {
            $company = $this->resource->findOrFail($id);
            $data = $this->getVariantFromField(VariantResource::TYPE_COMPANY_DRAWING_THUMBNAIL, 'logo_file_id', $company);
        } else {
            $data = $this->getVariantInfo(VariantResource::TYPE_COMPANY_DRAWING_THUMBNAIL, $file_id);
        }
        return $data;
    }

    /**
     * Get response for media controller
     *
     * @param mixed $id
     * @param array $config
     * @return FileResponse
     * @throws \Core\Components\Resource\Exceptions\MediaNotFoundException
     */
    public function getResponse(mixed $id, array $config = []): FileResponse
    {
        $data = $this->getFileData($id);

        $response = Response::file($data['path']);
        $response->contentType($data['content_type']);
        $response->filename($data['filename']);
        return $response;
    }

    /**
     * Get path to thumbnail by id or model
     *
     * @param mixed $id
     * @param string|UuidInterface|null $file_id
     * @return string
     * @throws \Core\Components\Resource\Exceptions\MediaNotFoundException
     */
    public function getPath(mixed $id, string|UuidInterface $file_id = null): string
    {
        $data = $this->getFileData($id, $file_id);
        return $data['path'];
    }
}
