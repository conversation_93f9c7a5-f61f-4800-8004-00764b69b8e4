<?php

namespace App\Resources\Bid\Item;

use App\ResourceDelegates\Bid\Item\PriceAdjustmentLineItemDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\BidItemPriceAdjustmentLineItem;
use Core\Components\Resource\Classes\Resource;

class PriceAdjustmentLineItemResource extends Resource
{
    const AMOUNT_TYPE_TOTAL = 1;
    const AMOUNT_TYPE_PERCENTAGE = 2;

    use UserActionTrackingTrait;

    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~self::ACTION_SEARCH;

    protected $table = 'bidItemPriceAdjustmentLineItems';
    protected $model = BidItemPriceAdjustmentLineItem::class;

    public static function getAmountTypes()
    {
        return [
            static::AMOUNT_TYPE_TOTAL, static::AMOUNT_TYPE_PERCENTAGE
        ];
    }

    protected static function boot()
    {
        static::delegate(PriceAdjustmentLineItemDelegate::class);
    }
}
