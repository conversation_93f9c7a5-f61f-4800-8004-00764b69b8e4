<?php

namespace App\Resources\Company\Form;

use App\ResourceDelegates\Company\Form\CategoryDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\CompanyFormCategory;
use Core\Components\Resource\Classes\Collection;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Resource;

class CategoryResource extends Resource
{
    const TYPE_BID = 1;

    const STATUS_ACTIVE = 1;
    const STATUS_ARCHIVED = 2;

    use UserActionTrackingTrait;

    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_GROUP_NESTED);

    protected $table = 'companyFormCategories';
    protected $model = CompanyFormCategory::class;

    protected $generate_id = true;

    protected $allow_no_user = true;

    public static function getTypes()
    {
        return [
            static::TYPE_BID
        ];
    }

    public static function getTypeMap()
    {
        return [
            CompanyFormCategory::TYPE_BID => self::TYPE_BID
        ];
    }

    public static function getStatuses()
    {
        return [static::STATUS_ACTIVE, static::STATUS_ARCHIVED];
    }

    protected static function boot()
    {
        static::delegate(CategoryDelegate::class);
    }

    /**
     * Get list containing all categories and their children's id's along with the top-level (root) categories needs to
     * recursively build a tree
     *
     * @param Collection $collection
     * @return Collection
     */
    public function getList(Collection $collection)
    {
        $categories = [];
        $roots = [];
        foreach ($collection as $category) {
            if (!isset($categories[$category['id']])) {
                $categories[$category['id']] = [
                    'children' => []
                ];
            }
            $categories[$category['id']]['entity'] = $category;
            if ($category['parent_id'] === null) {
                $roots[] = $category['id'];
            } else {
                if (!isset($categories[$category['parent_id']])) {
                    $categories[$category['parent_id']] = [
                        'children' => []
                    ];
                }
                $categories[$category['parent_id']]['children'][] = $category['id'];
            }
        }

        return new Collection([
            'all' => $categories,
            'root' => $roots
        ]);
    }

    /**
     * Get nested list of categories using getList() method output
     *
     * Note: by default this will pass a generic collection of categories to the getList() method automatically. this can
     * be stopped by setting the $get_list param to false
     *
     * @param Collection $collection
     * @param bool $get_list
     * @return Collection
     */
    public function getNestedList(Collection $collection, $get_list = true)
    {
        if ($get_list) {
            $collection = $this->getList($collection);
        }

        $build_list = function ($category) use (&$build_list, $collection) {
            $entity = $category['entity']->toArray();
            $entity['categories'] = [];

            if (count($category['children']) > 0) {
                foreach ($category['children'] as $child) {
                    $entity['categories'][] = $build_list($collection['all'][$child]);
                }
            }

            return $entity;
        };

        $new_collection = [];
        foreach ($collection['root'] as $root_id) {
            $new_collection[] = $build_list($collection['all'][$root_id]);
        }

        return new Collection($new_collection);
    }

    public function archiveChildrenByID($id)
    {
        $primary_field = $this->getPrimaryField();
        $id = $primary_field->saveValue($id);
        $this->newScopedQuery()
            ->where('companyFormCategories.parentCompanyFormCategoryID', $id)
            ->each(function ($category) use ($primary_field) {
                $this->partialUpdate(Entity::make([
                    $this->getPrimaryFieldName() => $primary_field->outputValueFromModel($category),
                    'status' => static::STATUS_ARCHIVED
                ]))->run();
            });
    }

    public function deleteChildrenByID($id)
    {
        $primary_field = $this->getPrimaryField();
        $id = $primary_field->saveValue($id);
        $this->newScopedQuery()
            ->where('companyFormCategories.parentCompanyFormCategoryID', $id)
            ->each(function ($category) use ($primary_field) {
                $this->delete(Entity::make([
                    $this->getPrimaryFieldName() => $primary_field->outputValueFromModel($category)
                ]))->run();
            });
    }
}
