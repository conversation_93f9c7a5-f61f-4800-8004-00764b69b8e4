<?php

namespace App\Resources\Company;

use App\ResourceDelegates\Company\SubscriptionDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\CompanySubscription;
use Core\Components\Resource\Classes\Resource;

class SubscriptionResource extends Resource
{
    const INTERVAL_UNIT_DAY = 1;
    const INTERVAL_UNIT_MONTH = 2;
    const INTERVAL_UNIT_YEAR = 3;

    const STATUS_ACTIVE = 1;
    const STATUS_REPLACED = 2;
    const STATUS_CANCELLED = 3;

    use UserActionTrackingTrait;

    protected $available_actions = (self::ACTION_GROUP_READ_ONLY_FULL | self::ACTION_GROUP_CREATE | self::ACTION_PARTIAL_UPDATE) & ~self::ACTION_GROUP_BATCH;

    protected $table = 'companySubscriptions';
    protected $model = CompanySubscription::class;

    protected $allow_no_user = true;

    public static function getIntervalUnits()
    {
        return [static::INTERVAL_UNIT_DAY, static::INTERVAL_UNIT_MONTH, static::INTERVAL_UNIT_YEAR];
    }

    public static function getStatuses()
    {
        return [static::STATUS_ACTIVE, static::STATUS_REPLACED, static::STATUS_CANCELLED];
    }

    protected static function boot()
    {
        static::delegate(SubscriptionDelegate::class);
    }

    public function getCurrentByCompanyID($company_id)
    {
        return $this->newScopedQuery()
            ->where('companyID', $company_id)
            ->where('isCurrent', 1)
            ->value('companySubscriptionID');
    }
}
