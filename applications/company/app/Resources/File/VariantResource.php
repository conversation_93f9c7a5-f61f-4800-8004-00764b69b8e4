<?php

declare(strict_types=1);

namespace App\Resources\File;

use App\Interfaces\Resource\FileHandlerResourceInterface;
use App\ResourceDelegates\File\VariantDelegate;
use App\Resources\FileResource;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\FileVariant;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\MediaTypeVersion;
use Core\Components\Resource\Classes\Resource;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Classes\ScopeBuilder;
use Core\Exceptions\AppException;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidInterface;

class VariantResource extends Resource implements FileHandlerResourceInterface
{
    use UserActionTrackingTrait;

    public const TYPE_DRAWING_APP_EVENT_THUMBNAIL = 1;
    public const TYPE_DRAWING_EVALUATION_THUMBNAIL = 2;
    public const TYPE_COMPANY_PROFILE_THUMBNAIL = 3;
    public const TYPE_USER_PROFILE_THUMBNAIL = 4;
    public const TYPE_COMPANY_EMAIL_THUMBNAIL = 5;
    public const TYPE_COMPANY_DOCUMENT_THUMBNAIL = 6;
    public const TYPE_USER_EMAIL_THUMBNAIL = 7;
    public const TYPE_FORM_UPLOAD_FIELD_THUMBNAIL = 8;
    public const TYPE_BID_CUSTOM_DRAWING_THUMBNAIL = 9;
    public const TYPE_DRAWING_BID_THUMBNAIL = 10;
    public const TYPE_PROPERTY_SIZE_MEDIUM = 11;
    public const TYPE_PROJECT_FILE_THUMBNAIL = 12;
    public const TYPE_USER_BID_THUMBNAIL = 13;
    public const TYPE_COMPANY_DRAWING_THUMBNAIL = 14;

    public const TYPES_CONFIG = [
        self::TYPE_DRAWING_APP_EVENT_THUMBNAIL => [
            'parent_type' => FileResource::TYPE_DRAWING,
            'version_name' => 'app-event-thumbnail'
        ],
        self::TYPE_DRAWING_EVALUATION_THUMBNAIL => [
            'parent_type' => FileResource::TYPE_DRAWING,
            'version_name' => 'evaluation_thumbnail'
        ],
        self::TYPE_COMPANY_PROFILE_THUMBNAIL => [
            'parent_type' => FileResource::TYPE_COMPANY_LOGO,
            'version_name' => 'profile_thumbnail'
        ],
        self::TYPE_USER_PROFILE_THUMBNAIL => [
            'parent_type' => FileResource::TYPE_USER_IMAGE,
            'version_name' => 'profile_thumbnail'
        ],
        self::TYPE_COMPANY_EMAIL_THUMBNAIL => [
            'parent_type' => FileResource::TYPE_COMPANY_LOGO,
            'version_name' => 'email_thumbnail'
        ],
        self::TYPE_COMPANY_DOCUMENT_THUMBNAIL => [
            'parent_type' => FileResource::TYPE_COMPANY_LOGO,
            'version_name' => 'document_thumbnail'
        ],
        self::TYPE_USER_EMAIL_THUMBNAIL => [
            'parent_type' => FileResource::TYPE_USER_IMAGE,
            'version_name' => 'email_thumbnail'
        ],
        self::TYPE_FORM_UPLOAD_FIELD_THUMBNAIL => [
            'parent_type' => FileResource::TYPE_FORM_UPLOAD,
            'version_name' => 'field_thumbnail'
        ],
        self::TYPE_BID_CUSTOM_DRAWING_THUMBNAIL => [
            'parent_type' => FileResource::TYPE_BID_CUSTOM_DRAWING,
            'version_name' => 'thumbnail'
        ],
        self::TYPE_DRAWING_BID_THUMBNAIL => [
            'parent_type' => FileResource::TYPE_DRAWING,
            'version_name' => 'bid_thumbnail'
        ],
        self::TYPE_PROPERTY_SIZE_MEDIUM => [
            'parent_type' => FileResource::TYPE_PROPERTY_IMAGE,
            'version_name' => 'size_medium'
        ],
        self::TYPE_PROJECT_FILE_THUMBNAIL => [
            'parent_type' => FileResource::TYPE_PROJECT_FILE,
            'version_name' => 'thumbnail'
        ],
        self::TYPE_USER_BID_THUMBNAIL => [
            'parent_type' => FileResource::TYPE_USER_IMAGE,
            'version_name' => 'bid_thumbnail'
        ],
        self::TYPE_COMPANY_DRAWING_THUMBNAIL => [
            'parent_type' => FileResource::TYPE_COMPANY_LOGO,
            'version_name' => 'drawing_thumbnail'
        ],
    ];

    public const STATUS_IN_PROGRESS = 1;
    public const STATUS_FINISHED = 2;
    public const STATUS_FAILED = 3;

    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~self::ACTION_GROUP_NESTED;

    protected $table = 'fileVariants';
    protected $model = FileVariant::class;

    protected $allow_no_user = true;

    public static function getTypes(): array
    {
        return [
            static::TYPE_DRAWING_APP_EVENT_THUMBNAIL, static::TYPE_DRAWING_EVALUATION_THUMBNAIL,
            static::TYPE_COMPANY_PROFILE_THUMBNAIL, static::TYPE_USER_PROFILE_THUMBNAIL,
            static::TYPE_COMPANY_EMAIL_THUMBNAIL, static::TYPE_COMPANY_DOCUMENT_THUMBNAIL,
            static::TYPE_USER_EMAIL_THUMBNAIL, static::TYPE_FORM_UPLOAD_FIELD_THUMBNAIL,
            static::TYPE_BID_CUSTOM_DRAWING_THUMBNAIL, static::TYPE_DRAWING_BID_THUMBNAIL,
            static::TYPE_PROPERTY_SIZE_MEDIUM, static::TYPE_PROJECT_FILE_THUMBNAIL, static::TYPE_USER_BID_THUMBNAIL,
            static::TYPE_COMPANY_DRAWING_THUMBNAIL
        ];
    }

    public static function getStatuses(): array
    {
        return [
            static::STATUS_IN_PROGRESS, static::STATUS_FINISHED, static::STATUS_FAILED
        ];
    }

    protected static function boot(): void
    {
        static::delegate(VariantDelegate::class);
    }

    public function getMediaTypeVersionByType(int $type): MediaTypeVersion
    {
        $config = self::TYPES_CONFIG[$type] ?? throw new AppException('Unable to find type config for type: %d', $type);
        /** @var FileResource $file_resource */
        $file_resource = $this->relationResource('file');
        return $file_resource->getMediaTypeByType($config['parent_type'])?->getVersion($config['version_name']);
    }

    public function findByFileIdAndType(string|UuidInterface $file_id, int $type): ?FileVariant
    {
        $file_id_field = $this->getFields()->get('file_id');
        return $this->newScopedQuery()
            ->select($this->getTableAlias() . '.*')
            ->where($this->getTableAlias() . '.fileID', $file_id_field->saveValue($file_id))
            ->where($this->getTableAlias() . '.type', $type)
            ->first();
    }

    public function getEntityByTypeAndFileID(int $type, string|UuidInterface $file_id, bool $generate = false): Entity
    {
        if (($variant = $this->findByFileIdAndType($file_id, $type)) !== null) {
            $variant = (new ScopeBuilder($this, Scope::make()))->toEntity($variant);
        } elseif ($generate) {
            $variant = Entity::make([
                'id' => Uuid::uuid4()->toString(),
                'type' => $type,
                'file_id' => (string) $file_id,
                'is_valid' => false
            ]);
        }
        return $variant;
    }

    public function invalidateByFileID(string|UuidInterface $file_id): void
    {
        $file_id = $this->getFields()->get('file_id')->saveValue($file_id);
        $primary_field = $this->getPrimaryField();
        $this->newScopedQuery()
            ->where($this->getTableAlias() . '.fileID', $file_id)
            ->get()
            ->each(function ($variant) use ($primary_field) {
                $this->update(Entity::make([
                    $primary_field->getName() => $primary_field->outputValueFromModel($variant),
                    'is_valid' => false
                ]))->partial()->run();
            });
    }

    public function deleteByFileID(string|UuidInterface $file_id, bool $delete_data = true): void
    {
        $file_id = $this->getFields()->get('file_id')->saveValue($file_id);
        $primary_field = $this->getPrimaryField();
        $this->newScopedQuery()
            ->where($this->getTableAlias() . '.fileID', $file_id)
            ->get()
            ->each(function ($variant) use ($primary_field, $delete_data) {
                $this->delete(Entity::make([
                    $primary_field->getName() => $primary_field->outputValueFromModel($variant)
                ]))
                    ->store('delete_data', $delete_data)
                    ->run();
            });
    }
}
