<?php

namespace App\Resources\Form\Item;

use App\ResourceDelegates\Form\Item\GroupDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\FormItemGroup;
use Core\Components\Resource\Classes\Resource;

class GroupResource extends Resource
{
    const TYPE_DEFAULT = 1;
    const TYPE_REPEATABLE_TABLE = 2;

    use UserActionTrackingTrait;

    protected $available_actions = (self::ACTION_GROUP_READ_ONLY | self::ACTION_GROUP_CREATE) & ~self::ACTION_GROUP_BATCH;

    protected $table = 'formItemGroups';
    protected $model = FormItemGroup::class;

    protected $allow_no_user = true;

    public static function getTypes()
    {
        return [
            static::TYPE_DEFAULT, static::TYPE_REPEATABLE_TABLE
        ];
    }

    protected static function boot()
    {
        static::delegate(GroupDelegate::class);
    }
}
