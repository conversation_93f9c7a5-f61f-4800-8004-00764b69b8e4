<?php

declare(strict_types=1);

namespace App\Resources;

use App\ResourceDelegates\IndustryDelegate;
use App\Traits\Resource\{BulkActionTrait, UserActionTrackingTrait};
use Common\Models\IntakeIndustry;
use Core\Components\Resource\Classes\Resource;

/**
 * Class IndustryResource
 *
 * @package App\Resources
 */
class IndustryResource extends Resource
{
    use BulkActionTrait;
    use UserActionTrackingTrait;

    /**
     * @var int Actions allowed on resource
     */
    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_GROUP_NESTED | self::ACTION_DELETE);

    /**
     * @var string Table name
     */
    protected $table = 'intakeIndustries';

    /**
     * @var string Model class name
     */
    protected $model = IntakeIndustry::class;

    /**
     * @var bool Determines if an authenticated user is required for resource
     */
    protected $allow_no_user = true;

    /**
     * Boot and assign delegates to resource
     */
    protected static function boot(): void
    {
        static::delegate(IndustryDelegate::class);
    }
}
