<?php

declare(strict_types=1);

namespace App\Resources\Product\Item;

use App\ResourceDelegates\Product\Item\MaterialDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\ProductItemMaterial;
use Core\Components\Resource\Classes\{Entity, Resource};
use Core\Components\Resource\Interfaces\SaveInterface;
use Core\Components\Resource\Traits\SaveTrait;

/**
 * Class MaterialResource
 *
 * @package App\Resources\Product\Item
 */
class MaterialResource extends Resource implements SaveInterface
{
    const STATUS_ACTIVE = 1;
    const STATUS_ARCHIVED = 2;

    use SaveTrait;
    use UserActionTrackingTrait;

    /**
     * @var int Actions which are allowed
     */
    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL;

    /**
     * @var string Table name
     */
    protected $table = 'productItemMaterials';

    /**
     * @var string Model associated with resource
     */
    protected $model = ProductItemMaterial::class;

    /**
     * @var bool Auto generate UUID for new entities
     */
    protected $generate_id = true;

    /**
     * @var bool Determines if resource can be used without user info
     */
    protected $allow_no_user = true;

    public static function getStatuses()
    {
        return [static::STATUS_ACTIVE, static::STATUS_ARCHIVED];
    }

    /**
     * Boot resource
     */
    protected static function boot(): void
    {
        static::delegate(MaterialDelegate::class);
    }

    public function archiveMissingMaterialsByItemID($item_id, array $material_ids)
    {
        $primary_field = $this->getPrimaryField();
        $item_id = $this->getFields()->get('item_id')->saveValue($item_id);
        $material_ids = array_map(function ($id) use ($primary_field) {
            return $primary_field->saveValue($id);
        }, $material_ids);
        $this->newScopedQuery()
            ->where('productItemMaterials.productItemID', $item_id)
            ->whereNotIn('productItemMaterials.productItemMaterialID', $material_ids)
            ->each(function ($material) use ($primary_field) {
                $this->partialUpdate(Entity::make([
                    $primary_field->getName() => $primary_field->outputValueFromModel($material),
                        'status' => static::STATUS_ARCHIVED
                ]))->run();
            });
    }

    public function archiveByItemID($item_id)
    {
        $primary_field = $this->getPrimaryField();
        $item_id = $this->getFields()->get('item_id')->saveValue($item_id);
        $this->newScopedQuery()
            ->where('productItemMaterials.productItemID', $item_id)
            ->each(function ($material) use ($primary_field) {
                $this->partialUpdate(Entity::make([
                    $primary_field->getName() => $primary_field->outputValueFromModel($material),
                    'status' => static::STATUS_ARCHIVED
                ]))->run();
            });
    }

    public function deleteMissingMaterialsByItemID($item_id, array $material_ids)
    {
        $primary_field = $this->getPrimaryField();
        $item_id = $this->getFields()->get('item_id')->saveValue($item_id);
        $material_ids = array_map(function ($id) use ($primary_field) {
            return $primary_field->saveValue($id);
        }, $material_ids);
        $this->newScopedQuery()
            ->where('productItemMaterials.productItemID', $item_id)
            ->whereNotIn('productItemMaterials.productItemMaterialID', $material_ids)
            ->each(function ($material) use ($primary_field) {
                $this->delete(Entity::make([
                    $primary_field->getName() => $primary_field->outputValueFromModel($material)
                ]))->run();
            });
    }

    public function deleteByItemID($item_id)
    {
        $primary_field = $this->getPrimaryField();
        $item_id = $this->getFields()->get('item_id')->saveValue($item_id);
        $this->newScopedQuery()
            ->where('productItemMaterials.productItemID', $item_id)
            ->each(function ($material) use ($primary_field) {
                $this->delete(Entity::make([
                    $primary_field->getName() => $primary_field->outputValueFromModel($material)
                ]))->run();
            });
    }
}
