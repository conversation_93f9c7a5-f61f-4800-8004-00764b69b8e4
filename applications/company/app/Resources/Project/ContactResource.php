<?php

declare(strict_types=1);

namespace App\Resources\Project;

use App\ResourceDelegates\Project\ContactDelegate;
use App\Traits\Resource\{BulkActionTrait, UserActionTrackingTrait};
use Common\Models\ProjectEmail;
use Core\Components\Resource\Classes\{Entity, Resource};
use Core\Components\Resource\Exceptions\{ImmutableEntityException, RequestFailedException};
use Exception;

/**
 * Class ContactResource
 *
 * @package App\Resources\Project
 */
class ContactResource extends Resource
{
    use BulkActionTrait;
    use UserActionTrackingTrait;

    /**
     * @var int Actions allowed on resource
     */
    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL;

    /**
     * @var string Table name
     */
    protected $table = 'projectEmail';

    /**
     * @var string Model class name
     */
    protected $model = ProjectEmail::class;

    /**
     * @var bool Determines if an authenticated user is required for resource
     */
    protected $allow_no_user = true;

    /**
     * Boot and assign delegates to resource
     */
    protected static function boot(): void
    {
        static::delegate(ContactDelegate::class);
    }

    /**
     * Delete all contacts for a project who's ID isn't defined in $contact_ids param
     *
     * @param int $project_id
     * @param array $contact_ids
     */
    public function deleteMissingContactsByProjectID(int $project_id, array $contact_ids): void
    {
        $query = $this->newScopedQuery()->where('projectEmail.projectID', $project_id);
        if (count($contact_ids) > 0) {
            $query->whereNotIn('projectEmail.projectEmailID', $contact_ids);
        }
        $query->each(function ($contact) {
                $this->delete(Entity::make([
                    $this->getPrimaryFieldName() => $contact->getKey()
                ]))
                    // force update to bypass any mutability checks
                    ->force()
                    ->run();
            });
    }

    /**
     * Delete all contacts by project ID
     *
     * @param int $project_id
     * @param bool $force
     */
    public function deleteByProjectID(int $project_id, bool $force = false): void
    {
        $primary_field = $this->getPrimaryField();
        $this->newScopedQuery()
            ->where($this->getTableAlias() . '.projectID', $project_id)
            ->get()
            ->each(function ($contact) use ($primary_field, $force) {
                $id = $primary_field->outputValueFromModel($contact);
                try {
                    $this->delete(Entity::make([
                        $primary_field->getName() => $id
                    ]))->force($force)->run();
                } catch (ImmutableEntityException $e) {
                    throw $this->wrapBulkActionException(new ImmutableEntityException('Contact is immutable'), $e, [
                        'project_contact_id' => $id
                    ]);
                } catch (Exception $e) {
                    throw $this->wrapBulkActionException(new RequestFailedException('Unable to delete contact'), $e, [
                        'project_contact_id' => $id
                    ]);
                }
            });
    }
}
