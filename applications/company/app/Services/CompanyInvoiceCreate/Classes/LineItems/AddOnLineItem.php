<?php

declare(strict_types=1);

namespace App\Services\CompanyInvoiceCreate\Classes\LineItems;

use App\Resources\Company\Invoice\LineItemResource;
use App\Services\CompanyInvoiceCreate\Classes\LineItem;
use Core\Components\Resource\Classes\Entity;

/**
 * Class AddOnLineItem
 *
 * @package App\Services\CompanyInvoiceCreate\Classes\LineItems
 */
class AddOnLineItem extends LineItem
{
    /**
     * @var int Line item type
     */
    protected int $type = LineItemResource::TYPE_ADD_ON;

    /**
     * @var int|null Addon id
     */
    protected ?int $addon_id = null;

    /**
     * Setup class data from array
     *
     * @param array $data
     * @throws \Core\Exceptions\AppException
     */
    public function hydrate(array $data): void
    {
        parent::hydrate($data);
        $this->importFromArray([
            'addon_id' => ['int', 'setAddonID']
        ], $data);
    }

    /**
     * Set addon id
     *
     * @param int $addon_id
     * @return $this
     */
    public function setAddonID(int $addon_id): self
    {
        $this->addon_id = $addon_id;
        return $this;
    }

    /**
     * Convert line item data into entity
     *
     * @return Entity
     * @throws \App\Services\CompanyInvoiceCreate\Exceptions\CompanyInvoiceCreateException
     * @throws \Core\Exceptions\AppException
     */
    public function toEntity(): Entity
    {
        $entity = parent::toEntity();
        $entity->set('addon_id', $this->addon_id);
        return $entity;
    }
}
