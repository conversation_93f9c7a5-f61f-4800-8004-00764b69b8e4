<?php

declare(strict_types=1);

namespace App\Services\CompanyInvoiceCreate\Classes\LineItems;

use App\Resources\Company\Invoice\LineItemResource;
use App\Services\CompanyInvoiceCreate\Classes\LineItem;

/**
 * Class DiscountLineItem
 *
 * @package App\Services\CompanyInvoiceCreate\Classes\LineItems
 */
class DiscountLineItem extends LineItem
{
    /**
     * @var int Line item type
     */
    protected int $type = LineItemResource::TYPE_DISCOUNT;

    /**
     * @var bool Determines if line item amount is negated
     */
    protected bool $negate = true;
}
