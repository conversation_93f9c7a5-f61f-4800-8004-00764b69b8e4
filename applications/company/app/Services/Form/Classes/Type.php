<?php

declare(strict_types=1);

namespace App\Services\Form\Classes;

use App\Services\Form\Exceptions\{FormException, ImportException, ValidationException};
use App\Services\Form\TemplateHelpers\FormHelper;
use App\Services\Form\Traits\{ArrayImportExportTrait, IdentifierTrait, ProductInfoTrait};
use App\Services\Template\Helpers\{ArrayHelper, ContentHelper, FormatHelper, LogicHelper};
use App\Services\TemplateService;
use Closure;
use Core\Classes\Str;
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Http\StaticAccessors\View;
use Core\Components\Resource\Classes\{Entity, Scope};
use Core\Components\Resource\Exceptions\EntityNotFoundException;
use Core\Components\Resource\Interfaces\AclInterface;
use Core\StaticAccessors\App;
use Ramsey\Uuid\Uuid;
use Throwable;

/**
 * Class Type
 *
 * Base class for all form types (company, system, etc.).
 *
 * @package App\Services\Form\Classes
 */
abstract class Type
{
    use ArrayImportExportTrait;
    use IdentifierTrait;
    use ProductInfoTrait;

    /**
     * @var self[] Cache of type instances keyed by id
     */
    protected static array $instances = [];

    /**
     * @var int[] Mapping of aliases to type values used for exports
     */
    protected static array $type_alias_map = [];

    /**
     * @var string[] Mapping of type values to their respective classes
     */
    protected static array $type_map = [];

    /**
     * @var bool Determines if setup method has been called yet
     */
    protected bool $setup = false;

    /**
     * @var int Form type
     */
    protected int $type;

    /**
     * @var string|null Polymorphic item id used for extra data related to specific form type
     */
    protected ?string $item_id = null;

    /**
     * @var string|null Name of form
     */
    protected ?string $name = null;

    /**
     * @var Structure|null Structure instance
     */
    protected ?Structure $structure = null;

    /**
     * @var string|null Form item id (sourced from structure or used to load structure)
     */
    protected ?string $form_item_id = null;

    /**
     * @var int Order which form is displayed in a list
     */
    protected int $order = 1;

    /**
     * @var array|null Configuration of form which will be replaced
     */
    protected ?array $replace_form = null;

    /**
     * Get cache file path for template code for a specific layout type
     *
     * @param int $layout_type
     * @param string $id
     * @return string
     */
    abstract public static function getTemplateCodeCacheFile(int $layout_type, string $id): string;

    /**
     * Get cache file path for template styles for specific layout type
     *
     * @param int $layout_type
     * @param string $id
     * @return string
     */
    abstract public static function getTemplateStyleCacheFile(int $layout_type, string $id): string;

    /**
     * Get allowed structure classes for this form type
     *
     * @return string[] List of class names
     */
    abstract protected static function getAllowedStructures(): array;

    /**
     * Get structure owner type for this type
     *
     * Used when creating a new structure from a type since all structures need owner info.
     *
     * @return int
     */
    abstract protected static function getStructureOwnerType(): int;

    /**
     * Get default structure type
     *
     * Used when importing raw structure data which doesn't contain type info.
     *
     * @return int
     */
    abstract protected static function getDefaultStructureType(): int;

    /**
     * Get cache file path for this type
     *
     * @param string $id UUID
     * @return string
     */
    abstract public static function getCacheFile(string $id): string;

    /**
     * Clear entire cache for form type
     */
    abstract public static function clearEntireCache(): void;

    /**
     * Clear cache files for this type
     *
     * @param string $id UUID
     */
    abstract public static function clearCacheByID(string $id): void;

    /**
     * Get type by id and return new instance
     *
     * @param AclInterface $acl
     * @param string $id UUID
     * @param bool $setup
     * @param bool $force Determines if cache is bypassed
     * @return static
     */
    abstract public static function getByID(AclInterface $acl, string $id, bool $setup = true, bool $force = false): self;

    /**
     * Create form instance from array
     *
     * Type info defined in data will be used to load proper class type from type mapping.
     *
     * @param array $data
     * @return static
     * @throws ImportException
     */
    public static function import(array $data): self
    {
        if (!isset($data['type'])) {
            throw new ImportException('No type defined');
        }
        $type = $data['type'];
        if (isset(static::$type_alias_map[$type])) {
            $type = static::$type_alias_map[$type];
        }
        if (!isset(static::$type_map[$type])) {
            throw new ImportException('Invalid type');
        }
        return (static::$type_map[$type])::make($data);
    }

    /**
     * Create new instance
     *
     * @param array $data
     * @return static
     */
    public static function make(array $data = []): self
    {
        return new static($data);
    }

    /**
     * Fetch and cache type info by id
     *
     * If type data doesn't exist in cache, it will be fetched from the database and cached. Structure will be fetched
     * independently.
     *
     * @param string $resource_class - Resource class path of type
     * @param AclInterface $acl
     * @param string $id UUID
     * @param bool $setup
     * @param bool $force If true, cache is bypassed
     * @return static
     * @throws FormException
     * @throws ImportException
     * @throws \Core\Exceptions\AppException
     */
    protected static function fetchAndCacheByID(
        string $resource_class,
        AclInterface $acl,
        string $id,
        bool $setup = true,
        bool $force = false
    ): self {
        $id = Uuid::fromString($id)->toString(); // normalize id
        if (isset(self::$instances[$id])) {
            return self::$instances[$id];
        }
        $cache_file = static::getCacheFile($id);
        $cache_miss = $force || App::debugEnabled() || !file_exists($cache_file);
        if ($cache_miss) {
            try {
                $type = $resource_class::make($acl)->entity($id)
                    ->scope(Scope::make()->format('form-v1'))
                    ->run()
                    ->toArray();
            } catch (EntityNotFoundException $e) {
                throw (new FormException('Unable to find type with id: %s', $id))->setLastException($e);
            }
        } else {
            $type = include($cache_file);
        }

        $type = static::import($type);
        if ($setup) {
            $type->setup($acl);
        }

        if ($cache_miss) {
            $cache_data = '<' . '?php return ' . var_export($type->toArray(), true) . ';' . PHP_EOL;
            if (file_put_contents($cache_file, $cache_data) === false) {
                throw new FormException('Unable to save cache file: %s', $cache_file);
            }
        }
        self::$instances[$id] = $type;
        return $type;
    }

    /**
     * Type constructor
     *
     * @param array $data Initial data
     */
    public function __construct(array $data = [])
    {
        if (count($data) > 0) {
            $this->hydrate($data);
        }
    }

    /**
     * Setup class data from array
     *
     * Used in conjunction with cache loading and importing.
     *
     * @param array $data
     * @throws FormException
     */
    public function hydrate(array $data): void
    {
        $this->importFromArray(
            [
                'id' => ['string', 'setID'],
                'name' => ['string', 'setName'],
                'form_item_id' => ['string', 'setFormItemID'],
                'item' => ['array', 'setItem'],
                'item_id' => ['string', 'setItemID'],
                'order' => ['int', 'setOrder'],
                'structure' => [
                    'array',
                    function ($structure) {
                        $this->setStructure(Structure::import($structure, static::getDefaultStructureType()));
                    }
                ]
            ],
            $data
        );
    }

    /**
     * Define related components within scoped closure
     *
     * @param Closure $with
     * @return $this
     */
    public function with(Closure $with): self
    {
        $with($this);
        return $this;
    }

    /**
     * Get type
     *
     * @return int
     */
    public function getType(): int
    {
        return $this->type;
    }

    /**
     * Set item id
     *
     * @param string $id
     * @return $this
     */
    public function setItemID(string $id): self
    {
        $this->item_id = $id;
        return $this;
    }

    /**
     * Get item id
     *
     * @return string|null
     */
    public function getItemID(): ?string
    {
        return $this->item_id;
    }

    /**
     * Set polymorphic item data
     *
     * @param array $item
     * @return static
     */
    abstract public function setItem(array $item);

    /**
     * Get polymorphic item data
     *
     * @return array|null
     */
    abstract public function getItem(): ?array;

    /**
     * Set name
     *
     * @param string $name
     * @return $this
     */
    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    /**
     * Get name
     *
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * Set structure instance
     *
     * @param Structure $structure
     * @return $this
     */
    public function setStructure(Structure $structure): self
    {
        $this->structure = $structure;
        return $this;
    }

    /**
     * Get structure instance
     *
     * @return Structure|null
     */
    public function getStructure(): ?Structure
    {
        return $this->structure;
    }

    /**
     * Set form item id
     *
     * @param string $id
     * @return $this
     */
    public function setFormItemID(string $id): self
    {
        $this->form_item_id = $id;
        return $this;
    }

    /**
     * Get form item id
     *
     * @return string|null
     */
    public function getFormItemID(): ?string
    {
        return $this->form_item_id;
    }

    /**
     * Set order
     *
     * @param int $order
     * @return $this
     */
    public function setOrder(int $order): self
    {
        $this->order = $order;
        return $this;
    }

    /**
     * Get order
     *
     * @return int
     */
    public function getOrder(): int
    {
        return $this->order;
    }

    /**
     * Set replace form info
     *
     * @param string $id UUID
     * @param bool $copy_name Determines if name from replaced form is copied to new one
     * @param bool $copy_options Determines if options from replaced form are copied to new one
     * @param bool $copy_categories Determines if categories from replaced form are coped to new one
     * @return $this
     */
    public function setReplaceForm(string $id, bool $copy_name = false, bool $copy_options = false, bool $copy_categories = false): self
    {
        $this->replace_form = compact('id', 'copy_name', 'copy_options', 'copy_categories');
        return $this;
    }

    /**
     * Get replace form info
     *
     * @return array|null
     */
    public function getReplaceForm(): ?array
    {
        return $this->replace_form;
    }

    /**
     * Search for structure component using array aliases
     *
     * @param array $path List of aliases
     * @return object|null
     * @throws FormException
     */
    public function search(array $path): ?object
    {
        if (($structure = $this->getStructure()) === null) {
            throw new FormException('Structure not defined to preform search');
        }
        return $structure->search($path);
    }

    /**
     * Get location within array format for use with debugging
     *
     * @return string
     */
    public function getLocation(): string
    {
        return 'type';
    }

    /**
     * Get lookup path of type
     *
     * @return string
     */
    public function getPath(): string
    {
        return '';
    }

    /**
     * Prepare structure for saving
     *
     * If structure is new, we pass our own product info helper and run its internal prepare method. We forcefully
     * set the form item id from structure to ensure they are properly linked.
     *
     * @param Structure $structure
     */
    protected function prepareStructure(Structure $structure): void
    {
        if (!$structure->exists) {
            $structure->setProductInfoHelper($this->getProductInfoHelper());
            $structure->prepare();
        }
        $this->setFormItemID($structure->getID());
    }

    /**
     * Prepare form for saving
     *
     * If structure isn't defined, but a form item id is, we fetch the structure independently and set it. If structure
     * is found, it is prepared as well (if new). Prepare method is used for fetching any necessary data before
     * validation is run.
     *
     * @param AclInterface $acl
     * @throws FormException
     * @throws \Core\Exceptions\AppException
     */
    public function prepare(AclInterface $acl): void
    {
        $structure = $this->getStructure();
        if ($structure === null && ($form_item_id = $this->getFormItemID()) !== null) {
            $structure = Structure::getByID($acl, $form_item_id);
            $this->setStructure($structure);
        }
        if ($structure !== null) {
            $this->prepareStructure($structure);
        }
    }

    /**
     * Validate form before saving
     *
     * Structure is verified if it allowed for this form type. If structure is new, we forcefully set its owner info
     * and run it's validate method to ensure it's valid before saving.
     *
     * @param AclInterface $acl
     * @param Structure $structure
     * @throws ValidationException
     */
    protected function validateStructure(AclInterface $acl, Structure $structure): void
    {
        if (!in_array(get_class($structure), static::getAllowedStructures())) {
            throw new ValidationException('Invalid structure type');
        }
        if (!$structure->exists) {
            $structure->setOwnerType(static::getStructureOwnerType());
            $structure->setOwnerID($this->getID());
            $structure->validate($acl);
        }
    }

    /**
     * Validate form
     *
     * Any requested product info is fetched and cached for lookup. If structure is defined, it's validated.
     *
     * @param AclInterface $acl
     * @throws ValidationException
     */
    public function validate(AclInterface $acl): void
    {
        $this->loadProductInfo($acl);
        if (($structure = $this->getStructure()) !== null) {
            $this->validateStructure($acl, $structure);
        }
    }

    /**
     * Get resource entity used to persist form
     *
     * @return Entity
     */
    public function getEntity(): Entity
    {
        return Entity::make([
            'id' => $this->getID(),
            'form_item_id' => $this->getFormItemID(),
            'type' => $this->getType(),
            'item' => $this->getItem(),
            'item_id' => $this->getItemID(),
            'name' => $this->getName(),
            'order' => $this->getOrder()
        ]);
    }

    /**
     * Determines if form type is in use
     *
     * @return bool
     */
    abstract protected function isInUse(): bool;

    /**
     * Clone form type
     *
     * @param bool $with_structure
     * @return $this
     */
    public function clone(bool $with_structure = false): self
    {
        $type = new static($this->export($with_structure));
        $type->setReplaceForm($this->getID(), false, false, true);
        return $type;
    }

    /**
     * Persist form
     *
     * If structure is new, it is saved before any type info is saved due to dependency issues. This method is
     * extended by more specific form types to handle their saving.
     *
     * @param AclInterface $acl
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     */
    public function persist(AclInterface $acl)
    {
        if (($structure = $this->getStructure()) !== null && !$structure->exists) {
            $structure->persist($acl);
        }
    }

    /**
     * Save form
     *
     * Data is prepared, validated, and persisted within a DB transaction.
     *
     * @param AclInterface $acl
     * @throws FormException
     * @throws ValidationException
     * @throws \Core\Exceptions\AppException
     */
    public function save(AclInterface $acl): self
    {
        $type = $this;
        $in_use = $type->isInUse();
        if ($in_use) {
            $type->setup($acl);
            $type = $type->clone();
        }
        $type->prepare($acl);
        $type->validate($acl);
        try {
            DB::transaction(
                function () use ($type, $acl) {
                    $type->persist($acl);
                }
            );
            if (!$in_use) {
                static::clearCacheByID($this->getID());
            }
            return $type;
        } catch (Throwable $e) {
            throw (new FormException('Unable to save form'))->setLastException($e);
        }
    }

    /**
     * Configure form to set it up for usage or output to array or client formats
     *
     * @param AclInterface $acl
     * @throws FormException
     * @throws \Core\Exceptions\AppException
     */
    protected function configure(AclInterface $acl): void
    {
        if (($form_item_id = $this->getFormItemID()) === null) {
            throw new FormException('Form item id is required to setup');
        }
        if ($this->getStructure() === null) {
            $structure = Structure::getByID($acl, $form_item_id, false);
            $this->setStructure($structure);
        }
    }

    /**
     * Setup form for usage
     *
     * Can only be run once per instance.
     *
     * @param AclInterface $acl
     * @throws FormException
     * @throws \Core\Exceptions\AppException
     */
    public function setup(AclInterface $acl): void
    {
        if ($this->setup) {
            throw new FormException('Setup has been already ran for type');
        }
        $this->configure($acl);
        $structure = $this->getStructure();
        if (!$structure->isSetup()) {
            $structure->setup($acl);
        }
        $this->setup = true;
    }

    /**
     * Setup form type for preview
     *
     * Validates and runs setup for newly generated form type to ensure it can render properly.
     *
     * @param AclInterface $acl
     * @throws FormException
     * @throws ValidationException
     * @throws \Core\Exceptions\AppException
     */
    public function preview(AclInterface $acl): void
    {
        if (!$this->exists) {
            $this->prepare($acl);
        }
        $this->validate($acl);
        $this->setup($acl);
    }

    /**
     * Convert form to array with any relationships
     *
     * Used as caching format.
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->getID(),
            'type' => $this->getType(),
            'name' => $this->getName(),
            'item' => $this->getItem(),
            'item_id' => $this->getItemID(),
            'form_item_id' => $this->getFormItemID(),
            'order' => $this->getOrder()
        ];
    }

    /**
     * Convert form into format useful for client side form library to consume
     *
     * @return array
     * @throws FormException
     */
    public function toClientFormat(): array
    {
        if (!$this->setup) {
            throw new FormException('Setup must be ran to create client format');
        }
        if (($structure = $this->getStructure()) === null) {
            throw new FormException('Structure is not defined');
        }
        return [
            'id' => $this->getID(),
            'name' => $this->getName(),
            'structure' => $structure->toClientFormat()
        ];
    }

    /**
     * Format for use in setup user interface
     *
     * @return array
     * @throws FormException
     */
    public function toSetupFormat(): array
    {
        if (!$this->setup) {
            throw new FormException('Setup must be ran to build create format');
        }
        if (($structure = $this->getStructure()) === null) {
            throw new FormException('Structure is not defined');
        }
        return [
            'id' => $this->getID(),
            'type' => $this->getType(),
            'item_id' => $this->getItemID(),
            'item' => $this->getItem(),
            'name' => $this->getName(),
            'form_item_id' => $this->getFormItemID(),
            'structure' => $structure->toSetupFormat()
        ];
    }

    /**
     * Export form to user friendly format useful to transferring info between companies or environments
     *
     * @param bool $with_structure
     * @return array
     */
    public function export(bool $with_structure = false): array
    {
        return $this->exportToArray([
            'type' => 'getType',
            'name' => 'getName',
            'item' => 'getItem',
            'structure' => function () use ($with_structure) {
                if (!$with_structure || ($structure = $this->getStructure()) === null) {
                    return null;
                }
                return $structure->export();
            },
            'form_item_id' => function () use ($with_structure) {
                if ($with_structure || ($form_item_id = $this->getFormItemID()) === null) {
                    return null;
                }
                return $form_item_id;
            }
        ], [
            'aliases' => [
                'type' => static::$type_alias_map
            ]
        ]);
    }

    /**
     * Get variables needed to build handlebars render template
     *
     * @param int $layout_type
     * @return array
     */
    public function getTemplateVars(int $layout_type): array
    {
        return [
            'id' => $this->getID(),
            'is_hidden_name' => false,
            'name' => $this->getName(),
            'classes' => []
        ];
    }

    /**
     * Build handlebars render template
     *
     * @param int $layout_type
     * @return array
     */
    public function buildTemplate(int $layout_type): array
    {
        $template = $this->getStructure()->buildTemplate($layout_type);
        $vars = $this->getTemplateVars($layout_type);
        $vars['classes'] = count($vars['classes']) > 0 ? ' ' . implode(' ', $vars['classes']) : '';
        $vars['content'] = Str::indent($template['content'], 2);
        $content = View::fetch('services.form.type', $vars)->render();
        return [
            'content' => $content,
            'styles' => $template['styles']
        ];
    }

    /**
     * Render form to HTML using entry data injected into handlebars template
     *
     * If form handlebars template is not cached, it will be generated via buildTemplate() and cached for future use.
     * Template service is used to render handlebars template with form helpers to access entry data.
     *
     * @param AclInterface $acl
     * @param int $layout_type
     * @param Entry $entry
     * @param array|null $context Additional data made available to handlebars template
     * @param bool $force If true, cache is bypassed
     * @return array
     * @throws \Core\Exceptions\AppException
     */
    public function render(
        AclInterface $acl,
        int $layout_type,
        Entry $entry,
        array $context = null,
        bool $force = false
    ): array {
        try {
            $service = new TemplateService();
            ArrayHelper::register($service);
            ContentHelper::register($acl, $service);
            FormatHelper::register($service);
            LogicHelper::register($service);
            $form = FormHelper::register($service);
            $form->addEntry($entry);

            $id = $this->getID();
            $code_cache_file = static::getTemplateCodeCacheFile($layout_type, $id);
            $style_cache_file = static::getTemplateStyleCacheFile($layout_type, $id);
            if ($force || App::debugEnabled() || !file_exists($code_cache_file) || !file_exists($style_cache_file)) {
                $template = $this->buildTemplate($layout_type);
                $code = $service->compile($template['content']);
                $style = $service->compile(implode("\n", $template['styles']));
                $service->save($code, $code_cache_file);
                $service->save($style, $style_cache_file);
            }

            return [
                'content' => $service->renderFromFile($code_cache_file, $context),
                'styles' => $service->renderFromFile($style_cache_file, $context)
            ];
        } catch (Throwable $e) {
            throw (new FormException('Unable to render form: %s', $this->getID()))->setLastException($e);
        }
    }
}
