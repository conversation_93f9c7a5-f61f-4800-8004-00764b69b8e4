<?php

declare(strict_types=1);

namespace App\Services\Form\Components\EntryFields;

use App\Resources\Form\Item\Entry\Group\FieldOptionResource;
use App\Services\Form\Classes\Entry\Group\{Field, Item};

/**
 * Class OptionEntryField
 *
 * @package App\Services\Form\Components\EntryFields
 */
class OptionEntryField extends Field
{
    /**
     * @var Field\Option[] List of defined options
     */
    protected array $options = [];

    /**
     * Reset class after clone operation
     */
    protected function __clone()
    {
        $this->options = [];
    }

    /**
     * Clone field and all nested options
     *
     * @param Item $item
     * @return $this
     */
    public function clone(Item $item): self
    {
        $field = parent::clone($item);
        foreach ($this->options as $option) {
            $field->addOption($option->clone($field));
        }
        return $field;
    }

    /**
     * Setup class data from array
     *
     * Used in conjunction with cache loading and importing.
     *
     * @param array $data
     * @throws \App\Services\Form\Exceptions\FormException
     */
    public function hydrate(array $data): void
    {
        parent::hydrate($data);
        $this->importFromArray([
            'options' => ['list', fn($option) => Field\Option::make($this, $option)]
        ], $data);
    }

    /**
     * Add option
     *
     * @param Field\Option $option
     */
    public function addOption(Field\Option $option): void
    {
        $this->options[] = $option;
    }

    /**
     * Get all options
     *
     * @return Field\Option[]
     */
    public function getOptions(): array
    {
        return $this->options;
    }

    /**
     * Validate options before saving
     *
     * @throws \App\Services\Form\Exceptions\ValidationException
     */
    public function validate(): void
    {
        $options = $this->getOptions();
        if (count($options) > 0) {
            foreach ($options as $option) {
                $option->validate();
            }
        }
    }

    /**
     * Persist options
     *
     * Fields are just containers for values and aren't saved directly to keep database structure cleaner.
     *
     * @param FieldOptionResource $resource
     * @throws \Core\Exceptions\AppException
     */
    public function persist(FieldOptionResource $resource): void
    {
        $options = $this->getOptions();
        if (count($options) > 0) {
            foreach ($options as $option) {
                $option->persist($resource);
            }
        }
    }

    /**
     * Convert into format useful for client side form library to consume
     *
     * @return array
     */
    public function toClientFormat(): array
    {
        return [
            'options' => array_map(fn($option) => $option->getOptionID(), $this->options)
        ];
    }
}
