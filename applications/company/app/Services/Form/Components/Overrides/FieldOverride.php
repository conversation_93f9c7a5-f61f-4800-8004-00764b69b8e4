<?php

declare(strict_types=1);

namespace App\Services\Form\Components\Overrides;

use App\Resources\Form\Item\OverrideResource;
use App\Services\Form\Classes\Structure\{Group\Field, Override};
use App\Services\Form\Exceptions\ValidationException;

/**
 * Class FieldOverride
 *
 * @package App\Services\Form\Components\Overrides
 */
class FieldOverride extends Override
{
    /**
     * @var int Override type
     */
    protected int $type = OverrideResource::TYPE_FIELD;

    /**
     * Set associated field
     *
     * This is an alias for setItem().
     *
     * @param Field $field
     * @return $this
     */
    public function setField(Field $field): self
    {
        return $this->setItem($field);
    }

    /**
     * Validate before save
     *
     * @throws ValidationException
     */
    public function validate(): void
    {
        parent::validate();
        if (($item_id = $this->getItemID()) !== null) {
            if (($field = $this->structure->getField($item_id, deep: true)) === null) {
                throw new ValidationException('Unable to find field with id: %s', $item_id);
            }
            // override item id in case user provided alias
            $this->setItemID($field->getID());
        }
    }

    /**
     * Configure override to set it up for usage or output to array or client formats
     *
     * If item id is available and associated field is found, we set the item instance.
     */
    protected function configure(): void
    {
        if (($item_id = $this->getItemID()) !== null && ($field = $this->structure->getField($item_id, deep: true)) !== null) {
            $this->setItem($field, true);
        }
    }
}
