<?php

declare(strict_types=1);

namespace App\Services\Form\Components\RuleConditionSideValues;

use App\Services\Form\Classes\Structure\Group\Rule\Condition\SideValue;
use App\Services\Form\Interfaces\Structure\Group\Rule\Condition\ContextSideValueInterface;

/**
 * Class ContextSideValue
 *
 * @package App\Services\Form\Components\RuleConditionSideValues
 */
class ContextSideValue extends SideValue implements ContextSideValueInterface
{
    public const KEY_LEFT = 'context';
    public const KEY_LEFT_ALT = 'context_1';
    public const KEY_RIGHT = 'context_2';

    /**
     * @var string|null Key for left side of conditional
     */
    protected ?string $left_key = self::KEY_LEFT;

    /**
     * @var string|null Key for right side of conditional
     */
    protected ?string $right_key = self::KEY_RIGHT;

    /**
     * Helper method to create new instance of side value
     *
     * @param string $path
     * @return static
     */
    public static function make(string $path): static
    {
        return new static($path);
    }

    /**
     * Create instance from conditional value
     *
     * @param string $path
     * @return static
     */
    public static function fromValue(string $path): static
    {
        return static::make($path);
    }

    /**
     * ContextSideValue constructor
     *
     * @param string $path
     */
    public function __construct(protected string $path)
    {}

    /**
     * Convert value into storage format for conditional
     *
     * @return string
     */
    public function toValue(): string
    {
        return $this->path;
    }
}
