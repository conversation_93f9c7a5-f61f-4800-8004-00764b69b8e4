<?php

declare(strict_types=1);

namespace App\Services\Form\Helpers;

use App\Services\Form\Exceptions\FormException;
use App\Services\Form\Interfaces\OrderableInterface;

/**
 * Class OrderingHelper
 *
 * @package App\Services\Form\Helpers
 */
class OrderingHelper
{
    /**
     * Sort orderable items by their order value from lowest to highest
     *
     * @param OrderableInterface[] $items
     * @return OrderableInterface[]
     */
    public static function sortItems(array $items)
    {
        usort($items, function (OrderableInterface $a, OrderableInterface $b) {
            if (($order_a = $a->getOrder()) === null) {
                throw new FormException('No order defined for item a in sort');
            }
            if (($order_b = $b->getOrder()) === null) {
                throw new FormException('No order defined for item b in sort');
            }
            return $order_a === $order_b ? 0 : ($order_a > $order_b ? 1 : -1);
        });
        return $items;
    }

    /**
     * Verify order values of items are in sequential order
     *
     * @param OrderableInterface[] $items
     * @return bool
     */
    public static function isSequential(array $items): bool
    {
        return array_map(fn($item) => $item->getOrder(), $items) === range(1, count($items));
    }
}
