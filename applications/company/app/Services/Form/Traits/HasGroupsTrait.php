<?php

declare(strict_types=1);

namespace App\Services\Form\Traits;

use App\Services\Form\Classes\Structure\Group;
use App\Services\Form\Exceptions\FormException;
use Closure;

/**
 * Trait HasGroupsTrait
 *
 * @package App\Services\Form\Traits
 */
trait HasGroupsTrait
{
    /**
     * @var Group[] List of defined groups
     */
    protected array $groups = [];

    /**
     * Register aliases with class for use with searching and lookup
     *
     * @param object $item
     * @param array $aliases
     */
    abstract public function registerAliases(object $item, array $aliases): void;

    /**
     * Get component instance by id or alias
     *
     * @param string $id
     * @return object|null
     */
    abstract public function get(string $id): ?object;

    /**
     * Find component in this class or any nested group using an alias path or specific id
     *
     * @param string $id Dot separated alias path or id
     * @return object|null
     */
    abstract public function find(string $id): ?object;

    /**
     * Define related groups within scoped closure
     *
     * @param Closure $with
     * @return $this
     */
    public function withGroups(Closure $with): self
    {
        return $this->with($with);
    }

    /**
     * Search for nested component using array aliases
     *
     * @param array $path List of aliases
     * @return object|null
     * @throws FormException
     */
    public function search(array $path): ?object
    {
        $count = count($path);
        if ($count === 1) {
            [$id] = $path;
            if (($item = $this->get($id)) === null) {
                foreach ($this->getGroups() as $group) {
                    if (($item = $group->search($path)) === null) {
                        continue;
                    }
                    break;
                }
            }
            return $item;
        }
        if ($count > 1) {
            if (($group = $this->getGroup(array_shift($path))) === null) {
                return null;
            }
            return $group->search($path);
        }
        throw new FormException('Invalid ID passed');
    }

    /**
     * Add group to component
     *
     * @param Group $group
     * @param string|null $alias
     */
    public function addGroup(Group $group, ?string $alias = null): void
    {
        $this->groups[] = $group;
        $aliases = [$group->getID()];
        if ($alias !== null) {
            $aliases[] = $alias;
        }
        $this->registerAliases($group, $aliases);
    }

    /**
     * Get all groups
     *
     * @return Group[]
     */
    public function getGroups(): array
    {
        return $this->groups;
    }

    /**
     * Get index of specified group
     *
     * @param Group $group
     * @return int
     */
    public function getGroupIndex(Group $group): int
    {
        return array_search($group, $this->groups, true);
    }

    /**
     * Get group by id or alias
     *
     * If deep is true, it will search nested groups for the group alias as well.
     *
     * @param string $id UUID or alias
     * @param bool $deep Determines if we search nested groups
     * @return Group|null
     */
    public function getGroup(string $id, bool $deep = false): ?Group
    {
        return ($group = $this->{$deep ? 'find' : 'get'}($id)) !== null && $group instanceof Group ? $group : null;
    }
}
