<?php

declare(strict_types=1);

namespace App\Services\GoogleApi\Calendar\Classes\Calendar;

use App\Services\GoogleApi\Calendar\Exceptions\Api\{
    ForbiddenException,
    Gone\ResourceDeletedException,
    ResourceNotFoundException
};
use App\Services\GoogleApi\Calendar\Exceptions\{EventDeletedException, EventDetailMismatchException};
use App\Services\GoogleApi\Calendar\Interfaces\EventDetailInterface;
use App\Services\GoogleApi\Calendar\Classes\Calendar;
use App\Services\GoogleApi\Services\CalendarService;
use Carbon\Carbon;
use Common\Models\GoogleCalendarEvent;
use Google_Service_Calendar;
use Google_Service_Calendar_Event;
use Monolog\Logger;
use Ramsey\Uuid\{Uuid, UuidInterface};

/**
 * Class Event
 *
 * @package App\Services\GoogleApi\Calendar\Classes\Calendar
 */
class Event
{
    public const TYPE_PROJECT = 1;
    public const TYPE_USER = 2;
    public const TYPE_COMPANY = 3;

    public const ACTION_PULLED = 1;
    public const ACTION_PUSHED = 2;

    /**
     * @var Logger|null Logger instance
     */
    protected static ?Logger $logger = null;

    /**
     * @var array Mapping of type to model type
     */
    protected static array $type_map = [
        self::TYPE_PROJECT => GoogleCalendarEvent::TYPE_PROJECT,
        self::TYPE_USER => GoogleCalendarEvent::TYPE_USER,
        self::TYPE_COMPANY => GoogleCalendarEvent::TYPE_COMPANY
    ];

    /**
     * @var Calendar Calendar instance
     */
    protected Calendar $calendar;

    /**
     * @var GoogleCalendarEvent Model instance
     */
    protected GoogleCalendarEvent $event;

    /**
     * @var EventDetailInterface|null Detail instance
     */
    protected ?EventDetailInterface $detail = null;

    /**
     * Get Log instance configured for Google Api service
     *
     * @return Logger
     */
    public static function getLog(): Logger
    {
        if (static::$logger === null) {
            static::$logger = CalendarService::createLogger('event');
        }
        return static::$logger;
    }

    /**
     * Get available types
     *
     * @return int[]
     */
    public static function getTypes(): array
    {
        return [self::TYPE_PROJECT, self::TYPE_USER, self::TYPE_COMPANY];
    }

    /**
     * Create Google calendar API library entity from detail instance
     *
     * @param EventDetailInterface $event_detail
     * @return Google_Service_Calendar_Event
     */
    protected static function createEntity(EventDetailInterface $event_detail): Google_Service_Calendar_Event
    {
        $all_day = $event_detail->isAllDay();
        $start_at = $event_detail->getStart();
        $end_at = $event_detail->getEnd();
        return new Google_Service_Calendar_Event([
            'summary' => $event_detail->getTitle(),
            'location' => $event_detail->getLocation(),
            'description' => $event_detail->getDescription(),
            'start' => [
                'date' => $all_day ? $start_at->format('Y-m-d') : null,
                'dateTime' => !$all_day ? $start_at->toIso8601String() : null,
                'timeZone' => $start_at->getTimezone()->getName()
            ],
            'end' => [
                'date' => $all_day ? $end_at->addDay()->startOfDay()->format('Y-m-d') : null,
                'dateTime' => !$all_day ? $end_at->toIso8601String() : null,
                'timeZone' => $end_at->getTimezone()->getName()
            ]
        ]);
    }

    /**
     * Pull event into local storage
     *
     * @param Calendar $calendar
     * @param EventDetailInterface $event_detail
     * @param string $google_id
     * @param UuidInterface|null $hit_id
     * @return static
     */
    public static function pull(Calendar $calendar, EventDetailInterface $event_detail, string $google_id, ?UuidInterface $hit_id = null): self
    {
        $event = GoogleCalendarEvent::create([
            'googleCalendarEventID' => Uuid::uuid4()->getBytes(),
            'googleCalendarID' => $calendar->getID()->getBytes(),
            'itemType' => static::$type_map[$event_detail->getType()],
            'itemID' => $event_detail->getItemID(),
            'googleID' => $google_id,
            'action' => self::ACTION_PULLED,
            'googleCalendarNotificationChannelHitID' => $hit_id !== null ? $hit_id->getBytes() : null
        ]);
        return new static($calendar, $event, $event_detail);
    }

    /**
     * Push event to Google calendar and store in local storage
     *
     * @param Calendar $calendar
     * @param EventDetailInterface $event_detail
     * @return static
     * @throws ResourceNotFoundException
     * @throws \App\Services\GoogleApi\Calendar\Exceptions\ApiException
     * @throws \App\Services\GoogleApi\Exceptions\OAuth\DisconnectedException
     * @throws \Core\Exceptions\AppException
     */
    public static function push(Calendar $calendar, EventDetailInterface $event_detail): self
    {
        /** @var Google_Service_Calendar_Event $entity */
        $entity = $calendar->callApi(function (Google_Service_Calendar $service) use ($calendar, $event_detail) {
            return $service->events->insert($calendar->getGoogleID(), self::createEntity($event_detail));
        }, true);
        $event = GoogleCalendarEvent::create([
            'googleCalendarEventID' => Uuid::uuid4()->getBytes(),
            'googleCalendarID' => $calendar->getID()->getBytes(),
            'itemType' => static::$type_map[$event_detail->getType()],
            'itemID' => $event_detail->getItemID(),
            'googleID' => $entity->getId(),
            'action' => self::ACTION_PUSHED
        ]);
        return new static($calendar, $event, $event_detail);
    }

    /**
     * Get event by item type and id
     *
     * @param Calendar $calendar
     * @param int $type
     * @param int $id
     * @return Event|null
     */
    public static function getByItemTypeAndID(Calendar $calendar, int $type, int $id): ?Event
    {
        /** @var GoogleCalendarEvent $event */
        $event = GoogleCalendarEvent::query()
            ->where('googleCalendarID', $calendar->getID()->getBytes())
            ->where('itemType', static::$type_map[$type])
            ->where('itemID', $id)
            ->first();
        return $event !== null ? new static($calendar, $event) : null;
    }

    /**
     * Event constructor
     *
     * @param Calendar $calendar
     * @param GoogleCalendarEvent $event
     * @param EventDetailInterface|null $event_detail
     */
    public function __construct(Calendar $calendar, GoogleCalendarEvent $event, ?EventDetailInterface $event_detail = null)
    {
        $this->calendar = $calendar;
        $this->event = $event;
        if ($event_detail !== null) {
            $this->detail = $event_detail;
        }
    }

    /**
     * Get id
     *
     * @return UuidInterface
     */
    public function getID(): UuidInterface
    {
        return $this->event->getUuidKey();
    }

    /**
     * Get type
     *
     * @return int
     */
    public function getType(): int
    {
        return array_search($this->event->itemType, static::$type_map);
    }

    /**
     * Get detail
     *
     * @return EventDetailInterface
     */
    public function getDetail(): EventDetailInterface
    {
        if ($this->detail === null) {
            $this->detail = EventDetail::load($this->getType(), $this->event->item, $this->calendar->getTimezone());
        }
        return $this->detail;
    }

    /**
     * Get action
     *
     * @return int
     */
    public function getAction(): int
    {
        return $this->event->action;
    }

    /**
     * Determine if event is cancelled
     *
     * @return bool
     */
    public function isCancelled(): bool
    {
        return $this->event->isCancelled;
    }

    /**
     * Update event with new detail info
     *
     * @param EventDetailInterface $event_detail
     * @throws EventDeletedException
     * @throws EventDetailMismatchException
     * @throws \App\Services\GoogleApi\Calendar\Exceptions\ApiException
     * @throws \App\Services\GoogleApi\Exceptions\OAuth\DisconnectedException
     * @throws \Core\Exceptions\AppException
     */
    public function update(EventDetailInterface $event_detail): void
    {
        if (static::$type_map[$event_detail->getType()] !== $this->event->itemType) {
            throw new EventDetailMismatchException('Event detail type does not match the existing type');
        }
        if ($event_detail->getItemID() !== $this->event->itemID) {
            throw new EventDetailMismatchException('Event detail item id does not match the existing id');
        }
        if ($this->getAction() === GoogleCalendarEvent::ACTION_PUSHED && !$this->isCancelled()) {
            try {
                $this->calendar->callApi(function (Google_Service_Calendar $service) use ($event_detail) {
                    return $service->events->update($this->calendar->getGoogleID(), $this->event->googleID, self::createEntity($event_detail));
                });
            } catch (ResourceDeletedException | ForbiddenException | ResourceNotFoundException $e) {
                $this->delete(true);
                self::getLog()->info('Unable to update event on Google', [
                    'exception' => $e,
                    'calendar_id' => $this->calendar->getID()->toString(),
                    'google_id' => $this->event->googleID
                ]);
                throw new EventDeletedException('Event %s does not exist in Google', $this->event->googleID);
            }
        }
        $this->event->fill([
            'actionCount' => $this->event->actionCount + 1,
            'lastActivityAt' => Carbon::now('UTC')
        ])->save();
        $this->detail = $event_detail;
    }

    /**
     * Cancel event
     *
     * If pushed, then we just mark it as cancelled in the database. Otherwise, it is deleted.
     *
     * @throws \Exception
     */
    public function cancel(): void
    {
        if ($this->event->action === GoogleCalendarEvent::ACTION_PUSHED) {
            if ($this->isCancelled()) {
                return;
            }
            $this->event->fill([
                'isCancelled' => true,
                'cancelledAt' => Carbon::now('UTC')
            ])->save();
            return;
        }
        $this->event->item->delete();
        $this->event->delete();
    }

    /**
     * Uncancel event
     */
    public function uncancel(): void
    {
        if ($this->event->action !== GoogleCalendarEvent::ACTION_PUSHED && !$this->isCancelled()) {
            return;
        }
        $this->event->fill([
            'isCancelled' => false,
            'cancelledAt' => null
        ])->save();
    }

    /**
     * Delete event
     *
     * If force is true, not Google API calls are made.
     *
     * @param bool $force
     * @throws \App\Services\GoogleApi\Calendar\Exceptions\ApiException
     * @throws \App\Services\GoogleApi\Exceptions\OAuth\DisconnectedException
     * @throws \Core\Exceptions\AppException
     */
    public function delete(bool $force = false): void
    {
        if ($this->getAction() === GoogleCalendarEvent::ACTION_PULLED) {
            $this->event->item->delete();
            $this->event->delete();
            return;
        }
        if (!$force) {
            try {
                $this->calendar->callApi(function (Google_Service_Calendar $service) {
                    return $service->events->delete($this->calendar->getGoogleID(), $this->event->googleID);
                });
            } catch (ResourceDeletedException | ForbiddenException | ResourceNotFoundException $e) {
                // ignore if already deleted
                self::getLog()->info('Unable to delete event from Google', [
                    'exception' => $e,
                    'calendar_id' => $this->calendar->getID()->toString(),
                    'google_id' => $this->event->googleID
                ]);
            }
        }
        $this->event->delete();
    }
}
