<?php

namespace App\Services\GoogleApi\Calendar\Interfaces;

use Carbon\Carbon;
use Common\Classes\DB\Model;

/**
 * Interface EventDetailInterface
 *
 * @package App\Services\GoogleApi\Calendar\Interfaces
 */
interface EventDetailInterface
{
    /**
     * Get type
     *
     * @return int
     */
    public function getType(): int;

    /**
     * Get id of associated item
     *
     * @return int
     */
    public function getItemID(): int;

    /**
     * Get title
     *
     * @return string|null
     */
    public function getTitle(): ?string;

    /**
     * Get location
     *
     * @return string|null
     */
    public function getLocation(): ?string;

    /**
     * Get description
     *
     * @return string|null
     */
    public function getDescription(): ?string;

    /**
     * Determines if event is all day
     *
     * @return bool
     */
    public function isAllDay(): bool;

    /**
     * Get start
     *
     * @return Carbon
     */
    public function getStart(): Carbon;

    /**
     * Get end
     *
     * @return Carbon
     */
    public function getEnd(): Carbon;

    /**
     * Get underlying model
     *
     * @return Model
     */
    public function getModel(): Model;
}
