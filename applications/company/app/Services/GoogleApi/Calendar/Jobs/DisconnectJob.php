<?php

declare(strict_types=1);

namespace App\Services\GoogleApi\Calendar\Jobs;

use App\Attributes\GoogleApiJobAttribute;
use App\Services\GoogleApi\Exceptions\OAuth\DisconnectedException as OAuthDisconnectedException;
use App\Services\GoogleApi\Exceptions\Service\DisconnectedException as ServiceDisconnectedException;
use App\Services\GoogleApi\Services\CalendarService;
use App\Services\GoogleApiService;
use Core\Attributes\SerializeIgnoreAttribute;
use Core\Components\Queue\Classes\Job;
use Core\Components\Queue\Exceptions\JobFailedException;
use Throwable;

/**
 * Class DisconnectJob
 *
 * @package App\Services\GoogleApi\Calendar\Jobs
 */
#[GoogleApiJobAttribute(type: 12, timeout: 300, max_tries: 8)]
class DisconnectJob extends Job
{
    /**
     * @var CalendarService|null Calendar service instance
     */
    #[SerializeIgnoreAttribute]
    protected ?CalendarService $calendar_service = null;

    /**
     * DisconnectJob constructor
     *
     * @param int $user_id
     */
    public function __construct(protected int $user_id)
    {}

    /**
     * Handle job
     *
     * Disconnects user from Google Calendar service.
     *
     * @throws \App\Services\GoogleApi\Exceptions\GoogleApiException
     * @throws \App\Services\GoogleApi\Exceptions\UserNotFoundException
     * @throws JobFailedException
     */
    public function handle(): void
    {
        $google_api = GoogleApiService::makeFromUserID($this->user_id);
        $this->calendar_service = new CalendarService($google_api);
        try {
            $this->calendar_service->disconnect(false, false);
        } catch (ServiceDisconnectedException | OAuthDisconnectedException $e) {
            throw (new JobFailedException('Calendar service already disconnected'))->setLastException($e);
        }
    }

    /**
     * Handle failed job
     *
     * @param Throwable $e
     * @param int $tries
     */
    public function failed(Throwable $e, int $tries): void
    {
        if ($this->calendar_service === null) {
            return;
        }
        $this->calendar_service->failDisconnect();
    }

    /**
     * Run code when job is retried after failure
     *
     * @throws ServiceDisconnectedException
     * @throws \App\Services\GoogleApi\Exceptions\Service\DisconnectInProgressException
     * @throws \App\Services\GoogleApi\Exceptions\UserNotFoundException
     */
    public function onRequeue(): void
    {
        $google_api = GoogleApiService::makeFromUserID($this->user_id);
        (new CalendarService($google_api))->startDisconnect();
    }
}
