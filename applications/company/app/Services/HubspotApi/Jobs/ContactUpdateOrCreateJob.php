<?php

declare(strict_types=1);

namespace App\Services\HubspotApi\Jobs;

use App\Attributes\JobAttribute;
use App\Services\HubspotApi\Exceptions\Api\ServiceException;
use App\Services\HubspotApiService;
use Common\Models\User;
use Core\Components\Queue\Classes\Job;
use Core\Components\Queue\Exceptions\JobFailedException;
use Throwable;

/**
 * Class ContactUpdateOrCreateJob
 *
 * @package App\Services\HubspotApi\Jobs
 */
#[JobAttribute(type: 40, channel: 'hubspot-api')]
class ContactUpdateOrCreateJob extends Job
{
    /**
     * ContactUpdateOrCreateJob constructor
     *
     * @param int $user_id
     * @param bool $sync_with_deal
     */
    public function __construct(protected int $user_id, protected bool $sync_with_deal = true)
    {}

    /**
     * Handle job
     *
     * @throws JobFailedException
     */
    public function handle(): void
    {
        if (($user = User::find($this->user_id)) === null) {
            throw new JobFailedException('Unable to find user: %d', $this->user_id);
        }
        try {
            $service = new HubspotApiService();
            $service->pushContact($user, $this->sync_with_deal);
        } catch (ServiceException $e) {
            throw $e;
        } catch (Throwable $e) {
            throw (new JobFailedException('Unable to push contact for user: %d', $this->user_id))->setLastException($e);
        }
    }
}
