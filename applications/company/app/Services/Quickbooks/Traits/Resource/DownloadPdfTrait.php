<?php

declare(strict_types=1);

namespace App\Services\Quickbooks\Traits\Resource;

use App\Services\QuickbooksService;
use QuickBooksOnline\API\Data\IPPIntuitEntity;

/**
 * Trait DownloadPdfTrait
 *
 * Resource trait to allow for downloading of PDF. Kept separate since not all resources allow this.
 *
 * @package App\Services\Quickbooks\Traits\Resource
 */
trait DownloadPdfTrait
{
    /**
     * Get service instance
     *
     * @return QuickbooksService
     */
    abstract public function getService(): QuickbooksService;

    /**
     * Convert array of data into entity object
     *
     * @param array $data
     * @return IPPIntuitEntity
     */
    abstract public function getEntity(array $data): IPPIntuitEntity;

    /**
     * Download PDF from Quickbooks for resource by id
     *
     * @param string $id
     * @return string path to temporary file
     * @throws \App\Services\Quickbooks\Exceptions\OAuth\DisconnectedException
     * @throws \App\Services\Quickbooks\Exceptions\QuickbooksException
     * @throws \Core\Exceptions\AppException
     * @throws \QuickBooksOnline\API\Exception\SdkException
     */
    public function downloadPdf(string $id): string
    {
        return $this->getService()->downloadPdf($this->getEntity(['Id' => $id]));
    }
}
