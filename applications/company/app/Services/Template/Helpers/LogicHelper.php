<?php

declare(strict_types=1);

namespace App\Services\Template\Helpers;

use App\Services\TemplateService;

/**
 * Class LogicHelper
 *
 * Handlebars helper to expand logic abilities within template.
 *
 * @package App\Services\Template\Helpers
 */
class LogicHelper
{
    /**
     * Register helpers with template service
     *
     * @param TemplateService $service
     * @throws \App\Services\Template\Exceptions\TemplateException
     */
    public static function register(TemplateService $service): void
    {
        $helpers = [
            'ifeq' => 'ifEquals'
        ];
        foreach ($helpers as $name => $method) {
            $service->registerHelper($name, [static::class, $method]);
        }
    }

    /**
     * Compare to values for equality
     *
     * If equal, main block is returned. Otherwise, inverse is sent.
     *
     * @param mixed $value_1
     * @param mixed $value_2
     * @param array $options
     * @return string
     */
    public static function ifEquals($value_1, $value_2, $options)
    {
        if ($value_1 === $value_2) {
            return $options['fn']();
        } else {
            return $options['inverse']();
        }
    }
}
