<?php

declare(strict_types=1);

namespace App\Services\Training\Jobs;

use App\Attributes\JobAttribute;
use App\Services\TrainingService;
use Common\Models\User;
use Core\Components\Queue\Classes\Job;
use Core\Components\Queue\Exceptions\JobFailedException;
use Throwable;

/**
 * Class CompleteActionJob
 *
 * @package App\Services\Training\Jobs
 */
#[JobAttribute(type: 43)]
class CompleteActionJob extends Job
{
    /**
     * CompleteActionJob constructor
     *
     * @param int $user_id
     * @param int|array<int> $action
     */
    public function __construct(protected int $user_id, protected int|array $action)
    {}

    /**
     * Handle job
     *
     * @throws JobFailedException
     */
    public function handle(): void
    {
        if (($user = User::find($this->user_id)) === null) {
            throw new JobFailedException('Unable to find user: %d', $this->user_id);
        }
        try {
            $service = new TrainingService($user);
            $service->completeAction($this->action);
        } catch (Throwable $e) {
            throw (new JobFailedException('Unable to complete actions for user: %d', $this->user_id))
                ->store('_context.action', $this->action)
                ->setLastException($e);
        }
    }
}
