<?php

namespace App\Services\Wisetack\Api;

use App\Services\Wisetack\DTOs\PrequalDTO;
use App\Services\Wisetack\Exceptions\ApiRequestException;
use App\Services\Wisetack\Helpers\WisetackPricingPlan;
use Common\Models\WisetackPrequalLink;
use Exception;
use Ramsey\Uuid\Uuid;
use GuzzleHttp\Exception\GuzzleException;

class PrequalApiService {


    /**
     * Sends a prequalification request to the Wisetack API.
     *
     * @param PrequalDTO $dto The data transfer object containing the prequalification data.
     * @return WisetackPrequalLink The response from the Wisetack API.
     * @throws ApiRequestException If there is an error in the HTTP request.
     */
    public function createPrequalificationLink(PrequalDTO $dto): WisetackPrequalLink
    {
        $payload = [];
        try {
            $merchant_id = $dto->getMerchantIdWithDashes();
            $payload = $dto->generateLoanPrequalData();

            $client = WisetackApiHelper::getHttpClient();
            $endpoint = WisetackApiHelper::$MERCHANTS_ENDPOINT . '/' . $merchant_id . '/prequallinks';

            $response = $client->request('POST', $endpoint, ['json' => $payload]);
            $body = json_decode($response->getBody()->getContents(), true);

            if (!in_array($response->getStatusCode(), [200, 201, 202])) {
                throw new Exception("Unexpected status code: {$response->getStatusCode()}");
            }

            return $this->persist($dto, $body);

        } catch (Exception|GuzzleException $e) {
            WisetackApiHelper::handleApiException($e, $payload, ['merchant' => $merchant_id]);
        }
    }

    /**
    * Persists a new prequalification link based on the provided data.
     *
    * @param PrequalDTO $dto
    * @param array $body
    * @return WisetackPrequalLink
    */
    protected function persist(PrequalDTO $dto, array $body): WisetackPrequalLink
    {
        $merchant_id = UUID::fromString($body['merchantId'])->getBytes();
        $pricing_plan = WisetackPricingPlan::PRICING_PLAN_STANDARD;

        try {
            $pricing_plan = WisetackPricingPlan::fetchPricingPlan($merchant_id);
        } catch (Exception $e) {
            WisetackApiHelper::getLog()->warning(
                "Error fetching pricing plan while creating a prequal for merchantId: {$body['merchantId']} | {$e->getMessage()}"
            );
        }

        $prequal_link = WisetackPrequalLink::create([
            'wisetackPrequalID' => $body['prequalId'],
            'wisetackMerchantID' => $merchant_id,
            'customerUUID' => $dto->getCustomerUUID(),
            'projectID' => $dto->getProjectId(),
            'link' => $body['prequalLink'],
            'signupID' => $body['signupId'],
            'checksum' => $body['checksum'],
            'pricingPlan' => $pricing_plan
        ]);



        $prequal_link->save();
        return $prequal_link;
    }
}



