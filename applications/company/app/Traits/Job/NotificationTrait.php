<?php

declare(strict_types=1);

namespace App\Traits\Job;

use Common\Models\Notification;
use Ramsey\Uuid\{Uuid, UuidInterface};

/**
 * Trait NotificationTrait
 *
 * @package App\Traits\Job
 */
trait NotificationTrait
{
    /**
     * Create and save notification model for type and specified item UUID
     *
     * @param int $type
     * @param UuidInterface $item_id
     * @return Notification
     */
    protected function createNotification(int $type, UuidInterface $item_id): Notification
    {
        return Notification::create([
            'notificationID' => Uuid::uuid4()->getBytes(),
            'type' => $type,
            'itemID' => $item_id->getBytes()
        ]);
    }
}
