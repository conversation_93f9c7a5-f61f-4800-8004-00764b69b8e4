<?php

namespace App\Traits\Resource\Controller;

use App\Exceptions\ApiException;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Exceptions\AppException;
use Throwable;

trait BaseTrait
{
    protected function logException(Throwable $e)
    {
        ApiException::log($e);
    }

    protected function handleException(Throwable $e)
    {
        throw ApiException::fromException($e);
    }

    protected function getMaxVersion(): ?int
    {
        return $this->max_version ?? null;
    }

    /**
     * @param int $version
     * @return \Core\Components\Resource\Classes\Resource
     * @throws AppException
     */
    protected function getResource(int $version = 1): \Core\Components\Resource\Classes\Resource
    {
        if (!isset($this->resource)) {
            throw new AppException('Resource not defined');
        }
        return new $this->resource(Auth::acl(), $version);
    }
}
