<?php

namespace App\Traits\Resource\Controller;

use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Http\Requests\ResourceRequest;
use Exception;

trait PolyActionTrait
{
    use ActionTrait;

    public function store(ResourceRequest $request)
    {
        try {
            $request = $this->handleRequest($request);
            $entity = $this->getRequestResource($request)->polyCreate($request->entity())
                ->scope($request->getScope(false))
                ->run();
            return Response::api()->fromEntity($entity)->statusCode(201);
        } catch (Exception $e) {
            $this->handleException($e);
        }
    }

    public function update($id, ResourceRequest $request)
    {
        try {
            $request = $this->handleRequest($request);
            $resource = $this->getRequestResource($request);
            $entity = $request->entity();
            $entity->set($resource->getPrimaryFieldName(), $id);
            $update_request = $resource->polyUpdateOrCreate($entity)
                ->scope($request->getScope(false));
            $entity = $update_request->run();
            return Response::api()->fromEntity($entity);
        } catch (Exception $e) {
            $this->handleException($e);
        }
    }

    public function partialUpdate($id, ResourceRequest $request)
    {
        try {
            $request = $this->handleRequest($request);
            $resource = $this->getRequestResource($request);
            $entity = $request->entity();
            $entity->set($resource->getPrimaryFieldName(), $id);
            $update_request = $resource->polyUpdate($entity)
                ->partial()
                ->scope($request->getScope(false));
            $entity = $update_request->run();
            return Response::api()->fromEntity($entity);
        } catch (Exception $e) {
            $this->handleException($e);
        }
    }
}
