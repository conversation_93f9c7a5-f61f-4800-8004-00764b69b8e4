    <?php

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

class UpdateCompanyTransactionMigration extends Migration
{
    public function up()
    {
        $this->schema->table('companyTransaction', function (Blueprint $table) {
            $table->renameColumn('transactionDescription', 'description');
            $table->renameColumn('transactionDate', 'date');
            $table->renameColumn('transactionAmount', 'amount');
            $table->bigInteger('number')->after('transactionID');
        });
    }

    public function down()
    {
        $this->schema->table('companyTransaction', function (Blueprint $table) {
            $table->renameColumn('description', 'transactionDescription');
            $table->renameColumn('date', 'transactionDate');
            $table->renameColumn('amount', 'transactionAmount');
            $table->dropColumn('number');
        });
    }
}
