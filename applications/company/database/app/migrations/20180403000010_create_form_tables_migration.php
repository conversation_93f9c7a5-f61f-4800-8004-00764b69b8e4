<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema;
use Illuminate\Database\Schema\Blueprint;

class CreateFormTablesMigration extends Migration
{
    public function up()
    {
        // form categories table
        $this->newTable('formCategories')
            ->primaryKey('formCategoryID')
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('companyID');
                $table->unsignedTinyInteger('type'); // bid
                $table->string('name', 200);
                $table->unsignedMediumInteger('order');
            })
            ->column('parentFormCategoryID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'companyID',
                'nullable' => true
            ])
            ->index('search', Schema\Table::INDEX_TYPE_FULLTEXT, ['name'])
            ->indexes(function (Blueprint $table) {
                $table->index('companyID');
                $table->index('parentFormCategoryID');
            })
            ->create();

        // form items table
        $this->newTable('formItems')
            ->primaryKey('formItemID')
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('companyID');
                $table->unsignedTinyInteger('type'); // bid
                $table->unsignedTinyInteger('status');
                $table->string('name', 100);
                $table->unsignedMediumInteger('order');
                $table->dateTime('archivedAt')->nullable();
                $table->unsignedInteger('archivedByUserID')->nullable();
            })
            ->column('itemID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'type',
                'nullable' => true
            ])
            ->column('replacedByFormItemID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'archivedByUserID',
                'nullable' => true
            ])
            ->index('search', Schema\Table::INDEX_TYPE_FULLTEXT, ['name'])
            ->indexes(function (Blueprint $table) {
                $table->index('companyID');
                $table->index(['type', 'itemID']);
                $table->index('replacedByFormItemID');
            })
            ->create();

        // form categories items
        $this->newTable('formCategoriesItems')
            ->primaryKey('formCategoryItemID')
            ->column('formCategoryID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formCategoryItemID'
            ])
            ->column('formItemID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formCategoryID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('formCategoryID');
                $table->index('formItemID');
            })
            ->create();

        // form item groups table
        $this->newTable('formItemGroups')
            ->primaryKey('formItemGroupID')
            ->columns(function (Blueprint $table) {
                $table->string('name', 100)->nullable();
                $table->unsignedTinyInteger('type');
                $table->text('config')->nullable();
                $table->unsignedMediumInteger('order');
            })
            ->column('formItemID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemGroupID'
            ])
            ->column('parentFormItemGroupID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemID',
                'nullable' => true
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('formItemID');
                $table->index('parentFormItemGroupID');
            })
            ->useHistory(false)
            ->create();

        // form item group fields table
        $this->newTable('formItemGroupFields')
            ->primaryKey('formItemGroupFieldID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('type');
                $table->string('label', 200)->nullable();
                $table->string('displayLabel', 200)->nullable();
                $table->unsignedTinyInteger('isRequired');
                $table->longText('config')->nullable();
            })
            ->column('formItemGroupID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemGroupFieldID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('formItemGroupID');
            })
            ->useHistory(false)
            ->create();

        // form item group field options table
        $this->newTable('formItemGroupFieldOptions')
            ->primaryKey('formItemGroupFieldOptionID')
            ->columns(function (Blueprint $table) {
                $table->string('label', 100);
                $table->unsignedSmallInteger('order');
            })
            ->column('formItemGroupFieldID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemGroupFieldOptionID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('formItemGroupFieldID');
            })
            ->useHistory(false)
            ->create();

        // form item group field products table
        $this->newTable('formItemGroupFieldProducts')
            ->primaryKey('formItemGroupFieldProductID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('itemType'); // category/product
                $table->unsignedTinyInteger('action'); // add, remove
                $table->unsignedSmallInteger('order');
            })
            ->column('formItemGroupFieldID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemGroupFieldProductID'
            ])
            ->column('itemID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'itemType'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('formItemGroupFieldID');
                $table->index(['itemType', 'itemID']);
            })
            ->useHistory(false)
            ->create();

        // form item group layouts table
        $this->newTable('formItemGroupLayouts')
            ->primaryKey('formItemGroupLayoutID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('type');
            })
            ->column('formItemGroupID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemGroupLayoutID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('formItemGroupID');
            })
            ->useHistory(false)
            ->create();

        // form item group layout items table
        $this->newTable('formItemGroupLayoutItems')
            ->primaryKey('formItemGroupLayoutItemID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('itemType'); // group, field, template
                $table->unsignedTinyInteger('size');
                $table->unsignedMediumInteger('order');
                $table->unsignedTinyInteger('isLastInRow');
            })
            ->column('formItemGroupLayoutID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemGroupLayoutItemID'
            ])
            ->column('itemID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'itemType'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('formItemGroupLayoutID');
                $table->index(['itemType', 'itemID']);
            })
            ->useHistory(false)
            ->create();

        // form item group templates table
        $this->newTable('formItemGroupTemplates')
            ->primaryKey('formItemGroupTemplateID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('type'); // client, server
                $table->longText('styles')->nullable();
                $table->longText('content');
                $table->longText('scripts')->nullable();
                $table->longText('config')->nullable();
            })
            ->column('formItemGroupID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemGroupTemplateID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('formItemGroupID');
            })
            ->useHistory(false)
            ->create();

        // form item group rules table
        $this->newTable('formItemGroupRules')
            ->primaryKey('formItemGroupRuleID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('type');
                $table->longText('conditions');
                $table->unsignedMediumInteger('priority')->default(5);
            })
            ->column('formItemGroupID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemGroupRuleID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('formItemGroupID');
            })
            ->useHistory(false)
            ->create();

        // form item group rule events table
        $this->newTable('formItemGroupRuleEvents')
            ->primaryKey('formItemGroupRuleEventID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('type');
                $table->unsignedTinyInteger('state');
                $table->longText('params');
                $table->unsignedMediumInteger('order');
            })
            ->column('formItemGroupRuleID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemGroupRuleEventID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('formItemGroupRuleID');
            })
            ->useHistory(false)
            ->create();

        // form imports
        $this->newTable('formImports')
            ->primaryKey('formImportID')
            ->columns(function (Blueprint $table) {
                $table->longText('data');
            })
            ->column('formItemID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formImportID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('formItemID');
            })
            ->useHistory(false)
            ->create();

        // form item entries table
        $this->newTable('formItemEntries')
            ->primaryKey('formItemEntryID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('isLocked');
            })
            ->column('formItemID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemEntryID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('formItemID');
            })
            ->create();

        // form item entry groups table
        $this->newTable('formItemEntryGroups')
            ->primaryKey('formItemEntryGroupID')
            ->columns(function (Blueprint $table) {
                $table->unsignedSmallInteger('index');
            })
            ->column('formItemEntryID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemEntryGroupID'
            ])
            ->column('parentFormItemEntryGroupID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemEntryID',
                'nullable' => true
            ])
            ->column('formItemGroupID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'parentFormItemEntryGroupID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('formItemEntryID');
                $table->index('parentFormItemEntryGroupID');
            })
            ->create();

        // form item entry group field values
        $this->newTable('formItemEntryGroupFieldValues')
            ->primaryKey('formItemEntryGroupFieldValueID')
            ->columns(function (Blueprint $table) {
                $table->longText('value')->nullable();
            })
            ->column('formItemEntryGroupID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemEntryGroupFieldValueID'
            ])
            ->column('formItemGroupFieldID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemEntryGroupID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('formItemEntryGroupID', 'form_item_entry_group_id_index');
            })
            ->create();

        // form item entry group field files
        $this->newTable('formItemEntryGroupFieldFiles')
            ->primaryKey('formItemEntryGroupFieldFileID')
            ->column('formItemEntryGroupID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemEntryGroupFieldFileID'
            ])
            ->column('formItemGroupFieldID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemEntryGroupID'
            ])
            ->column('fileID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemGroupFieldID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('formItemEntryGroupID', 'form_item_entry_group_id_index');
                $table->index('fileID', 'file_id_index');
            })
            ->create();

        // form item entry group field options
        $this->newTable('formItemEntryGroupFieldOptions')
            ->primaryKey('formItemEntryGroupFieldOptionID')
            ->column('formItemEntryGroupID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemEntryGroupFieldOptionID'
            ])
            ->column('formItemGroupFieldID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemEntryGroupID'
            ])
            ->column('formItemGroupFieldOptionID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemGroupFieldID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('formItemEntryGroupID', 'form_item_entry_group_id_index');
            })
            ->create();

        // form item entry group field products
        $this->newTable('formItemEntryGroupFieldProducts')
            ->primaryKey('formItemEntryGroupFieldProductID')
            ->column('formItemEntryGroupID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemEntryGroupFieldProductID'
            ])
            ->column('formItemGroupFieldID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemEntryGroupID'
            ])
            ->column('productItemID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'formItemGroupFieldID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('formItemEntryGroupID', 'form_item_entry_group_id_index');
                $table->index('productItemID', 'product_item_id_index');
            })
            ->create();
    }

    public function down()
    {
        $this->dropTables([
            'formItemGroups', 'formItemGroupLayouts', 'formItemGroupLayoutItems', 'formItemGroupFields',
            'formItemGroupFieldOptions', 'formItemGroupFieldProducts', 'formItemGroupTemplates', 'formItemGroupRules',
            'formItemGroupRuleEvents', 'formImports'
        ]);

        $this->dropTables([
            'formCategories', 'formCategoriesItems', 'formItems', 'formItemEntries', 'formItemEntryGroups',
            'formItemEntryGroupFieldFiles', 'formItemEntryGroupFieldValues', 'formItemEntryGroupFieldOptions',
            'formItemEntryGroupFieldProducts'
        ], true);
    }
}
