<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

class CreateCompanyPaymentTablesMigration extends Migration
{
    public function up()
    {
        $this->newTable('companyPaymentMethods')
            ->primaryKey('companyPaymentMethodID', false)
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('companyID');
                $table->unsignedTinyInteger('itemType');
                $table->unsignedInteger('itemID');
                $table->string('name', 50)->nullable();
                $table->string('paymentProfileID', 50)
                    ->comment('Authorize.net customer payment profile id');
                $table->unsignedTinyInteger('isDefault');
            })
            ->indexes(function (Blueprint $table) {
                $table->index('companyID');
                $table->index(['itemType', 'itemID']);
            })
            ->create();

        $this->newTable('companyCreditCardPaymentMethods')
            ->primaryKey('companyCreditCardPaymentMethodID', false)
            ->columns(function (Blueprint $table) {
                $table->string('number', 4);
                $table->date('expirationDate')->comment('Date of last day for year and month - YYYY-MM-{LAST}');
                $table->string('address', 100);
                $table->string('address2', 100)->nullable();
                $table->string('city', 50);
                $table->string('state', 25);
                $table->string('zip', 12);
            })
            ->create();

        $this->newTable('companyAchPaymentMethods')
            ->primaryKey('companyAchPaymentMethodID', false)
            ->columns(function (Blueprint $table) {
                $table->string('accountNumber', 4);
            })
            ->create();

        $this->newTable('companyInvoices')
            ->primaryKey('companyInvoiceID', false)
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('companyID');
                $table->unsignedInteger('companyPaymentMethodID')->nullable()
                    ->comment('Payment method to use for invoice, if NULL company default is used');
                $table->unsignedTinyInteger('status'); // pending, paid, failed, refunded
                $table->decimal('total', 8, 2);
                $table->dateTime('paidAt')->nullable();
                $table->unsignedInteger('paidByCompanyPaymentMethodID')->nullable();
                $table->string('transactionID', 20)->nullable()->comment('Authorize.net transaction id');
                $table->dateTime('failedAt')->nullable();
                $table->dateTime('refundedAt')->nullable();
                $table->string('refundTransactionID', 20)->nullable()->comment('Authorize.net refund transaction id');
                $table->dateTime('voidedAt')->nullable();
                $table->string('voidTransactionID', 20)->nullable()->comment('Authorize.net void transaction id');
                $table->unsignedTinyInteger('isStatementFileValid');
            })
            ->column('statementFileID', Table::COLUMN_TYPE_UUID, [
                'after' => 'voidTransactionID',
                'nullable' => true
            ])
            ->timestamps(true, false)
            ->indexes(function (Blueprint $table) {
                $table->index('companyID');
            })
            ->create();

        $this->newTable('companyInvoiceCredits')
            ->primaryKey('companyInvoiceCreditID', false)
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('companyID');
                $table->unsignedTinyInteger('type'); // subscription refund, general
                $table->string('description', 200);
                $table->decimal('amount', 8, 2);
                $table->decimal('remainingAmount', 8, 2);
                $table->unsignedTinyInteger('isExpended');
                $table->dateTime('expendedAt')->nullable();
                $table->unsignedInteger('companySubscriptionID')->nullable();
            })
            ->indexes(function (Blueprint $table) {
                $table->index('companyID');
            })
            ->create();

        $this->newTable('companyInvoiceLineItems')
            ->primaryKey('companyInvoiceLineItemID', false)
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('companyInvoiceID');
                $table->unsignedTinyInteger('type'); // general, add-on, discount, fee, credit
                $table->string('description', 200);
                $table->decimal('amount', 8, 2);
                $table->decimal('quantity', 8, 2);
                $table->decimal('total', 8, 2);
                $table->unsignedTinyInteger('isRefundable');
                $table->unsignedInteger('companySubscriptionID')->nullable();
                $table->unsignedInteger('companySubscriptionPriceAdjustmentID')->nullable();
                $table->unsignedInteger('companyInvoiceCreditID')->nullable();
            })
            ->timestamps(true, false)
            ->indexes(function (Blueprint $table) {
                $table->index('companyInvoiceID');
            })
            ->create();
    }

    public function down()
    {
        $this->dropTables([
            'companyPaymentMethods', 'companyCreditCardPaymentMethods', 'companyAchPaymentMethods', 'companyInvoices',
            'companyInvoiceCredits', 'companyInvoiceLineItems'
        ], true);
    }
}
