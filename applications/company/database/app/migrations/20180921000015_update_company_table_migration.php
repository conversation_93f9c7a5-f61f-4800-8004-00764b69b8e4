<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

class UpdateCompanyTableMigration extends Migration
{
    public function up()
    {
        $this->updateTable('companies')
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('subscriptionID')->nullable()->after('manufacturerID');
                $table->unsignedTinyInteger('isSubscriptionLocked')->after('subscriptionID');
                $table->renameColumn('registrationID', 'oldRegistrationID');
                $table->dateTime('suspendedAt')->nullable()->after('status');
                $table->dateTime('dormantAt')->nullable()->after('suspendedAt');
            })
            ->column('registrationID', Table::COLUMN_TYPE_UUID, [
                'after' => 'brandingID',
                'nullable' => true
            ])
            ->alter();
    }

    public function down()
    {
        $this->updateTable('companies')
            ->columns(function (Blueprint $table) {
                $table->dropColumn([
                    'subscriptionID', 'isSubscriptionLocked', 'registrationID', 'suspendedAt', 'dormantAt'
                ]);
                $table->renameColumn('oldRegistrationID', 'registrationID');
            })
            ->alter();
    }
}
