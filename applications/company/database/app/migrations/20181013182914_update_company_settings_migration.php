<?php

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

class UpdateCompanySettingsMigration extends Migration
{
    public function up()
    {
        $this->schema->rename('companySettings', 'companySettingsOld');
        $this->newTable('companySettings')
            ->primaryKey('companySettingID', false)
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('companyID');
                $table->string('name', 100);
                $table->longText('value')->nullable();
            })
            ->indexes(function (Blueprint $table) {
                $table->index('companyID');
                $table->index(['companyID', 'name']);
            })
            ->create();
    }

    public function down()
    {
        $this->dropTables(['companySettings'], true);
        $this->schema->rename('companySettingsOld', 'companySettings');
    }
}
