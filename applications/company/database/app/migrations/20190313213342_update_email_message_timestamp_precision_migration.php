<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Core\Components\DB\StaticAccessors\DB;
use Illuminate\Database\Schema\Blueprint;

class UpdateEmailMessageTimestampPrecisionMigration extends Migration
{
    public function up()
    {
        $this->updateTable('emailMessages')
            ->column('sentAt', Table::COLUMN_TYPE_DATETIME_PRECISION, [
                'precision' => 6,
                'nullable' => true,
                'after' => 'headers'
            ])
            ->useHistory(false)
            ->alter();

        DB::statement('ALTER TABLE `emailMessageRecipients` MODIFY COLUMN `deliveredAt` DATETIME(6) DEFAULT NULL');
        DB::statement('ALTER TABLE `emailMessageRecipients` MODIFY COLUMN `openedAt` DATETIME(6) DEFAULT NULL');
        DB::statement('ALTER TABLE `emailMessageRecipients` MODIFY COLUMN `failedAt` DATETIME(6) DEFAULT NULL');
    }

    public function down()
    {
        $this->updateTable('emailMessages')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('sentAt');
            })
            ->useHistory(false)
            ->alter();

        DB::statement('ALTER TABLE `emailMessageRecipients` MODIFY COLUMN `deliveredAt` DATETIME DEFAULT NULL');
        DB::statement('ALTER TABLE `emailMessageRecipients` MODIFY COLUMN `openedAt` DATETIME DEFAULT NULL');
        DB::statement('ALTER TABLE `emailMessageRecipients` MODIFY COLUMN `failedAt` DATETIME DEFAULT NULL');
    }
}
