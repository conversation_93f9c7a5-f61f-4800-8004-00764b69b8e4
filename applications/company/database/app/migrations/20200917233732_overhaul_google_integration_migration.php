<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

class OverhaulGoogleIntegrationMigration extends Migration
{
    public function up()
    {
        $this->newTable('googleAuthRequests')
            ->primaryKey('googleAuthRequestID')
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('userID');
                $table->unsignedTinyInteger('type'); // initial, addition
                $table->unsignedTinyInteger('status'); // pending, completed, access denied, failure
                $table->dateTime('completedAt')->nullable();
                $table->dateTime('accessDeniedAt')->nullable();
                $table->dateTime('failedAt')->nullable();
            })
            ->column('googleOAuthID', Table::COLUMN_TYPE_UUID, [
                'after' => 'completedAt',
                'nullable' => true
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('userID', 'user_id_index');
            })
            ->timestamps(true, false)
            ->useHistory(false)
            ->create();
        $this->newTable('googleServices')
            ->primaryKey('googleServiceID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('service'); // calendar
                $table->unsignedTinyInteger('status'); // requested, connected, disconnecting, disconnected
                $table->dateTime('disconnectedAt')->nullable();
            })
            ->column('googleAuthRequestID', Table::COLUMN_TYPE_UUID, [
                'after' => 'googleServiceID',
                'nullable' => true
            ])
            ->column('googleOAuthID', Table::COLUMN_TYPE_UUID, [
                'after' => 'googleAuthRequestID',
                'nullable' => true
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('googleAuthRequestID', 'google_auth_request_id_index');
                $table->index(['service', 'googleOAuthID'], 'service_google_oauth_id_index');
            })
            ->timestamps(true, false)
            ->useHistory(false)
            ->create();

        $this->newTable('googleOAuth')
            ->primaryKey('googleOAuthID')
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('userID');
                $table->unsignedTinyInteger('status'); // connected, disconnected
                $table->text('accessToken')->nullable();
                $table->dateTime('accessTokenExpiresAt')->nullable();
                $table->text('refreshToken')->nullable();
                $table->dateTime('refreshTokenExpiresAt')->nullable();
                $table->unsignedTinyInteger('isCurrent');
                $table->dateTime('disconnectedAt')->nullable();
                $table->unsignedTinyInteger('isExternalDisconnect')->nullable();
            })
            ->indexes(function (Blueprint $table) {
                $table->index('userID', 'user_id_index');
            })
            ->timestamps(true, false)
            ->useHistory(false)
            ->create();

        $this->newTable('googleCalendars')
            ->primaryKey('googleCalendarID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('ownerType');
                $table->unsignedInteger('ownerID');
                $table->unsignedTinyInteger('status'); // adding, added, removing, removed
                $table->string('calendarID', 200);
                $table->string('name', 200);
                $table->unsignedSmallInteger('pullWindowDaysBefore')->nullable()
                    ->comment('Days before current date to pull events after');
                $table->unsignedSmallInteger('pullWindowDaysAfter')->nullable()
                    ->comment('Days after current date to pull events before');
                $table->unsignedSmallInteger('pullWindowExpandWaitDays')->nullable()
                    ->comment('Number of days to wait before increasing the pulled event date window');
                $table->unsignedMediumInteger('pullAfterMinutes')->nullable()
                    ->comment('Number of minutes to wait before pulling events after last incremental sync time');
                $table->dateTime('pullWindowStartAt')->nullable();
                $table->dateTime('pullWindowEndAt')->nullable();
                $table->unsignedTinyInteger('pullStatus'); // pulling, pulled
                $table->string('pullSyncToken', 200)->nullable();
                $table->dateTime('lastPullAt')->nullable();
                $table->unsignedSmallInteger('pushWindowDaysBefore')->nullable()
                    ->comment('Days before current date to push events after');
                $table->unsignedSmallInteger('pushWindowDaysAfter')->nullable()
                    ->comment('Days after current date to push events before');
                $table->unsignedSmallInteger('pushWindowExpandWaitDays')->nullable()
                    ->comment('Number of days to wait before increasing the pushed event date window');
                $table->dateTime('pushWindowStartAt')->nullable();
                $table->dateTime('pushWindowEndAt')->nullable();
                $table->unsignedTinyInteger('pushStatus');
                $table->dateTime('lastPushAt')->nullable();
                $table->dateTime('removedAt')->nullable();
            })
            ->column('googleServiceID', Table::COLUMN_TYPE_UUID, [
                'after' => 'googleCalendarID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index(['ownerType', 'ownerID'], 'owner_type_id_index');
                $table->index('googleServiceID', 'google_service_id_index');
            })
            ->timestamps(true, false)
            ->useHistory(false)
            ->create();
        $this->newTable('googleCalendarNotificationChannels')
            ->primaryKey('googleCalendarNotificationChannelID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('status'); // active, stopped
                $table->string('resourceID', 200);
                $table->dateTime('expiresAt');
                $table->dateTime('stoppedAt')->nullable();
            })
            ->column('googleCalendarID', Table::COLUMN_TYPE_UUID, [
                'after' => 'googleCalendarNotificationChannelID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('googleCalendarID', 'google_calendar_id_index');
            })
            ->timestamps(true, false)
            ->useHistory(false)
            ->create();
        $this->newTable('googleCalendarNotificationChannelHits')
            ->primaryKey('googleCalendarNotificationChannelHitID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('status'); // pending, complete
                $table->unsignedSmallInteger('count');
                $table->dateTime('completedAt', 6)->nullable();
                $table->dateTime('createdAt', 6);
            })
            ->column('googleCalendarNotificationChannelID', Table::COLUMN_TYPE_UUID, [
                'after' => 'googleCalendarNotificationChannelHitID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('googleCalendarNotificationChannelID', 'notification_channel_id_index');
            })
            ->noTimestamps()
            ->useHistory(false)
            ->create();
        $this->newTable('googleCalendarEvents')
            ->primaryKey('googleCalendarEventID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('itemType');
                $table->unsignedInteger('itemID');
                $table->string('googleID', 200)->nullable();
                $table->unsignedTinyInteger('action'); // pushed, pulled
                $table->unsignedSmallInteger('actionCount');
                $table->dateTime('lastActivityAt')->nullable();
                $table->unsignedTinyInteger('isCancelled')->comment('Determines if user cancelled event via Google interface');
                $table->dateTime('cancelledAt')->nullable();
            })
            ->column('googleCalendarID', Table::COLUMN_TYPE_UUID, [
                'after' => 'googleCalendarEventID'
            ])
            ->column('googleCalendarNotificationChannelHitID', Table::COLUMN_TYPE_UUID, [
                'after' => 'lastActivityAt',
                'nullable' => true
            ])
            ->indexes(function (Blueprint $table) {
                $table->index(['itemType', 'itemID'], 'item_type_id_index');
                $table->index('googleCalendarID', 'google_calendar_id_index');
                $table->index('googleID', 'google_id_index');
            })
            ->timestamps(false, false)
            ->useHistory(false)
            ->create();
        $this->newTable('googleCalendarPulls')
            ->primaryKey('googleCalendarPullID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('type'); // pull, window expansion
                $table->dateTime('windowStartAt');
                $table->dateTime('windowEndAt');
                $table->unsignedMediumInteger('totalEvents');
                $table->unsignedMediumInteger('syncedEvents');
                $table->unsignedMediumInteger('createdEvents');
                $table->unsignedMediumInteger('updatedEvents');
                $table->unsignedMediumInteger('pushedEventsSkipped');
                $table->unsignedMediumInteger('cancelledEventsSkipped');
                $table->unsignedMediumInteger('cancelledEvents');
                $table->unsignedMediumInteger('uncancelledEvents');
                $table->dateTime('createdAt', 6);
            })
            ->column('googleCalendarID', Table::COLUMN_TYPE_UUID, [
                'after' => 'googleCalendarPullID'
            ])
            ->column('googleCalendarNotificationChannelHitID', Table::COLUMN_TYPE_UUID, [
                'after' => 'cancelledEvents',
                'nullable' => true
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('googleCalendarID', 'google_calendar_id_index');
            })
            ->noTimestamps()
            ->useHistory(false)
            ->create();
        $this->newTable('googleCalendarPushes')
            ->primaryKey('googleCalendarPushID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('type'); // push, window expansion
                $table->dateTime('windowStartAt');
                $table->dateTime('windowEndAt');
                $table->unsignedMediumInteger('totalEvents');
                $table->unsignedMediumInteger('createdEvents');
                $table->unsignedMediumInteger('updatedEvents');
                $table->unsignedMediumInteger('deletedEvents');
                $table->dateTime('createdAt', 6);
            })
            ->column('googleCalendarID', Table::COLUMN_TYPE_UUID, [
                'after' => 'googleCalendarPushID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('googleCalendarID', 'google_calendar_id_index');
            })
            ->noTimestamps()
            ->useHistory(false)
            ->create();
    }

    public function down()
    {
        $this->dropTables([
            'googleAuthRequests', 'googleServices', 'googleOAuth', 'googleCalendars', 'googleCalendarNotificationChannels',
            'googleCalendarNotificationChannelHits', 'googleCalendarEvents', 'googleCalendarPulls', 'googleCalendarPushes'
        ]);
    }
}
