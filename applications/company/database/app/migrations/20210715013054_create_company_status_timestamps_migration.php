<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

final class CreateCompanyStatusTimestampsMigration extends Migration
{
    public function up()
    {
        $this->updateTable('companies')
            ->columns(function (Blueprint $table) {
                $table->datetime('trialAt')->nullable()->after('setupStatus');
                $table->datetime('activeAt')->nullable()->after('trialAt');
            })
            ->alter();
    }

    public function down()
    {
        $this->updateTable('companies')
            ->columns(function (Blueprint $table) {
                $table->dropColumn(['trialAt', 'activeAt']);
            })
            ->alter();
    }
}
