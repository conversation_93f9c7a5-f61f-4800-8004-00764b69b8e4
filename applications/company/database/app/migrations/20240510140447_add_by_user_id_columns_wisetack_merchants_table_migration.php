<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

class AddByUserIdColumnsWisetackMerchantsTableMigration extends Migration
{
    public function up(): void
    {
        if ($this->schema->hasTable('wisetackMerchants')) {
            $this->schema->table('wisetackMerchants', function (Blueprint $table) {
                $table->integer('createdByUserID')->after('createdAt')->nullable();
                $table->integer('deletedByUserID')->after('deletedAt')->nullable();
            });
        }
    }

    public function down(): void
    {
        if ($this->schema->hasTable('wisetackMerchants')) {
            $this->schema->table('wisetackMerchants', function (Blueprint $table) {
                $table->dropColumn('createdByUserID');
                $table->dropColumn('deletedByUserID');
            });
        }
    }
}








