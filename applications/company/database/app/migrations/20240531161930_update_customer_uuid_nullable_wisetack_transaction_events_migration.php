<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use Core\Components\DB\StaticAccessors\DB;
use Illuminate\Database\Schema\Blueprint;
use App\Classes\DB\Schema;

class UpdateCustomerUuidNullableWisetackTransactionEventsMigration extends Migration
{
    public function up(): void
    {
        if ($this->schema->hasTable('wisetackTransactionEvents')) {
            DB::statement('ALTER TABLE `wisetackTransactionEvents` MODIFY `customerUUID` BINARY(16) NULL');
        }
    }

    public function down(): void
    {
    }
}








