<?php
declare(strict_types=1);

use App\Classes\DB\Migration;

final class AddWisetackTransactionExpirationDateColumn extends Migration
{
    public function up(): void
    {
        if ($this->schema->hasTable('wisetackTransactions')) {
            DB::statement('ALTER TABLE `wisetackTransactions` ADD COLUMN `expirationDate` DATETIME NULL AFTER `paymentLink`');
        }
    }

    public function down(): void
    {
        if ($this->schema->hasTable('wisetackTransactions')) {
            DB::statement('ALTER TABLE `wisetackTransactions` DROP COLUMN `expirationDate`');
        }
    }
}
