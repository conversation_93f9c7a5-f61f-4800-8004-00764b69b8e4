# Must already be autenticated, set cookie in cookie jar to match browser PHPSESSID value
# Must connect to google through user profile since this cannot be done via HTTP client due to OAuth process

### Get list of available calendars for user
GET {{host}}/api/integration/google/calendar/list-available

### Get calendar info for current user
GET {{host}}/api/integration/google/calendar?owner_type=1&include_user=true

### Add new calendar
# <calendar-id> must be an google identifer returned from list-available call
POST {{host}}/api/integration/google/calendar
Content-Type: application/json

{
  "owner_type": 1,
  "calendar_id": "<calendar-id>"
}

> {% client.global.set('last_calendar_id', request.body.json.id) %}

### Sync all calendars by owner type
# full_sync: true will dump local cache and pull all events again
PUT {{host}}/api/integration/google/calendar/sync
Content-Type: application/json

{
  "owner_type": 1,
  "full_sync": false
}

### Get individual calendar details
GET {{host}}/api/integration/google/calendar/{{last_calendar_id}}

### Sync single calendar by id
# full_sync: true will dump local cache and pull all events again
PUT {{host}}/api/integration/google/calendar/{{last_calendar_id}}/sync
Content-Type: application/json

{
  "full_sync": false
}

### Remove calendar by id
DELETE {{host}}/api/integration/google/calendar/{{last_calendar_id}}

### Disconnect Calendar
GET {{host}}/api/integration/google/calendar/disconnect