'use strict';

/**
 * Class to parse and compare content types
 */
class ContentType {
    /**
     * Constructor
     *
     * @param {string|null} content_type
     */
    constructor(content_type = null) {
        this.state = {
            raw: content_type,
            type: null,
            subtype: null,
            params: {}
        };
        if (content_type !== null) {
            Object.assign(this.state, ContentType.parse(content_type));
        }
    };

    /**
     * Parse type, subtype, and params out of passed content type
     *
     * @param {string} type
     * @returns {{raw: string, type: string, subtype: string, params: {}}}
     */
    static parse(type) {
        let parts = type.split(';');
        type = parts.shift().trim();
        let item = {
            raw: type,
            params: {}
        };
        let sides = type.split('/', 2);
        if (sides.length !== 2) {
            throw new Error('Invalid content type');
        }
        item.type = sides[0].trim();
        item.subtype = sides[1].trim();
        if (parts.length > 0) {
            for (let part of parts) {
                let [name, value] = part.split('=');
                item.params[name.trim()] = value.trim();
            }
        }
        return item;
    };

    /**
     * Get raw content type used to create instance
     *
     * @returns {string|null}
     */
    get raw() {
        return this.state.raw;
    };

    /**
     * Get type
     *
     * @returns {string|null}
     */
    get type() {
        return this.state.type;
    };

    /**
     * Set type
     *
     * @param {string} value
     */
    set type(value) {
        this.state.type = value;
    };

    /**
     * Get subtype
     *
     * @returns {string|null}
     */
    get subtype() {
        return this.state.subtype;
    };

    /**
     * Set subtype
     *
     * @param {string} value
     */
    set subtype(value) {
        this.state.subtype = value;
    };

    /**
     * Get params list
     *
     * @returns {object}
     */
    get params() {
        return this.state.params;
    };

    /**
     * Set params
     *
     * @param {object} value
     */
    set params(value) {
        if (value === null || typeof value !== 'object' || Array.isArray(value)) {
            throw new Error('Params must be an object');
        }
        this.state.params = value;
    };

    /**
     * Get specific param
     *
     * @param {string} name
     * @returns {string|null}
     */
    getParam(name) {
        return this.state.params[name] || null;
    };

    /**
     * Set param
     *
     * @param {string} name
     * @param {string} value
     */
    setParam(name, value) {
        this.state.params[name] = value;
    };

    /**
     * Check if content type matches
     *
     * @param {(ContentType|string)} challenger
     * @param {boolean} [match_any=true] - Determines if it can match the all content type
     * @returns {boolean}
     */
    matches(challenger, match_any = true) {
        let main = this.toString(false);
        if (!(challenger instanceof ContentType)) {
            challenger = new ContentType(challenger);
        }
        if ((match_any && main === '*/*') || main === challenger.toString(false)) {
            return true;
        }
        if (this.state.type !== challenger.type) {
            return false;
        }
        return this.state.subtype === '*' || this.state.subtype === challenger.subtype;
    };

    /**
     * Convert to string
     *
     * @param {boolean} [params=true] - Determines if params are included in content type
     * @returns {string}
     */
    toString(params = true) {
        let str = `${this.state.type}/${this.state.subtype}`;
        if (params) {
            let keys = Object.keys(this.state.params);
            if (keys.length > 0) {
                for (let key of keys) {
                    str += `;${key}=${this.state.params[key]}`; // @todo handle quoting if needed
                }
            }
        }
        return str;
    };
}

module.exports = ContentType;
