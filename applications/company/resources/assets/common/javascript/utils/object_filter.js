'use strict';

/**
 * Filter object by keys
 *
 * Optionally remap key names. Any keys which don't exist in map config will be removed.
 *
 * @param {object} data
 * @param {object} map
 * @returns {object}
 */
module.exports = (data, map) => {
    let obj = {};
    for (let name of Object.keys(data)) {
        let key = map[name];
        if (key === undefined) {
            continue;
        }
        if (key === true) {
            key = name;
        }
        obj[key] = data[name];
    }
    return obj;
};
