@use '../color';
@use '../elevation';
@use '../typography';
@use 'sass:color' as sass-color;

// font
$font-family: typography.$font-family-header;
$font-weight: 600;

// default
$default-color: color.$white-default !default;
$default-bg-color: color.$red-default !default;
$default-border-color: $default-bg-color !default;
$default-box-shadow: elevation.$primary-base;

// hover
$hover-color: $default-color !default;
$hover-bg-color: color.$red-light-1 !default;
$hover-border-color: $hover-bg-color !default;
$hover-box-shadow: elevation.$primary-hover;

// active
$active-color: $hover-color !default;
$active-bg-color: color.$red-dark-1 !default;
$active-border-color: $active-bg-color !default;
$active-box-shadow: none;

// disabled
$disabled-color: color.$grey-light-2 !default;
$disabled-bg-color: color.$grey-light-4 !default;
$disabled-border-color: $disabled-bg-color !default;
$disabled-box-shadow: none;

// loading
$loading-color: $default-bg-color !default;
$loading-bg-color: $default-bg-color !default;
$loading-border-color: $loading-bg-color !default;
$loading-box-shadow: $default-box-shadow;
$loading-image: url('~@cac-loader/dots_white.svg?inline');
