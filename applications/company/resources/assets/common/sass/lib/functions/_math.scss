@forward 'sass:math';

@use 'sass:math';

/// Calculates the height as a percentage of the width for a given ratio.
/// @param {List} $ratio - Ratio to use to calculate the height, formatted as `x by y`.
/// @return {Number} A percentage value for the height relative to the width of a responsive container.
@function ratio-to-percentage($ratio) {
    $w: nth($ratio, 1);
    $h: nth($ratio, 3);
    @return math.div($h, $w) * 100%;
}
