'use strict';

const lang = require('lodash/lang');
const Inputmask = require('inputmask');

const Modal = require('@ca-submodule/modal').Legacy;
const Api = require('@ca-package/api');

const states = require('@cac-js/data/states');
const us_territories = require('@cac-js/data/us_territories');
const provinces = require('@cac-js/data/provinces');

const modal_pm_tpl = require('@cam-account-tpl/modals/payment_methods/add.hbs');

module.exports = class extends Modal {
    constructor() {
        super(Modal.Size.TINY, false, modal_pm_tpl({
            ach_allowed: account_data.ach_allowed,
            info: `${window.fx_url.assets.IMAGE}icons/info.png`,
            states,
            us_territories,
            provinces
        }));

        this.elem.active_tab = 'credit';

        this.elem.sub_title = this.elem.root.find('.modal_subtitle');
        this.elem.form_elements_general = this.elem.root.find('input[data-general]');
        this.elem.form_elements_credit = this.elem.root.find('input[data-credit], select[data-credit]');
        this.elem.form_elements_bank = this.elem.root.find('input[data-bank]');
        this.elem.form = this.elem.root.find('form');
        this.elem.close_pm = this.elem.root.find('.close-pm');
        this.elem.save = this.elem.root.find('.save');
        this.elem.state = this.elem.root.find('select[name="state"]');
        this.elem.credit_info = this.elem.root.find('.credit');
        this.elem.credit_change_wrapper = this.elem.root.find('.credit_change');
        this.elem.tabs = this.elem.root.find('[data-tabs]');
        this.elem.tab_panels = this.elem.root.find('.tabs-panel');
        this.elem.error = this.elem.root.find('[data-error]');
        this.elem.error_message = this.elem.error.find('[data-error-message]');

        e$(this.elem.root[0]).find('[data-tooltip]').foundation();

        this.elem.error.hide();

        this.form = this.elem.form.parsley().on('form:submit', () => {
            this.save();
            return false;
        }).on('form:validate', () => {
            this.elem.form.find('.parsley-errors-list [class^="parsley-fx-"]').remove();
        });

        let fields = {
            name: {
                maxlength: 50,
                maxlengthMessage: 'Invalid length - 50 chars. max'
            },
            is_default: {}
        };
        this.field = {};
        for (let item in fields) {
            if (lang.isUndefined(fields[item].requiredMessage)) {
                fields[item].requiredMessage = 'This value is required.';
            }
            this.field[item] = this.elem.form_elements_general.filter(`[name="${item}"]`).parsley(fields[item]);
        }

        this.elem.form_elements_general.filter('[name="is_default"]').prop('checked', true);

        this.elem.close_pm.on('click.fx', (e) => {
            e.preventDefault();
            this.clearForm();
            this.close();
            return false;
        });

        Inputmask({
            "mask": "99/9999"
        }).mask(this.elem.form_elements_credit.filter('[name="expiration_date"]'));

        this.elem.tabs.on('click.fx', 'a', (e) => {
            e.preventDefault();
            let this_tab = $(e.currentTarget);
            let tab_id = this_tab.attr('data-tab-id');

            if (this.elem.active_tab !== tab_id) {
                this.elem.tabs.find('.tabs-title').removeClass('is-active');
                this.elem.tabs.find('a').attr('aria-selected', false);
                this_tab.parent().addClass('is-active');
                this_tab.attr('aria-selected', true);
                this.elem.active_tab = tab_id;
                this.formReset();

                for (let item of this.elem.tab_panels) {
                    let this_item = $(item);
                    if (this_item.attr('id') === tab_id) {
                        this_item.addClass('is-active');
                        for (let element of this.elem[`form_elements_${tab_id}`]) {
                            let this_element = $(element);
                            if (this_element.data(tab_id) !== false) {
                                this_element.prop('required', true);
                            }
                        }
                    } else {
                        this_item.removeClass('is-active');
                        for (let element of this.elem[`form_elements_${this_item.attr('id')}`]) {
                            $(element).prop('required', false);
                        }
                    }
                }
            }
            return false;
        });
    };

    formReset() {
        this.elem.form.find('.parsley-errors-list [class^="parsley-fx-"]').remove();
        this.form.reset();
        this.elem.error.hide();
    };

    clearForm() {
        for (let item of this.elem.form_elements_general) {
            let this_item = $(item);
            if (this_item.attr('type') === 'checkbox') {
                this_item.prop('checked', false);
                continue;
            }
            this_item.val('');
        }
        for (let item of this.elem.form_elements_credit) {
            let this_item = $(item);
            this_item.val('');
            if (this_item.data('credit')) {
                this_item.prop('required', true);
            }
        }
        for (let item of this.elem.form_elements_bank) {
            let this_item = $(item);
            if (this_item.attr('type') === 'radio') {
                this_item.prop('checked', false);
                continue;
            }
            this_item.val('');
            this_item.prop('required', false);
        }
        $('a[data-tab-id="credit"]').trigger('click.fx');
        this.elem.root.scrollTop(0);
        this.elem.credit_info.removeClass('show');
        this.elem.credit_change_wrapper.removeClass('show');
        this.formReset();
    };

    getData() {
        let data = {};

        for (let item of this.elem.form_elements_general) {
            let this_item = $(item);

            if (lang.isString(this_item.attr('name'))) {
                switch(this_item.attr('type')) {
                    case 'checkbox':
                        data[this_item.attr('name')] = this_item.is(':checked');
                        break;
                    default:
                        data[this_item.attr('name')] = this_item.val();
                }
            }
        }

        switch(this.elem.active_tab) {
            case 'credit':
                data['item_type'] = 2;
                break;
            case 'bank':
                data['item_type'] = 1;
                break;
        }

        let data_item = {};
        for (let item of this.elem[`form_elements_${this.elem.active_tab}`]) {
            let this_item = $(item);
            let value = '';
            switch(this_item.attr('name')) {
                case 'expiration_date':
                    let [month, year] = this_item.val().split('/');
                    value = `${year}-${month}`;
                    break;
                case 'account_type':
                    value = parseInt(this_item.val());
                    break;
                default:
                    value = this_item.val();
                    break;
            }
            data_item[this_item.attr('name')] = value;
        }
        data['item'] = data_item;

        return data;
    };

    open() {
        super.open();
        this.elem.root.scrollTop(0);
    };

    save() {
        this.formReset();
        let data = this.getData();

        this.elem.save.attr('disabled', true);

        Api.Resources.CompanyPaymentMethods().store(data).then(() => {
            this.fire('saved');
            this.elem.save.attr('disabled', false);
            super.close();
            this.clearForm();
        }, (error, response) => {
            this.elem.save.attr('disabled', false);
            switch (response.statusCode()) {
                case 422:
                    let item_errors = response.data().errors;
                    if (item_errors.item !== undefined) {
                        this.elem.error_message.text(item_errors.item);
                        this.elem.error.show();
                        break;
                    }
                    for (let item in item_errors) {
                        if (!lang.isUndefined(this.field[item])) {
                            this.field[item].addError(`fx-${item}`, {message: item_errors[item]});
                        }
                    }
                    this.fire('saved');
                    break;
                default:
                    this.fire('saved');
                    super.close();
                    this.fire('error', error);
                    this.clearForm();
                    break;
            }
        });
    };
};
