'use strict';

import $ from 'jquery';

import {findChild, jsSelector} from '@ca-package/dom';
import Page from '@ca-package/router/src/page';
import FormInput from '@ca-submodule/form-input';
import PasswordInput from '@ca-submodule/form-input/src/password';
import FormValidator from '@ca-submodule/validator';
import '@ca-submodule/validator/src/validators/password';
import {Message} from '@ca-submodule/flash-message';
import verify_url from '@cac-js/verify_url';

import page_tpl from '@cam-auth-tpl/pages/main-pages/reset_password.hbs';

FormInput.use(PasswordInput);

/**
 * @memberof module:Auth/Pages/MainPages
 */
export class ResetPasswordPage extends Page {
    /**
     * Fetch password reset entity by id
     * @param {string} id
     * @returns {Promise<object>}
     */
    fetchPasswordReset(id) {
        return new Promise((resolve, reject) => {
            $.getJSON(`${window.fx_url.API}user/password-resets/${id}`)
                .done(resolve)
                .fail(reject);
        });
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        this.parent.showLoader();
        verify_url(window.location.href).then(async valid => {
            if (!valid) {
                throw Message.error('Link is not valid');
            }
            try {
                this.state.info = await this.fetchPasswordReset(request.params.id);
            } catch (e) {
                if (e.status === 404) {
                    throw Message.error('Reset request does not exist or has expired');
                }
                throw e;
            }
            if (this.state.info.completed_at !== null) {
                throw Message.error('Reset request already completed');
            }
            this.parent.hideLoader();
        }).catch(e => {
            if (e instanceof Message) {
                this.parent.messages.addMessage(e, false);
            }
            this.router.navigate('forgot_password');
            this.parent.hideLoader();
        });
        this.parent.header = 'Reset Password';
        await super.load(request, next);
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        await super.unload(request, next);
        this.reset();
    };

    /**
     * Handle reset password request
     */
    handle() {
        this.parent.clearMessages();
        this.parent.showLoader();
        this.state.validator.clearAllFieldErrors();
        $.ajax({
            url: `${window.fx_url.API}user/password-resets/${this.state.info.id}/complete`,
            method: 'PUT',
            dataType: 'json',
            contentType: 'application/json',
            data: JSON.stringify({
                password: this.state.validator.getInputElem('password').val()
            })
        })
            .done(() => {
                let message = Message.success('Password reset successfully, please login.');
                this.parent.messages.addMessage(message, false);
                this.router.navigate('login');
                this.parent.hideLoader();
            })
            .fail(xhr => {
                switch (xhr.status) {
                    case 403:
                        this.parent.messages.addMessage(Message.error('Reset request has already been completed'), false);
                        this.router.navigate('forgot_password');
                        break;
                    case 404:
                        this.parent.messages.addMessage(Message.error('Reset request has expired'), false);
                        this.router.navigate('forgot_password');
                        break;
                    case 422:
                        let errors = xhr.responseJSON.errors;
                        if (errors !== undefined) {
                            this.state.validator.setFieldErrors(errors);
                        }
                        break;
                    default:
                        this.parent.messages.addMessage(Message.error('Unable to reset password at this time, please contact support'));
                }
                this.parent.hideLoader();
            });
    };

    /**
     * Reset page to default state
     */
    reset() {
        this.state.validator.reset();
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);

        this.elem.form = findChild(root, jsSelector('form'));

        this.state.validator = FormValidator.create(this.elem.form, {
            password: {
                required: true,
                password: 3
            }
        }).on('submit', () => this.handle());

        FormInput.init(this.state.validator.getInputElem('password'));
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return page_tpl();
    };
}
