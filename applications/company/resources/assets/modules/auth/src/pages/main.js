/**
 * @module Auth/Pages/MainPages
 */

'use strict';

import {find, findChild, jsSelector} from '@ca-package/dom';
import Page from '@ca-package/router/src/page';
import FlashMessage from '@ca-submodule/flash-message';

import {LoginPage} from './main-pages/login';
import {ForgotPasswordPage} from './main-pages/forgot_password';
import {ResetPasswordPage} from './main-pages/reset_password';
import {SetPasswordPage} from './main-pages/set_password';
import {SetUserDetails} from './main-pages/set_user_details';

import page_tpl from '@cam-auth-tpl/pages/main.hbs';

const MessageTypes = {
    alert: FlashMessage.Message.Type.ERROR,
    info: FlashMessage.Message.Type.INFO,
    success: FlashMessage.Message.Type.SUCCESS
};

/**
 * @memberof module:Auth/Pages
 */
export class MainPage extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            messages: null
        });
        this.routerSubscribe('route-changed', 'onRouteChanged');
    };

    /**
     * Get child routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            login: {
                path: '/login',
                page: LoginPage
            },
            forgot_password: {
                path: '/forgot-password',
                page: ForgotPasswordPage
            },
            reset_password: {
                path: '/reset-password/{id}',
                bindings: {
                    id: 'uuid'
                },
                page: ResetPasswordPage
            },
            set_password: {
                path: '/set-password',
                page: SetPasswordPage
            },
            set_user_details: {
                path: '/set-user-details',
                page: SetUserDetails
            }
        };
    };

    /**
     * Get messages instance
     *
     * Will create one if it doesn't exist already
     *
     * @returns {module:FlashMessage.Controller}
     */
    get messages() {
        if (this.state.messages === null) {
            let messages = new FlashMessage.Controller();
            this.elem.messages = findChild(this.elem.root, jsSelector('messages'));
            this.elem.messages.append(messages.render());
            messages.boot(this.elem.messages);
            this.state.messages = messages;
        }
        return this.state.messages;
    };

    /**
     * Clear all existing flash messages
     */
    clearMessages() {
        if (this.state.messages === null) {
            return;
        }
        this.state.messages.deleteAllMessages();
    };

    /**
     * Handle route changed event
     *
     * Clears and renders any pending flash messages.
     */
    onRouteChanged() {
        if (this.state.messages === null) {
            return;
        }
        this.state.messages.deleteAllMessages();
        this.state.messages.renderPendingMessages();
    };

    /**
     * Toggle loader display
     *
     * @param {boolean} [show=true]
     */
    showLoader(show = true) {
        this.elem.loader.toggle(show);
    };

    /**
     * Hide loader overlay
     */
    hideLoader() {
        this.showLoader(false);
    };

    /**
     * Set header of module
     *
     * @param {string} text
     */
    set header(text) {
        this.elem.header.text(text);
    };

    /**
     * Get page container
     *
     * @returns {jQuery}
     */
    getPageContainer() {
        return this.elem.content;
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.layout = find(jsSelector('layout'));
        this.elem.loader = findChild(this.elem.layout, jsSelector('loader'));
        this.elem.header = findChild(root, jsSelector('header'));
        this.elem.content = findChild(root, jsSelector('content'));

        let message = window.auth_data.message;
        if (message !== null) {
            let flash_message = FlashMessage.Message.make(message.message, MessageTypes[message.type]);
            flash_message.actionClear();
            if (message.auto_delete === undefined || message.auto_delete) {
                flash_message.deleteAfter(10);
            }
            this.messages.addMessage(flash_message, false);
        }
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return page_tpl();
    };
}
