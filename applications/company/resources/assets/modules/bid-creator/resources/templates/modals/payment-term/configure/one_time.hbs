<form class="m-one-time-type" data-js="form">
    <div class="s-grid">
        <div class="i-g-row t-align-middle">
            <div class="i-gr-column t-size-6">
                <span class="c-oty-amount">
                    <span class="c-otya-title">Amount:</span>
                    <span class="c-otya-amount" data-js="amount">$0.00</span></span>
            </div>
            <div class="i-gr-column t-size-6">
                <div class="f-field">
                    <label class="f-f-label" for="m-oty-due-time-frame">Payment Due<span class="f-fl-required t-show">*</span></label>
                    <select class="f-f-input" id="m-oty-due-time-frame" data-js="due_time_frame"></select>
                </div>
            </div>
        </div>
    </div>
</form>