/**
 * @module BidCreator/Entities/LineItem/Product
 */

'use strict';

const lang = require('lodash/lang');
const cloneDeep = require('lodash/cloneDeep');
const includes = require('lodash/includes');
const uuid4 = require('uuid/v4');

/**
 * @type {module:Api}
 */
const Api = require('@ca-package/api');

const Base = require('./base');
const Utils = require('../../utils');

const Number = require('@cac-js/utils/number');

/**
 * @alias module:BidCreator/Entities/LineItem/Product
 */
class Product extends Base {
    /**
     * Constructor
     *
     * @param {Object} [data={}]
     * @param {boolean} [existing=false]
     */
    constructor(data = {}, existing = false) {
        super(data, existing);

        this.state.type = Base.Type.PRODUCT;

        this.state.materials = [];
        this.state.additional_costs = [];
        this.state.is_component_adjustment = null;
        this.state.component_amount = null;
        this.state.product_changed = false;
        this.state.adjustment_mode = null;
        this.state.adjustment_type = null;
        this.state.adjustment = null;

        if (data.item !== undefined) {
            this.product_id = data.item.product_item_id;
            this.state.product_item_price_id = data.item.product_item_price_id;
            if (data.item.materials.length > 0 ) {
                this.state.materials = data.item.materials;
            }
            if (data.item.additional_costs.length > 0 ) {
                this.state.additional_costs = data.item.additional_costs;
            }
            if (data.item.component_amount !== null) {
                this.state.component_amount = data.item.component_amount;
            }
            if (data.item.is_component_adjustment !== null) {
                this.is_component_adjustment = data.item.is_component_adjustment;
            }
            if (data.item.adjustment !== null) {
                this.state.adjustment = data.item.adjustment;
            }
            if (data.item.adjustment_mode !== null) {
                this.state.adjustment_mode = data.item.adjustment_mode;
            }
            if (data.item.adjustment_type !== null) {
                this.state.adjustment_type = data.item.adjustment_type;
            }
        }
        if (existing) {
            this.setStateChanged(false);
        }
        // @todo maybe store the current product in class
    };

    /**
     * Adjustment Modes
     *
     * @readonly
     *
     * @returns {{PLUS: number, MINUS: number}}
     */
    static get AdjustmentMode() {
        return {
            POSITIVE: 1,
            NEGATIVE: 2
        };
    };

    /**
     * Adjustment Types
     *
     * @readonly
     *
     * @returns {{TOTAL: number, PERCENTAGE: number}}
     */
    static get AdjustmentType() {
        return {
            TOTAL: 1,
            PERCENTAGE: 2
        };
    };

    get product_item_price_id() {
        return this.state.product_item_price_id;
    };

    /**
     * Set adjustment mode
     *
     * @param {number} value
     */
    set adjustment_mode(value) {
        let data = value;
        if (value === null) {
            data = null;
        }
        if (data !== null && !includes(Product.AdjustmentMode, value)) {
            throw new Error('Adjustment mode is not valid');
        }
        this.setState({
            adjustment_mode: data
        });
    };

    /**
     * Get adjustment type
     *
     * @readonly
     *
     * @returns {number}
     */
    get adjustment_mode() {
        return this.state.adjustment_mode;
    };

    /**
     * Set adjustment type
     *
     * @param {number} value
     */
    set adjustment_type(value) {
        let data = value;
        if (value === null) {
            data = null;
        }
        if (data !== null && !includes(Product.AdjustmentType, value)) {
            throw new Error('Adjustment type is not valid');
        }
        this.setState({
            adjustment_type: data
        });
    };

    /**
     * Get adjustment type
     *
     * @readonly
     *
     * @returns {number}
     */
    get adjustment_type() {
        return this.state.adjustment_type;
    };

    /**
     * Set adjustment
     *
     * @param {(string|Decimal)} value
     */
    set adjustment(value) {
        let data = value;
        if (value === null) {
            data = null;
        }
        this.setState({
            adjustment: data
        });
    };

    /**
     * Get adjustment
     *
     * @readonly
     *
     * @returns {Decimal}
     */
    get adjustment() {
        let adjustment = null;
        if (this.state.adjustment !== null) {
            adjustment = Number.ofInput(this.state.adjustment);
        }
        return adjustment;
    };

    /**
     * Get adjustment
     *
     * @param {boolean} [formatted=true] - Determines if amount is properly formatted
     * @returns {Decimal}
     */
    getAdjustment(formatted = true) {
        let adjustment = this.adjustment;
        if (formatted) {
            adjustment = Number.toCurrency(adjustment);
        }
        return adjustment;
    };

    /**
     * Cache product entity
     *
     * Stores entities in static cache which any line item can use to help prevent unnecessary calls to the server to
     * get individual product entities
     *
     * @param {(Object|Api.Entity)} entity
     * @returns {Api.Entity}
     */
    static cacheEntity(entity) {
        if (!(entity instanceof Api.Entity)) {
            entity = new Api.Entity(entity);
        }
        Product.__cache.set(entity.get('id'), entity);
        return entity;
    };

    /**
     * Get entity by id
     *
     * If entity is already in the internal cache, we return that otherwise an API call is made to retrieve, cache, and
     * return the entity.
     *
     * @param {string} id
     * @returns {Promise}
     */
    static getEntity(id) {
        let entity = Product.__cache.get(id);
        if (entity !== undefined) {
            return Promise.resolve(entity);
        }
        return new Promise((resolve, reject) => {
            Api.Resources.ProductItems().accept('application/vnd.adg.fx.bid-v1+json').retrieve(id).then((entity) => {
                this.cacheEntity(entity);
                resolve(entity);
            }, (error) => {
                reject(error);
            });
        });
    };

    /**
     * Get price from product entity based on quantity
     *
     * @param {Api.Entity} entity - Product entity with pricing info
     * @param {string|Decimal} quantity - Quantity amount
     * @returns {(null|Array)}
     */
    static getPrice(entity, quantity) {
        let prices = entity.get('prices');
        if (lang.isArray(prices)) {
            quantity = Utils.getAbsDecimal(quantity);
            if (quantity === null) {
                return null;
            }
            for (let item of prices) {
                item.min_count = Utils.getDecimalFromInput(item.min_count);
                if (item.max_count !== null) {
                    item.max_count = Utils.getDecimalFromInput(item.max_count);
                }
                if (quantity.gte(item.min_count) && (item.max_count === null || quantity.lt(item.max_count))) {
                    return [item.id, Utils.getDecimalFromInput(item.price)];
                }
            }
        }
        return null;
    };

    /**
     * Set name override
     *
     * @param {string} value
     */
    set name_override(value) {
        if (!lang.isString(value)) {
            throw new Error('Override Name is not valid');
        } else if (value.length < 1 || value.length > 100) {
            throw new Error('Override Name must be between 1 and 100 characters');
        }
        if (this.state.name_override === value) {
            return;
        }
        this.setState({
            name_override: value
        });
    };

    /**
     * Get name
     *
     * If override is set, it is returned otherwise the main name is
     *
     * @returns {string}
     */
    getName() {
        return lang.isNil(this.state.name_override) ? this.state.name : this.state.name_override;
    };

    /**
     * Product has components
     *
     * @returns {boolean}
     */
    hasComponents() {
        return this.state.materials.length > 0 || this.state.additional_costs.length > 0;
    };

    /**
     * Set product id
     *
     * @param {string} value - Product uuid
     */
    set product_id(value) {
        let data = {};

        // if product changes, reset name, adjustment, and total
        if (this.product_id !== undefined && value !== this.product_id) {
            data.name_override = null;
            data.adjustment = null;
            data.adjustment_type = null;
            data.adjustment_mode = null;
            data.total = null;
            if (this.product_id !== undefined) {
                this.state.product_changed = true;
            }
        }
        data.product_id = value;
        this.setState(data);
        // @todo maybe invalid name and amount since they may not be associated with the product
    };



    /**
     * Get product id
     *
     * @readonly
     *
     * @returns {string}
     */
    get product_id() {
        return this.state.product_id;
    };

    /**
     * Get price
     *
     * This is an alias for amount since price sounds better when working with products
     *
     * @readonly
     *
     * @returns {Decimal}
     */
    get price() {
        return this.amount;
    };

    /**
     * Set materials array
     *
     * @param {array} array
     */
    set materials(array) {
        if (!lang.isArray(array)) {
            throw new Error('Materials is not valid');
        }
        this.setState({
            materials: array
        });
    };

    /**
     * Get materials
     *
     * @readonly
     *
     * @returns {Array}
     */
    get materials() {
        return this.state.materials;
    };

    /**
     * Set additional costs array
     *
     * @param {array} array
     */
    set additional_costs(array) {
        if (!lang.isArray(array)) {
            throw new Error('Additional costs is not valid');
        }
        this.setState({
            additional_costs: array
        });
    };

    /**
     * Get additional costs
     *
     * @readonly
     *
     * @returns {Array}
     */
    get additional_costs() {
        return this.state.additional_costs;
    };

    /**
     * Set component adjustment
     *
     * @param {boolean} boolean
     */
    set is_component_adjustment(boolean) {
        let data = boolean;
        if (boolean === null) {
            data = null;
        }
        if (data !== null && typeof boolean !== 'boolean') {
            throw new Error('Is component adjustment is not valid');
        }
        this.setState({
            is_component_adjustment: data
        });
    };

    /**
     * Get is component adjustment
     *
     * @readonly
     *
     * @returns {boolean}
     */
    get is_component_adjustment() {
        return this.state.is_component_adjustment;
    };

    /**
     * Set component amount
     *
     * @param {(string|Decimal)} value
     */
    set component_amount(value) {
        let data = value;
        if (value === null) {
            data = null;
        }
        this.setState({
            component_amount: data
        });
    };

    /**
     * Get adjustment
     *
     * @readonly
     *
     * @returns {Decimal}
     */
    get component_amount() {
        let amount = null;
        if (this.state.component_amount !== null) {
            amount = Number.ofInput(this.state.component_amount);
        }
        return amount;
    };

    /**
     * Get component amount
     *
     * @param {boolean} [formatted=true] - Determines if amount is properly formatted
     * @returns {Decimal}
     */
    getComponentAmount(formatted = true) {
        let amount = this.component_amount;
        if (formatted) {
            amount = Number.toCurrency(amount);
        }
        return amount;
    };

    /**
     * Compare specified line item against internal data and return if they are equal
     *
     * @param {Product} line_item
     * @returns {boolean}
     */
    matches(line_item) {
        if (
            line_item.product_id !== this.product_id ||
            !line_item.quantity.equals(this.quantity)
        ) {
            return false;
        }
        return true;
    };

    /**
     * Get payload for API save request
     *
     * @param {string} bid_item_id - Bid uuid
     * @returns {Object}
     */
    getPayload(bid_item_id) {
        let payload = super.getPayload(bid_item_id);
        payload.item = {
            id: this.item_id,
            product_item_id: this.product_id,
            product_item_price_id: this.state.product_item_price_id,
            adjustment_mode: this.adjustment_mode,
            adjustment_type: this.adjustment_type,
            adjustment: this.adjustment
        };
        if (this.state.materials.length > 0) {
            for (let material of this.state.materials) {
                // only update the material quantities if the component amount hasn't been updated by the user, we know this because there is a product quantity defined on the material
                if (material.product_quantity !== undefined && material.product_quantity.toFixed(2) === this.quantity.toFixed(2)) {
                    continue;
                }
                if (material.id === undefined) {
                    material.id = uuid4();
                    continue;
                }
                if (material.product_item_material_id === null) {
                    continue;
                }
                let new_quantity = parseFloat(material.unit_quantity) * this.quantity.toFixed(2);
                material.total_quantity = new_quantity.toString();
            }
            payload.item.materials = this.state.materials;
        }
        if (this.state.additional_costs.length > 0) {
            for (let additional_cost of this.state.additional_costs) {
                // only update the material quantities if the component amount hasn't been updated by the user, we know this because there is a product quantity defined on the additional cost
                if (additional_cost.product_quantity !== undefined && additional_cost.product_quantity.toFixed(2) === this.quantity.toFixed(2)) {
                    continue;
                }
                if (additional_cost.id === undefined) {
                    additional_cost.id = uuid4();
                    continue;
                }
                if (additional_cost.product_item_additional_cost_id === null) {
                    continue;
                }
                let new_quantity = parseFloat(additional_cost.unit_quantity) * this.quantity.toFixed(2);
                additional_cost.total_quantity = new_quantity.toString();
            }
            payload.item.additional_costs = this.state.additional_costs;
        }
        if (this.state.component_amount !== null) {
            payload.item.component_amount = this.state.component_amount.toString();
        }
        if (this.state.is_component_adjustment !== null) {
            payload.item.is_component_adjustment = this.state.is_component_adjustment;
        }
        return payload;
    };

    /**
     * Validate product line item
     *
     * @returns {Array}
     */
    validate() {
        let errors = super.validate();
        if (lang.isNil(this.product_id)) {
            errors.push('Product ID not defined');
        }
        return errors;
    };

    /**
     * Set internal values based on product entity
     *
     * @param {Api.Entity} entity - Product entity
     * @param {boolean} editing
     */
    populate(entity, editing) {;
        let data = {
            name: entity.get('name')
        };
        let materials = cloneDeep(entity.get('materials')),
            additional_costs = cloneDeep(entity.get('additional_costs'));

        // if the materials stored in the state are different than the products materials, we need to trigger an override
        let update_components = false;
        if (this.state.materials.length > 0 && materials.length > 0 && materials[0].id !== this.state.materials[0].product_item_material_id) {
            update_components = true;
        } else if (this.state.materials.length > 0 && materials.length === 0) {
            this.state.materials = [];
            this.state.additional_costs = [];
        };

        // only attach materials and additional costs if they exist and aren't set
        if (this.state.materials.length === 0 || update_components) {
            if (materials.length > 0) {
                for (let item of materials) {
                    item.product_item_material_id = item.id;
                    item.material_id = item.material.id;
                    item.id = uuid4();
                    item.name = item.material.name;
                    item.unit_id = item.material.unit_id;
                    item.unit_name = item.material.unit.name;
                    item.unit_abbreviation = item.material.unit.abbreviation;
                    item.unit_quantity = item.quantity;
                }
                data.materials = materials;
            }
        } else {
            data.materials = this.state.materials;
        }
        if (this.state.additional_costs.length === 0 || update_components) {
            if (additional_costs.length > 0) {
                for (let item of additional_costs) {
                    item.product_item_additional_cost_id = item.id;
                    item.additional_cost_id = item.additional_cost.id;
                    item.id = uuid4();
                    item.name = item.additional_cost.name;
                    item.unit_id = item.additional_cost.unit_id;
                    item.unit_name = item.additional_cost.unit.name;
                    item.unit_abbreviation = item.additional_cost.unit.abbreviation;
                    item.unit_quantity = item.quantity;
                }
                data.additional_costs = additional_costs;
            }
        } else {
            data.additional_costs = this.state.additional_costs;
        }

        if (update_components) {
            this.setStateChanged(true);
        }

        if (this.state.product_changed || this.state.quantity_changed) {
            editing = false;
        }

        // only update pricing info if product is current active, otherwise no price data will be available
        if (entity.get('status') === Api.Constants.ProductItems.Status.ACTIVE) {
            let price = Product.getPrice(entity, this.quantity);
            if (price !== null) {
                data.product_item_price_id = price[0];
                let product_price = price[1].toFixed(2);
                data.original_amount = product_price;
                if (!editing) {
                    data.amount = product_price;
                }
            }
            if (lang.isNil(data.original_amount)) {
                throw new Error('Unable to get price for quantity');
            }
        }
        this.state.product_changed = false;
        this.state.quantity_changed = false;
        this.setState(data, false);
    };

    /**
     * Prepare product line item by fetching pricing data from internal cache/API and populating internal values
     *
     * @param {boolean} editing
     * @returns {Promise}
     */
    prepare(editing = false) {
        return new Promise((resolve, reject) => {
            Product.getEntity(this.product_id).then((entity) => {
                this.populate(entity, editing);
                super.prepare(editing).then(resolve, reject);
            }, (error) => {
                reject(error);
            });
        });
    };
}

Product.__cache = new Map;

module.exports = Product;
