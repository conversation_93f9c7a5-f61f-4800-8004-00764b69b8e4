'use strict';

const Confirm = require('@ca-submodule/modal').Confirm;

const content_tpl = require('@cam-bid-creator-tpl/modals/line_items_missing.hbs');

class NoLineItems extends Confirm {
    constructor() {
        super();
        this.setTitle('Missing Line Items');
        this.setContent(content_tpl());
    };

    /**
     * Open modal
     *
     * @param {{resolve: function, reject: function}} promise
     */
    open({promise}) {
        this.state.promise = promise;
        super.open();
    };

    /**
     * Handle 'yes' action
     */
    handleYes() {
        this.close();
        this.state.promise.resolve();
    };

    /**
     * Handle 'no' action
     */
    handleNo() {
        this.close();
        this.state.promise.reject();
    };
}

module.exports = NoLineItems;
