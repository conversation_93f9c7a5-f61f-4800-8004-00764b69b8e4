<form class="m-template-form" data-js="form">
    <h4 data-js="type-name"></h4>
    <div class="f-field t-type">
        <label class="f-f-label">Type</label>
        <select class="f-f-input" data-js="type">
            <option>-- Select One --</option>
            <option value="1">Bid - Cover</option>
            <option value="2">Bid - Intro</option>
            <option value="3">Bid - Sections</option>
            <option value="4">Bid - Line Items</option>
            <option value="11">Bid - Terms &amp; Conditions</option>
            <option value="5">Bid - Images</option>
            <option value="10">Bid - Media</option>
            <option value="6">Scope of Work - Project Info</option>
            <option value="7">Scope of Work - Materials List</option>
            <option value="8">Scope of Work - Sections</option>
            <option value="9">Scope of Work - Images</option>
            <option value="12">Scope of Work - Terms &amp; Conditions</option>
        </select>
    </div>
    <div class="f-field">
        <label class="f-f-label">Name</label>
        <input class="f-f-input" type="text" data-js="name"/>
    </div>
    <div class="c-tf-row t-switch">
        <div class="f-field">
            <input class="f-f-input" type="checkbox" id="{{ns}}-is-default"
                   data-fx-form-input="switch" data-js="is_default">
            <label class="f-f-label t-label" for="{{ns}}-is-default">
                Default
                <span data-tooltip data-type="info">
                    Set as default template type.
                </span>
            </label>
        </div>
    </div>
    <div class="f-field t-content" data-js="codemirror-content">
        <label class="f-f-label">Content <span class="f-fl-optional">(Optional)</span></label>
        <textarea class="f-f-input" id="{{ns}}-template-content" name="content" data-js="content"></textarea>
    </div>
    <div class="f-field t-style" data-js="codemirror-styles">
        <label class="f-f-label">Styles <span class="f-fl-optional">(Optional)</span></label>
        <textarea class="f-f-input" id="{{ns}}-template-styles" name="styles" data-js="styles"></textarea>
    </div>
    <div class="f-field hidden" data-js="template-alias-wrapper">
        <label class="f-f-label">Alias <span class="f-fl-optional">(Optional)</span></label>
        <input class="f-f-input" type="text" data-js="alias"/>
    </div>
</form>