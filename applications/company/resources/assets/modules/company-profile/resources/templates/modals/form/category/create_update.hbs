<form data-js="form">
    <div class="s-grid t-space-bottom">
        <div class="i-g-row">
            <div class="i-gr-column t-size-12">
                <div class="f-field">
                    <label class="f-f-label">Name<span class="f-fl-required t-show">*</span></label>
                    <div class="f-f-input"><input type="text" value="" data-js="name" /></div>
                </div>
            </div>
        </div>
        <div class="i-g-row">
            <div class="i-gr-column t-size-12">
                <div class="f-field">
                    <label class="f-f-label">
                        Parent Filter
                        <span class="f-fl-optional">(Optional)</span>
                    </label>
                    <div class="f-f-input"><select data-js="parent_id"></select></div>
                </div>
            </div>
        </div>
    </div>
</form>
