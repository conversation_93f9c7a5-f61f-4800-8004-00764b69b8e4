<div class="c-g--p-page">
    <div class="m-page-header">
        <h3 class="c-ph-title" data-title data-js="name">{{title}}</h3>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary t-edit" data-js="edit" data-navigate="{{edit_route}}">
                <div data-text>Edit</div>
                <svg data-icon><use xlink:href="#remix-icon--design--edit-2-line"></use></svg>
            </button>
        </div>
    </div>
    <div class="m-general-info">
        <div class="c-gi-logo">
            <h5>Company Logo</h5>
            <div data-js="logo"></div>
        </div>
        <div class="c-gi-colors">
            <h5>Company Brand Colors</h5>
            <div class="c-gic-swatches">
                <div class="c-gics-container">
                    <div class="c-gicsc-title" data-js="title_color">
                        Primary
                    </div>
                    <div data-js="color"></div>
                </div>
                <div class="c-gics-container">
                    <div class="c-gicsc-title" data-js="title_color_hover">
                        Secondary
                    </div>
                    <div data-js="color-hover"></div>
                </div>
            </div>
        </div>
        <div class="c-gi-details">
            <h5>Company Details</h5>
            <div class="c-gid-wrapper">
                <div class="m-company-details">
                    <h6 class="c-cd-title">Address</h6>
                    <p data-js="address"></p>
                </div>
                <div class="m-company-details">
                    <h6 class="c-cd-title">Billing Address</h6>
                    <p data-js="billing"></p>
                </div>
                <div class="m-company-details">
                    <h6 class="c-cd-title">Phone Numbers</h6>
                    <p data-js="phones"></p>
                </div>
                <div class="m-company-details">
                    <h6 class="c-cd-title">Website</h6>
                    <p data-js="website"></p>
                </div>
            </div>
        </div>
    </div>
</div>