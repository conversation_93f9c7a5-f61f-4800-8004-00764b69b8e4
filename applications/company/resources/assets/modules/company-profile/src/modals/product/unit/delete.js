'use strict';

const Api = require('@ca-package/api');

const Confirm = require('@ca-submodule/modal').Confirm;

const content_tpl = require('@cam-company-profile-tpl/modals/product/unit/delete.hbs');

class Delete extends Confirm {
    constructor() {
        super();
        this.setTitle('Delete Unit');
        this.setContent(content_tpl());
        this.elem.unit = this.elem.content.fxFind('unit');
    };

    /**
     * Open modal
     *
     * @param {object} $0
     * @param {string} $0.category_id
     * @param {object} $0.promise
     * @returns {Modal}
     */
    open({unit_id, promise}) {
        this.startWorking();
        Api.Resources.Units()
            .retrieve(unit_id)
            .then(({data}) => {
                if (data.status !== Api.Constants.Units.Status.ACTIVE) {
                    promise.resolve(null);
                    return;
                }
                this.state.unit = data;
                this.elem.unit.text(data.name);
                this.resetWorking();
            }, error => {
                this.showErrorMessage('Unable to fetch unit info');
                console.log(error);
            });
        this.state.promise = promise;
        return super.open();
    };

    /**
     * Handle 'yes' response
     */
    handleYes() {
        this.startWorking();
        Api.Resources.Units()
            .partialUpdate(this.state.unit.id, {
                status: Api.Constants.Units.Status.ARCHIVED
            })
            .then(() => {
                this.resetWorking();
                this.state.promise.resolve(true);
            }, error => {
                let message = 'Unable to delete unit!';
                if (error.code === 1009) { //forbidden
                    message = 'Cannot delete unit with child units';
                }
                this.showErrorMessage(message);
                this.resetWorking();
            });
    };

    /**
     * Handle 'no' response
     */
    handleNo() {
        this.resetWorking();
        this.state.promise.resolve(null);
    };
}

module.exports = Delete;
