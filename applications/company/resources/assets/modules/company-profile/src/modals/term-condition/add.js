'use strict';

const TermConditionModal = require('../term-condition');
const modal_term_condition_tpl = require('@cam-company-profile-tpl/modals/term-condition.hbs');

module.exports = class extends TermConditionModal {
    constructor() {
        super(modal_term_condition_tpl({
            ns: 'add',
            info: window.fx_url.assets.IMAGE+'icons/info.png'
        }));
        this.namespace = 'add';

        this.elem.close_term_condition.on('click.fx', (e) => {
            e.preventDefault();
            this.clearForm();
            this.close();
            return false;
        });
    };

    open() {
        this.elem.title.html('Add Terms & Conditions');
        this.elem.type_container.show();
        this.elem.type_display.hide();
        super.open();
    };

    save() {
        this.formReset();
        this.fire('loading');
        let data = this.getData();
        data['type'] = parseInt(data['type']);

        this.getApiRequest().store(data).then((entity, response) => {
            this.fire('redraw');
            this.fire('loaded');
            super.close();
            this.clearForm();
        }, (error, response) => {
            switch (response.statusCode()) {
                case 422:
                    let item_errors = response.data().errors;
                    for (let item in item_errors) {
                        this.field[item].addError('fx-'+item, {message: item_errors[item]});
                    }
                    this.fire('loaded');
                    break;
                default:
                    this.fire('loaded');
                    super.close();
                    this.fire('error', error);
                    this.clearForm();
                    break;
            }
        });
    };
};
