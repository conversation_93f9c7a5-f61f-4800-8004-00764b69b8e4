'use strict';

const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector, onClickWatcher} = require("@ca-package/dom");

const bid_customization_tpl = require('@cam-company-profile-tpl/pages/main-pages/bid_customization.hbs');

class BidCustomization extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            active_menu_tab: 'content',
            tab_items: {
                content: {
                    title: 'Content',
                    icon: 'document--file-paper-line',
                    route: 'content'
                },
                templates: {
                    title: 'Templates',
                    icon: 'document--article-line',
                    route: 'templates'
                }
            }
        });
    };

    /**
     * Get available routes
     *
     * @readonly
     *
     * @returns {object}
     */
    static get routes() {
        return {
            content: {
                default: true,
                page: require('./bid-customization-pages/content')
            },
            templates: {
                path: '/templates',
                page: require('./bid-customization-pages/templates')
            }
        };
    };

    /**
     * Show loader overlay
     *
     * @param {boolean} [show=true]
     */
    showLoader(show = true) {
        this.elem.loader.toggle(show);
    };

    /**
     * Hide loader overlay
     */
    hideLoader() {
        return this.showLoader(false);
    };

    /**
     * Set active tab menu
     *
     * @param {string} id
     * @param {string} full_route
     */
    setActiveTabMenu(id, full_route = null) {
        if (this.state.active_menu_tab === id) {
            return;
        }
        this.state.tab_items[this.state.active_menu_tab].elem.removeClass('t-active');
        this.state.tab_items[id].elem.addClass('t-active');
        this.state.active_menu_tab = id;

        if (full_route !== null) {
            this.router.navigate(full_route);
            return;
        }
        this.router.navigate(`bid_customization.${this.state.tab_items[id].route}`);
    };

    /**
     * Turn edit mode on or off
     *
     * @param {boolean} status
     */
    setEditMode(status) {
        for (let id in this.state.tab_items) {
            if (this.state.active_menu_tab === id) {
                continue;
            }
            let $item =  this.state.tab_items[id];
            if (status) {
                $item.elem.addClass('t-disabled');
            } else {
                $item.elem.removeClass('t-disabled');
            }
        }
    };

    /**
     * Get container element for sub pages
     *
     * @returns {*}
     */
    getPageContainer() {
        return this.elem.page_container;
    };

    /**
     * Set active item from route id
     *
     * @param {string} route_id
     * @param {string} full_route
     */
    setActiveItemFromRoute(route_id, full_route) {
        for (let id of Object.keys(BidCustomization.routes)) {
            if (route_id.indexOf(id) !== 0) {
                continue;
            }
            this.setActiveTabMenu(id, full_route);
            break;
        }
    };

    /**
     * Handle a route change
     *
     * Automatically updates the selected tab based on the new routes id
     *
     * @param {object} current
     * @param {object} previous
     */
    onRouteChange({current, previous}) {
        this.setActiveTabMenu(current.name.split('.')[1]);
    };

    /**
     * Unload component
     *
     * @param {object} request
     */
    async unload(request, next) {
        this.router.unsubscribe('route-changed', this);
        await super.unload(request, next);
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        let route_id = this.router.current_route.name;
        this.setActiveItemFromRoute(route_id.replace('bid_customization.', ''), route_id);
        this.router.subscribe('route-changed', this, 'onRouteChange');

        await super.load(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);

        this.elem.tab_menu = findChild(root, jsSelector('tab-menu'));
        this.elem.tab_item = findChild(root, jsSelector('tab-item'));

        this.elem.loader = findChild(root, jsSelector('loader'));
        this.elem.page_container = findChild(root, jsSelector('page-container'));

        for (let item of this.elem.tab_item) {
            let $item = $(item);
            this.state.tab_items[$item.data('id')]['elem'] = $item;
        }

        let that = this;
        onClickWatcher(this.elem.tab_menu, jsSelector('tab-item'), function() {
            if ($(this).hasClass('t-disabled')) {
                return false;
            }
            let id = $(this).data('id');
            that.setActiveTabMenu(id);
            return false;
        }, true);
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return bid_customization_tpl({
            tab_items: this.state.tab_items
        });
    };
}

module.exports = BidCustomization;