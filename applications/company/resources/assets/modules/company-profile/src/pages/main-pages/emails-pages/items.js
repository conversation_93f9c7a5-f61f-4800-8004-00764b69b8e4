'use strict';

const Page = require('@ca-package/router/src/page');

const items_tpl = require('@cam-company-profile-tpl/pages/main-pages/emails-pages/items.hbs');

class Items extends Page {
    /**
     * Get available routes
     *
     * @readonly
     *
     * @returns {object}
     */
    static get routes() {
        return {
            manager: {
                default: true,
                page: require('./items-pages/manager')
            },
            create: {
                path: '/create',
                page: require('./items-pages/create_update')
            },
            update: {
                path: '/update/{email_id}',
                page: require('./items-pages/create_update'),
                bindings: {
                    email_id: 'uuid'
                }
            }
        };
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return items_tpl();
    };
}

module.exports = Items;
