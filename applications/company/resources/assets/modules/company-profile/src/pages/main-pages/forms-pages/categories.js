'use strict';

const Drop = require('tether-drop');
const lang = require('lodash/lang');

require('jquery-ui/ui/data');
require('jquery-ui/ui/scroll-parent');
require('jquery-ui/ui/version');
require('jquery-ui/ui/widget');
require('jquery-ui/ui/ie');
require('jquery-ui/ui/widgets/mouse');
require('jquery-ui/ui/widgets/sortable');
require('jquery-ui-touch-punch');

require('nestedSortable');

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');

const {createErrorMessage} = require('@cas-notification-toast-js/message/error');

const categories_tpl = require('@cam-company-profile-tpl/pages/main-pages/forms-pages/categories.hbs');
const list_row_tpl = require('@cam-company-profile-tpl/pages/main-pages/forms-pages/categories-components/list_row.hbs');
const list_row_menu_tpl = require('@cam-company-profile-tpl/pages/main-pages/forms-pages/categories-components/list_row_menu.hbs');

class Categories extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            drop_items: {}
        });
    };

    /**
     * Get available routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            create: {
                path: '/create',
                modal: require('./categories-pages/create_update')
            },
            update: {
                path: '/update/{category_id}',
                modal: require('./categories-pages/create_update'),
                bindings: {
                    category_id: 'uuid'
                }
            },
            delete: {
                path: '/delete/{category_id}',
                modal: require('./categories-pages/delete'),
                bindings: {
                    category_id: 'uuid'
                }
            }
        };
    };

    /**
     * Open add subcategory modal
     *
     * @param {string} id
     */
    addSubcategory(id) {
        this.router.navigate('forms.categories.create', {}, {parent_id: id});
    };

    /**
     * Open edit category modal
     *
     * @param {string} id
     */
    editCategory(id) {
        this.router.navigate('forms.categories.update', {category_id: id});
    };

    /**
     * Open delete category modal
     *
     * @param {string} id
     */
    deleteCategory(id) {
        this.router.navigate('forms.categories.delete', {category_id: id});
    };

    /**
     * Make actions menu on category row
     *
     * @param {jQuery} row
     * @param {boolean} delete_allowed
     */
    makeActionsMenu(row, delete_allowed = true) {
        let menu = {
            anchor: row.find('a[data-fx-dropdown="actions"]')
        };
        let id = row.attr('id');
        let action_menu = {
            add: {
                label: 'Add Sub-Filter',
                fn: 'addSubcategory'
            },
            edit: {
                label: 'Edit',
                fn: 'editCategory'
            }
        };
        if (delete_allowed) {
            action_menu['delete'] = {
                label: 'Delete',
                fn: 'deleteCategory'
            };
        }

        menu.content = $(list_row_menu_tpl({
            menu: action_menu
        }));
        let drop = new Drop({
            target: menu.anchor[0],
            content: menu.content[0],
            classes: 'drop-theme-basic',
            position: 'bottom right',
            openOn: 'click',
            pin: true
        });
        this.state.drop_items[id] = drop;

        menu.content.on('click.fx', 'a[data-fx-menu-id]', (e) => {
            e.preventDefault();
            let menu_id = $(e.currentTarget).data('fx-menu-id');
            let menu_fn = action_menu[menu_id].fn;
            this[menu_fn](id);
            drop.close();
        });
    };

    /**
     * Add category row to list
     *
     * @param {Object} element
     */
    addRow(element) {
        let new_row = $(list_row_tpl({
            id: element.id,
            name: element.name
        }));

        if (!lang.isNull(element.parent_id)) {
            let list_item_location = this.elem.categories_table.find(`[id="${element.parent_id}"]`);
            list_item_location.find('ul:eq(0)').append(new_row);
        } else {
            this.elem.categories_table.append(new_row);
        }
        this.makeActionsMenu(new_row);
    };

    /**
     * Build category list
     *
     * @param {Object} data
     */
    buildList(data) {
        for (let item in data) {
            let element = data[item];
            if (lang.isUndefined(element.id)) {
                element = element.data;
            }
            this.addRow(element);

            if (element.categories.length > 0) {
                this.buildList(element.categories);
            }
        }
    };

    /**
     * Change parent category id
     *
     * @param {string} id
     * @param {string} parent
     */
    editParent(id, parent) {
        let data = {
            'parent_id': parent
        };

        Api.Resources.CompanyFormCategories().partialUpdate(id, data).fail((error) => {
            this.router.main_route.layout.toasts.addMessage(createErrorMessage('Unable to change parent for category, please contact support'));
            console.log(error);
        });
    };

    /**
     * Clear category list
     */
    clear() {
        this.elem.categories_table.empty();
    };

    /**
     * Fetch category data from server
     */
    async fetchData() {
        try {
            let collection = await Api.Resources.CompanyFormCategories()
                .sort('name', 'asc').accept('application/vnd.adg.fx.list-v1+json').all();
            this.buildList(collection.state.entities);
        } catch (e) {
            console.log(e);
        }
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        let forms = this.getParentByName('forms');
        forms.showLoader();
        await super.load(request, next);
        await this.fetchData();
        forms.hideLoader();
    };

    /**
     * Refresh page
     *
     * @param {object} request
     */
    refresh(request) {
        if (request.query.update === 'true') {
            this.clear();
            this.fetchData().catch(e => console.log(e));
        }
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        this.clear();
        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);

        this.elem.categories_table = this.elem.root.find('ul');
        this.elem.add_category = this.elem.root.fxFind('add-category');
        this.elem.loader = this.elem.root.fxFind('loader');
        this.elem.categories_table.nestedSortable({
            listType: 'ul',
            handle: '.category',
            items: 'li',
            toleranceElement: '> .category',
            sort: (e) => {
                let element = e.originalEvent.target;
                let item_dragged = $(element).closest('li');
                this.state.drop_items[item_dragged.attr('id')].close();
            },
            relocate: (e) => {
                let element = e.originalEvent.target;
                let item_dragged = $(element).closest('li');
                let parent_id = item_dragged.parents('li').attr('id');

                if(lang.isUndefined(parent_id)) {
                    parent_id = null;
                }
                this.editParent(item_dragged.attr('id'), parent_id);
            }
        });

        this.elem.add_category.fxClick(() => this.router.navigate('forms.categories.create'), true);
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return categories_tpl({
            back_route: 'forms.items.manager',
            add_route: 'forms.categories.create'
        });
    };
}

module.exports = Categories;
