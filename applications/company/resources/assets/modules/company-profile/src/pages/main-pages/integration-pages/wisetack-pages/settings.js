'use strict';

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector, onEvent} = require("@ca-package/dom");

const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/switch'));
const FormValidator = require('@ca-submodule/validator');
const Tooltip = require('@ca-submodule/tooltip');

const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');
const {createErrorMessage} = require('@cas-notification-toast-js/message/error');

const settings_tpl = require('@cam-company-profile-tpl/pages/main-pages/integrations-pages/wisetack-pages/settings.hbs');
const {PRICING_PLANS} = require("@cac-js/wisetack");

class Settings extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            integrations: parent.getParentByName('integrations')
        });
    };

    /**
     * Populate fields using data object
     *
     * @param {object} data
     */
    populate(data) {
        this.elem.financing_required_for_all_projects.attr('checked', data.wisetack_financing_required_for_all_projects);
        this.elem.project_financing_enabled_by_default.attr('checked', data.wisetack_project_financing_enabled_by_default);

        this.elem.pricing_plan_radios
            .find(`input[name="pricing_plan"][value="${data.wisetack_current_pricing_plan}"]`)
            .prop('checked', true);

        if (this.isWisetackMerchantApproved()) {
            this.elem.current_pricing_plan_wrapper.removeClass('t-hidden');
        }
    };

    /**
     * Check if merchant is approved
     *
     * @returns {boolean}
     */
    isWisetackMerchantApproved() {
        return !!profile_data.wisetack.is_merchant_approved
    }

    /**
     * Save Wisetack settings
     */
    save() {
        this.clearError();
        this.state.integrations.showLoader();

        let data = {
            settings: {
                wisetack_financing_required_for_all_projects: this.elem.financing_required_for_all_projects.is(':checked') ?? false,
                wisetack_project_financing_enabled_by_default: this.elem.project_financing_enabled_by_default.is(':checked') ?? false
            }
        };

        if (this.isWisetackMerchantApproved()) {
            const value = this.elem.pricing_plan_radios
                .find('input[name="pricing_plan"]:checked')
                .val();
            data.settings.wisetack_current_pricing_plan = parseInt(value ?? null);
        }

        Api.Resources.Companies().partialUpdate('current', data)
            .then((response) => {
                this.state.integrations.hideLoader();
                const message = createSuccessMessage('Wisetack info saved successfully');
                this.router.main_route.layout.toasts.addMessage(message);
                this.router.navigate('integrations.wisetack.details');
            })
            .catch((error) => {
                console.error(error);
                this.state.integrations.hideLoader();
                const response = error.response || {};
                if (response.statusCode === 422) {
                    const itemErrors = response.data?.errors || [];
                    console.log(itemErrors);
                } else {
                    const message = createErrorMessage('Unable to save Wisetack Settings, please contact support');
                    this.router.main_route.layout.toasts.addMessage(message);
                }
            });
    };

    /**
     * Fetch settings data from server
     */
    async fetchData() {
        try {
            let {data: entity} = await Api.Resources.Companies().
            fields(['id']).relations({
                'settings': {}
            }).retrieve('current');
            this.populate(entity.settings);
        } catch (e) {}
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {

        this.elem.cancel.prop('disabled', false);
        this.state.validator.reset();
        this.elem.root.scrollTop(0);

        this.elem.current_pricing_plan.val();
        this.clearError();

        this.state.integrations.setEditMode(false);
        await super.unload(request, next);
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        await super.load(request, next);
        this.state.integrations.showLoader();
        this.state.integrations.setEditMode(true);
        await this.fetchData();
        this.state.integrations.hideLoader();
        await super.load(request, next);
    };

    /**
     * Set message and show error container
     *
     * @param {string} message
     */
    setError(message) {
        this.elem.form.scrollTop(0);
        this.elem.error.text(message).addClass('t-show');
    }

    /**
     * Clear and hide error container
     */
    clearError() {
        this.elem.error.text('').removeClass('t-show');
    };

    /**
     * Initialize form to use on submit
     */
    initForm() {
        FormInput.init(this.elem.financing_required_for_all_projects);
        FormInput.init(this.elem.project_financing_enabled_by_default);

        this.state.validator = FormValidator.create(this.elem.form, {
            financing_required_for_all_projects: {},
            project_financing_enabled_by_default: {},
        }, {
            validate_event: true,
            error_event: true
        }).on('submit', () => this.save())
            .on('validate', () => this.clearError())
            .on('error', () => this.setError('Please review form errors below'));

        let pricing_plan_radios = Object.entries(PRICING_PLANS)
            .map(([key, value]) => `
                <label><input class="f-f-input" type="radio" name="pricing_plan" value="${key}">${value["full_name_option"]}</label>`)
            .join("");

        this.elem.pricing_plan_radios.html(pricing_plan_radios);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        Tooltip.initAll(root);

        this.elem.save = findChild(root, jsSelector('save'));
        this.elem.cancel = findChild(root, jsSelector('cancel'));
        this.elem.form = findChild(root, jsSelector('form'));
        this.elem.error = findChild(root, jsSelector('error'));

        this.elem.financing_required_for_all_projects = findChild(root, jsSelector('financing-required-for-all-projects'));
        this.elem.project_financing_enabled_by_default = findChild(root, jsSelector('project-financing-enabled-by-default'));
        this.elem.current_pricing_plan = findChild(root, jsSelector('current-pricing-plan'));
        this.elem.pricing_plan_radios = findChild(root, jsSelector('pricing-plan-radios'));
        this.elem.current_pricing_plan_wrapper = findChild(root, jsSelector('current-pricing-plan-wrapper'));
        this.initForm();

        onEvent(this.elem.save, 'click', (e) => {
            e.preventDefault();
            this.elem.form.trigger('submit');
            return false;
        });
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return settings_tpl({
            cancel_route: 'integrations.wisetack.details'
        });
    };
}

module.exports = Settings;