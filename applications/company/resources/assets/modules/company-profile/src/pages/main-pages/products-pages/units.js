'use strict';

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');

const Table = require('@ca-submodule/table').Base;

const units_tpl = require('@cam-company-profile-tpl/pages/main-pages/products-pages/units.hbs');

class Units extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            table: null,
            table_scope: {
                sorts: {
                    name: Table.Sort.ASC
                }
            }
        });
    };

    /**
     * Get available routes
     *
     * @readonly
     *
     * @returns {object}
     */
    static get routes() {
        return {
            create: {
                path: '/create',
                modal: require('./units-pages/create_update')
            },
            update: {
                path: '/update/{unit_id}',
                modal: require('./units-pages/create_update'),
                bindings: {
                    unit_id: 'uuid'
                }
            },
            delete: {
                path: '/delete/{unit_id}',
                modal: require('./units-pages/delete'),
                bindings: {
                    unit_id: 'uuid'
                }
            }
        };
    };

    /**
     * Create the DataTable and apply settings and defaults
     */
    createTable() {
        this.state.table = new Table(this.elem.table)
            .on('row_click', (data) => {
                this.router.navigate('products.units.update', {
                    unit_id: data.id
                })
            });


        // set header config
        this.state.table.setHeader();

        // set toolbar config
        this.state.table.setToolbar({
            filter: false,
            settings: false
        });

        // set columns config
        this.state.table.setColumns({
            name: {
                label: 'Name',
                responsive: 1,
                width: '55%'
            },
            abbreviation: {
                label: 'Abbreviation',
                responsive: 3,
                width: '40%'
            }
        });

        //set row action config
        this.state.table.setRowActions({
            edit: {
                label: 'Edit',
                action: (data) =>
                    this.router.navigate('products.units.update', {
                        unit_id: data.id
                    })
            },
            delete: {
                label: 'Delete',
                negate: true,
                action: (data) =>
                    this.router.navigate('products.units.delete', {
                        unit_id: data.id
                    })
            }
        });

        this.state.table.setAjax(Api.Resources.Units, (request) => {
            request.filter('status', Api.Constants.Units.Status.ACTIVE);
        });

        // modify table state
        if (this.state.table_scope) {
            this.state.table.setState(this.state.table_scope);
        }
    };

    /**
     * Build or draw table
     */
    loadTable() {
        this.createTable();
        this.state.table.setState(this.state.table_scope);
        this.state.table.build();
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        let products = this.getParentByName('products');
        products.showLoader();
        await super.load(request, next);
        this.loadTable();
        products.hideLoader();
    };

    /**
     * Refresh page
     *
     * @param request
     */
    refresh(request) {
        this.state.table.draw();
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        this.state.table.destroy();
        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.table = this.elem.root.fxFind('table-container');
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return units_tpl({
            back_route: 'products.items.manager',
            add_route: 'products.units.create'
        });
    };
}

module.exports = Units;
