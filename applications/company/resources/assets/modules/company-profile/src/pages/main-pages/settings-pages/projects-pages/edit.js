'use strict';

const autosize = require('autosize');

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector, onEvent} = require("@ca-package/dom");

const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');
const {createErrorMessage} = require('@cas-notification-toast-js/message/error');
const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/switch'));
const Tooltip = require('@ca-submodule/tooltip');

const FormValidator = require("@cas-validator-js");
const KeyCodes = require('@cac-js/data/event_key_codes');

const projects_tpl = require('@cam-company-profile-tpl/pages/main-pages/settings-pages/projects-pages/edit.hbs');
const project_type_row_tpl = require('@cam-company-profile-tpl/pages/main-pages/settings-pages/projects-pages/projects-components/project_type_edit_row.hbs');
const result_type_row_tpl = require('@cam-company-profile-tpl/pages/main-pages/settings-pages/projects-pages/projects-components/result_type_edit_row.hbs');

class Edit extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            settings: parent.getParentByName('settings'),
            project_type_items: {},
            result_type_items: {},
        });
    };

    /**
     * Prepare entity to save
     *
     * @returns {object}
     */
    buildEntity() {
        let data = {
            recently_completed_status: parseFloat(this.state.validator.getInputElem('recently_completed_status').val()),
            settings: {
                project_sales_collaboration: this.elem.project_sales_collaboration.is(':checked'),
                project_install_collaboration: this.elem.project_install_collaboration.is(':checked'),
                marketing_source_required: this.elem.marketing_source_required.is(':checked'),
                project_costing_for_primary_only: this.elem.project_costing_for_primary_only.is(':checked'),
                result_required: this.elem.result_required.is(':checked'),
                project_type_required: this.elem.project_type_required.is(':checked'),
                default_project_summary: this.elem.default_project_summary.val()
            }
        }

        if (Object.keys(this.state.project_type_items).length > 0) {
            let project_types = [];
            for (let item in this.state.project_type_items) {
                let this_item = this.state.project_type_items[item];
                let type = {
                    name: this_item.fields.name.val(),
                    status: this_item.fields.toggle.is(':checked') ? Api.Constants.ProjectTypes.Status.ACTIVE : Api.Constants.ProjectTypes.Status.INACTIVE
                };
                if (this_item.data !== null) {
                    type['id'] = this_item.data.id;
                }
                project_types.push(type);
            }
            data['project_types'] = project_types;
        }

        if (Object.keys(this.state.result_type_items).length > 0) {
            let result_types = [];
            for (let key in this.state.result_type_items) {
                let item = this.state.result_type_items[key];
                let type = {
                    name: item.fields.name.val(),
                    type: Api.Constants.ResultTypes.Type.PROJECT,
                    status: item.fields.toggle.is(':checked')
                        ? Api.Constants.ResultTypes.Status.ACTIVE
                        : Api.Constants.ResultTypes.Status.INACTIVE
                };
                if (item.data !== null && item.data.id) {
                    type.id = item.data.id;
                }
                result_types.push(type);
            }
            data['result_types'] = result_types;
        }

        return data;
    };

    /**
     * Save settings data to server
     */
    save() {
        this.state.settings.showLoader();
        let data = this.buildEntity();

        Api.Resources.Companies().partialUpdate('current', data).then(({data}) => {
            setTimeout(() => {
                this.state.settings.hideLoader();
                let message = createSuccessMessage(`Company project settings saved successfully`);
                this.router.main_route.layout.toasts.addMessage(message);
                this.router.navigate('settings.projects.details');
            }, 2000);
        }, (error, response) => {
            switch (response.statusCode()) {
                case 422:
                    let item_errors = response.data().errors;
                    console.error(item_errors)
                    this.state.settings.hideLoader();
                    break;
                default:
                    let message = createErrorMessage('Unable to save company project settings, please contact support');
                    this.router.main_route.layout.toasts.addMessage(message);
                    break;
            }
        });
    };

    populate(data) {
        const {
            settings = {},
            recently_completed_status = '',
            project_types = [],
            result_types = []
        } = data;

        // Assign settings properties
        const settingsMap = {
            marketing_source_required: this.elem.marketing_source_required,
            project_sales_collaboration: this.elem.project_sales_collaboration,
            project_install_collaboration: this.elem.project_install_collaboration,
            project_costing_for_primary_only: this.elem.project_costing_for_primary_only,
            result_required: this.elem.result_required,
            project_type_required: this.elem.project_type_required
        };

        Object.entries(settingsMap).forEach(([key, elem]) => {
            elem.prop('checked', settings[key] || false); // Default to false if undefined
        });

        this.state.validator.getInputElem('recently_completed_status').val(recently_completed_status || '');

        let default_project_summary = settings.default_project_summary !== undefined ? settings.default_project_summary : '';
        this.elem.default_project_summary.val(default_project_summary).trigger('change');
        this.clearTable();

        // Add default rows if necessary
        if (project_types.length === 0) this.addProjectTypeRow();
        if (result_types.length === 0) this.addResultTypeRow();

        project_types
            .filter(item => item.status !== Api.Constants.ProjectTypes.Status.ARCHIVED)
            .forEach(item => this.addProjectTypeRow(item));

        result_types
            .filter(item => item.status !== Api.Constants.ResultTypes.Status.ARCHIVED)
            .forEach(item => this.addResultTypeRow(item));
    };

    /**
     * Remove project type row
     *
     * @param {number} index
     */
    removeProjectTypeRow(index) {
        this.state.project_type_items[index].elem.remove();
        delete this.state.project_type_items[index];

        if (Object.keys(this.state.project_type_items).length === 0) {
            this.addProjectTypeRow();
        }
    };

    /**
     * Add project type row
     *
     * @param {object} data
     */
    addProjectTypeRow(data = null) {
        let index = Object.keys(this.state.project_type_items).length,
            row = $(project_type_row_tpl({
                index,
                name: data === null ? '' : data.name,
                active_status: data === null ? true : data.status === Api.Constants.ProjectTypes.Status.ACTIVE
            }));

        let toggle = findChild(row, jsSelector('toggle')),
            remove_row = findChild(row, jsSelector('remove')),
            name = findChild(row, jsSelector('name'));

        let toggle_form_input = FormInput.init(toggle);


        let that = this;
        onEvent(remove_row, 'click',  function (e) {
            e.preventDefault();
            let index = $(this).data('index');
            that.removeProjectTypeRow(index);
            return false;
        });
        Tooltip.initAll(row);

        this.state.project_type_items[index] = {
            data,
            elem: row,
            fields: {
                toggle_form_input,
                toggle,
                name
            }
        };
        onEvent(name, 'keydown', e => {
            switch (e.keyCode) {
                case KeyCodes.ENTER:
                    return false;
            }
        });

        this.elem.project_types_table.append(row);
    };

    removeResultTypeRow(index) {
        this.state.result_type_items[index].elem.remove();
        delete this.state.result_type_items[index];

        // If no more result type rows, optionally add one
        if (Object.keys(this.state.result_type_items).length === 0) {
            this.addResultTypeRow();
        }
    }

    addResultTypeRow(data = null) {
        let index = Object.keys(this.state.result_type_items).length;
        let row = $(result_type_row_tpl({
            index,
            name: data === null ? '' : data.name,
            active_status: data === null
                ? true
                : data.status === Api.Constants.ResultTypes.Status.ACTIVE
        }));

        let toggle = findChild(row, jsSelector('toggle')),
            remove_row = findChild(row, jsSelector('remove')),
            name = findChild(row, jsSelector('name'));

        let toggle_form_input = FormInput.init(toggle);

        onEvent(remove_row, 'click', (e) => {
            e.preventDefault();
            let idx = $(remove_row).data('index');
            this.removeResultTypeRow(idx);
            return false;
        });
        Tooltip.initAll(row);

        this.state.result_type_items[index] = {
            data,
            elem: row,
            fields: {
                toggle_form_input,
                toggle,
                name
            }
        };
        onEvent(name, 'keydown', e => {
            if (e.keyCode === KeyCodes.ENTER) {
                return false;
            }
        });

        this.elem.result_types_table.append(row);
    }


    /**
     * Fetch settings data from server
     */
    async fetchData() {
        try {
            let {data: entity} = await Api.Resources.Companies().
            fields(['recently_completed_status']).relations({
                'settings': {},
                'project_types': {
                    sorts: {
                        'name': 'asc'
                    }
                },
                'result_types': {
                    filters: {
                        'type': Api.Constants.ResultTypes.Type.PROJECT
                    },
                    sorts: {
                        'name': 'asc'
                    }
                }
            }).retrieve('current');
            this.populate(entity);
        } catch (e) {}
    };

    /**
     * Clear table and reset object
     */
    clearTable() {
        for (let item in this.state.project_type_items) {
            let $item = this.state.project_type_items[item].elem;
            $item.remove();
        }
        this.state.project_type_items = {};

        for (let item in this.state.result_type_items) {
            let $item = this.state.result_type_items[item].elem;
            $item.remove();
        }
        this.state.result_type_items = {};
    };

    /**
     * Refresh page
     *
     * @param {object} request
     */
    async refresh(request) {
        await this.fetchData();
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        await super.load(request, next);
        this.state.settings.setEditMode(true);
        this.state.settings.showLoader();
        await this.fetchData();
        this.state.settings.hideLoader();
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        this.state.validator.getInputElem('recently_completed_status').val('');
        this.elem.default_project_summary.val('');

        this.state.validator.reset();
        this.elem.root.scrollTop(0);
        this.clearError();
        this.state.settings.setEditMode(false);
        this.clearTable();
        await super.unload(request, next);
    };

    /**
     * Set message and show error container
     *
     * @param {string} message
     */
    setError(message) {
        this.elem.form.scrollTop(0);
        this.elem.error.text(message).addClass('t-show');
    };

    /**
     * Clear and hide error container
     */
    clearError() {
        this.elem.error.text('').removeClass('t-show');
    };

    /**
     * Initialize form to use on submit
     */
    initForm() {
        this.state.validator = FormValidator.create(this.elem.form, {
            recently_completed_status: {
                required: true,
                type: 'digits'
            }
        }, {
            validate_event: true,
            error_event: true
        })
            .on('submit', () => this.save())
            .on('validate', () => {})
            .on('error', () => this.setError('Please review errors below'));
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        Tooltip.initAll(root);

        this.elem.form = findChild(root, jsSelector('form'));
        this.elem.error = findChild(root, jsSelector('error'));
        this.elem.save = findChild(root, jsSelector('save'));
        this.elem.project_types_table = findChild(root, jsSelector('project-types-table'));
        this.elem.add_project_type = findChild(root, jsSelector('add-project-type'));

        this.elem.result_types_table = findChild(root, jsSelector('result-types-table'));
        this.elem.add_result_type = findChild(root, jsSelector('add-result-type'));
        this.elem.default_project_summary = findChild(root, jsSelector('default_project_summary'));

        this.initForm();

        autosize(this.elem.default_project_summary[0]);
        this.elem.default_project_summary.fxEvent('change', () => {
            autosize.update(this.elem.default_project_summary);
        });

        this.elem.project_sales_collaboration = findChild(root, jsSelector('project-sales-collaboration'));
        FormInput.init(this.elem.project_sales_collaboration);

        this.elem.project_install_collaboration = findChild(root, jsSelector('project-install-collaboration'));
        FormInput.init(this.elem.project_install_collaboration);

        this.elem.marketing_source_required = findChild(root, jsSelector('marketing-source-required'));
        FormInput.init(this.elem.marketing_source_required);

        this.elem.project_costing_for_primary_only = findChild(root, jsSelector('project-costing-for-primary-only'));
        FormInput.init(this.elem.project_costing_for_primary_only);

        this.elem.result_required = findChild(root, jsSelector('result-required'));
        FormInput.init(this.elem.result_required);

        this.elem.project_type_required = findChild(root, jsSelector('project-type-required'));
        FormInput.init(this.elem.project_type_required);

        onEvent(this.elem.add_project_type, 'click', (e) => {
            e.preventDefault();
            this.addProjectTypeRow();
            return false;
        });

        if (this.elem.add_result_type) {
            onEvent(this.elem.add_result_type, 'click', (e) => {
                e.preventDefault();
                this.addResultTypeRow();
                return false;
            });
        }

        onEvent(this.elem.save, 'click', (e) => {
            e.preventDefault();
            this.elem.form.trigger('submit');
            return false;
        });
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return projects_tpl({
            title: 'Edit Project Settings',
            cancel_route: 'settings.projects.details'
        });
    };
}

module.exports = Edit;