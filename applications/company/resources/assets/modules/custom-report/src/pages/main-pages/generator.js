'use strict';

const Papa = require('papaparse');
const moment = require('moment-timezone');

const FormValidator = require('@ca-submodule/validator');
const Page = require('@ca-package/router/src/page');
const sleep = require('@cac-js/utils/sleep');
const Number = require('@cac-js/utils/number');

const FormInput = require('@ca-submodule/form-input');
const NumberInput = require('@ca-submodule/form-input/src/number');
FormInput.use(require('@ca-submodule/form-input/src/date'));
FormInput.use(require('@ca-submodule/form-input/src/static_dropdown'));
FormInput.use(NumberInput);
const Table = require('@ca-submodule/table').Base;
const {createInfoMessage} = require('@cas-notification-toast-js/message/info');
const {createErrorMessage} = require('@cas-notification-toast-js/message/error');

const Api = require('../../api');
const log = require('../../log');

const generator_tpl = require('@cam-custom-report-tpl/pages/main-pages/generator.hbs');
const date_input_tpl = require('@cam-custom-report-tpl/pages/main-pages/generator-components/inputs/date.hbs');
const text_input_tpl = require('@cam-custom-report-tpl/pages/main-pages/generator-components/inputs/text.hbs');
const dropdown_input_tpl = require('@cam-custom-report-tpl/pages/main-pages/generator-components/inputs/dropdown.hbs');
const number_input_tpl = require('@cam-custom-report-tpl/pages/main-pages/generator-components/inputs/number.hbs');

const NumberTypes = {
    float: 'FLOAT',
    integer: 'INT',
    currency: 'CURRENCY',
    percentage: 'PERCENTAGE',
    feet: 'FEET',
    inches: 'INCHES'
};

/**
 * @memberof module:CustomReport/Pages/MainPages
 */
class Generator extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            report: null,
            result: null,
            inputs: {},
            form_validated: false,
            result_retry_count: 10,
            result_retry_wait: 2000,
            generate_message: null,
            table: null
        });
    };

    /**
     * Fetch report by id
     *
     * @returns {Promise<void>}
     */
    async fetchReport(id) {
        let {data: {data: report}} = await Api.Resources.CompanyCustomReports()
            .fields(['id', 'name'])
            .relation('config')
            .retrieve(id);
        return report;
    };

    /**
     * Load report by id
     *
     * If previous report is loaded and doesn't match, it will be unloaded first.
     *
     * @param {string} id
     * @returns {Promise<{data: object, inputs: Promise<void>}>}
     */
    async loadReport(id) {
        if (this.state.report !== null) {
            if (this.state.report.data.id === id) {
                return this.state.report;
            }
            this.resetReport();
        }
        let report = await this.fetchReport(id),
            inputs = this.buildInputs(report.config.inputs);
        if (this.router.current_route.is_initial) {
            this.parent.selectReportById(report.id);
        }
        this.state.report = {data: report, inputs};
        return this.state.report;
    };

    /**
     * Clear generate flash message from layout
     */
    clearGenerateMessage() {
        if (this.state.generate_message === null) {
            return;
        }
        this.state.generate_message.delete();
        this.state.generate_message = null;
    };

    /**
     * Fetch individual result by id
     *
     * @param {string} id
     * @param {number} [count=0]
     * @returns {Promise<*>}
     */
    async fetchResult(id, count = 0) {
        let {data: {data: result}} = await Api.Resources.CompanyCustomReportResults()
            .accept('application/vnd.adg.fx.detail-v1+json')
            .retrieve(id);
        if (result.status === Api.Constants.CompanyCustomReportResults.Status.GENERATING) {
            if (count === 1) {
                this.state.generate_message = createInfoMessage('Generating report...', {delete_after: null});
                this.parent.layout.toasts.addMessage(this.state.generate_message);
            } else if (count === this.state.result_retry_count) {
                this.clearGenerateMessage();
                let message = createErrorMessage('Unable to generate report within allotted time, please try again later');
                this.parent.layout.toasts.addMessage(message);
                throw new Error('Report not generated within time allotment');
            }
            await sleep(this.state.result_retry_wait);
            result = await this.fetchResult(id, count + 1);
        } else if (
            result.status === Api.Constants.CompanyCustomReportResults.Status.GENERATED ||
            result.status === Api.Constants.CompanyCustomReportResults.Status.FAILED
        ) {
            this.clearGenerateMessage();
            if (result.status === Api.Constants.CompanyCustomReportResults.Status.FAILED) {
                throw new Error('Failed to generate report');
            }
        }
        return result;
    };

    /**
     * Create table with row data
     *
     * @param {object} report
     * @param {object} result
     * @param {object[]} data
     * @returns {module:Table.Base}
     */
    createTable(report, result, data) {
        let table = new Table(this.elem.root, {
            class: 't-static'
        });
        table.setHeader({
            name: `${report.name}<span class="c-g--header-generated">Generated at ${moment(result.generated_at).tz(this.router.main_route.layout.user.timezone).format('MM/DD/YYYY h:mma')}</span>`
        });
        table.setToolbar({
            filter: false,
            settings: false
        });
        let columns = {},
            i = 0;
        for (let column of report.config.columns) {
            let key = `col_${++i}`,
                column_config = {
                    label: column.title
                };
            switch (column.type) {
                case 'date':
                case 'datetime':
                    let formats = {
                        date: 'MM/DD/YYYY',
                        datetime: 'MM/DD/YYYY h:mma'
                    };
                    column_config.value = (row, type) => {
                        if (row[key] === '' || type !== 'display') {
                            return row[key];
                        }
                        return moment(row[key]).format(formats[column.type]);
                    };
                    break;
                case 'currency':
                    column_config.value = (row, type) => {
                        if (row[key] === '' || type !== 'display') {
                            return row[key];
                        }
                        return Number.toCurrency(row[key]);
                    };
                    break;
                case 'link':
                    column_config.value = (row, type) => {
                        if (row[key] === '' || type !== 'display') {
                            return row[key];
                        }
                        return `<a href="${row[key]}" target="_blank">${column.link_title || row[key]}</a>`;
                    };
                    break;
            }
            columns[key] = column_config;
        }
        table.setColumns(columns);
        table.setButtons({
            download: {
                label: 'Download',
                action: () => window.open(`${result.file_media_urls.original}?download=true`, '_blank'),
                type_class: 't-primary'
            }
        });
        table.setData(data);
        table.build();

        return table;
    };

    /**
     * Parse CSV pulled from URL
     *
     * @param {string} url
     * @returns {Promise<object[]>}
     */
    parseCsv(url) {
        return new Promise((resolve, reject) => {
            Papa.parse(url, {
                download: true,
                skipEmptyLines: true,
                complete: ({data}) => {
                    data.shift(); // drop header line
                    let rows = [];
                    for (let datum of data) {
                        let row = {},
                            i = 1;
                        for (let value of datum) {
                            row[`col_${i++}`] = value;
                        }
                        rows.push(row);
                    }
                    resolve(rows);
                },
                error: error => reject(error)
            })
        });
    };

    /**
     * Load individual result
     *
     * @param {object} report
     * @param {string} id
     * @returns {Promise<object>}
     */
    async loadResult(report, id) {
        if (this.state.result !== null) {
            if (this.state.result.id === id) {
                return this.state.result;
            }
            this.resetResult();
        }
        // @todo if large (by row count or file size), warn user
        let result = await this.fetchResult(id),
            rows = await this.parseCsv(result.file_media_urls.original);
        this.state.table = this.createTable(report.data, result, rows);
        this.elem.root.addClass('t-results');
        this.state.result = result;
        return result;
    };

    /**
     * Reset result to default state
     */
    resetResult() {
        if (this.state.table !== null) {
            this.state.table.destroy();
            this.state.table = null;
        }
        this.elem.root.removeClass('t-results');
        this.clearGenerateMessage();
        this.state.result = null;
    };

    /**
     * Reset report to default state
     *
     * If result is defined, it is also reset.
     */
    resetReport() {
        if (this.state.report === null) {
            return;
        }
        let {inputs} = this.state.report;
        inputs.then(() => {
            for (let name of Object.keys(this.state.inputs)) {
                let input = this.state.inputs[name];
                switch (input.type) {
                    case 'date':
                        input.input.destroy();
                        input.parsley.destroy();
                        break;
                    case 'number':
                        input.input.destroy();
                        input.parsley.destroy();
                        break;
                }
                input.elem.root.remove();
            }
        });
        this.state.form.destroy();
        this.parent.elem.inputs_wrapper.removeClass('t-show');
        if (this.state.result !== null) {
            this.resetResult();
        }
        this.state.report = null;
    };

    /**
     * Get payload for run request
     *
     * @returns {object}
     */
    getRunPayload() {
        let payload = {};
        for (let name of Object.keys(this.state.inputs)) {
            payload[name] = this.state.inputs[name].elem.input.val();
        }
        return payload;
    };

    /**
     * Handle run request (form submit)
     *
     * @returns {Promise<void>}
     */
    async run() {
        try {
            this.parent.showLoader();
            let {data: {result_id}} = await Api.Resources.CompanyCustomReports()
                .method(Api.Request.Method.PUT)
                .custom(`${this.state.report.data.id}/run`, this.getRunPayload());
            this.router.navigate('generator', {report_id: this.state.report.data.id}, {result_id});
        } catch (error) {
            if (error.response && error.response.statusCode() === 422) {
                let errors = error.response.data().errors;
                for (let name of Object.keys(errors)) {
                    if (this.state.inputs[name] === undefined) {
                        continue;
                    }
                    this.state.inputs[name].parsley.addError(name, {message: errors[name], updateClass: true});
                }
                this.parent.hideLoader();
                return;
            }
            throw error;
        }
    };

    /**
     * Build inputs from config and add to header
     *
     * @param {array} inputs
     * @returns {Promise<void>}
     */
    async buildInputs(inputs) {
        this.state.form = FormValidator.init(this.parent.elem.inputs_wrapper)
            .on('form:validate', () => {
                this.state.form_validated = true;
            })
            .on('form:submit', () => {
                this.run().catch(error => {
                    this.parent.hideLoader();
                    this.handleError('run', error, {
                        message: 'Unable to run report, please contact support'
                    });
                });
                return false;
            });
        if (inputs.length > 0) {
            this.parent.elem.inputs.addClass('t-has-inputs');
            for (let {type, name, label, options, number_type} of inputs) {
                let input = {type};
                switch (type) {
                    case 'date':
                        input.elem = {
                            root: $(date_input_tpl({name, label}))
                        };
                        this.parent.elem.inputs.append(input.elem.root);
                        input.elem.input = input.elem.root.fxFind('input');
                        input.input = FormInput.init(input.elem.input);
                        input.parsley = input.elem.input.parsley({
                            required: true,
                            requiredMessage: 'Required'
                        });
                        input.elem.input.on('date:change', () => {
                            if (!this.state.form_validated) {
                                return;
                            }
                            input.parsley.validate();
                            input.parsley.removeError(name);
                        });
                        break;
                    case 'text':
                        input.elem = {
                            root: $(text_input_tpl({name, label}))
                        };
                        this.parent.elem.inputs.append(input.elem.root);
                        input.elem.input = input.elem.root.fxFind('input');
                        break;
                    case 'dropdown':
                        input.elem = {
                            root: $(dropdown_input_tpl({name, label}))
                        };
                        this.parent.elem.inputs.append(input.elem.root);
                        input.elem.input = input.elem.root.fxFind('input');
                        input.input = FormInput.init(input.elem.input, {
                            placeholder: '-- Select Any That Apply --',
                            data_provider: () => {
                                let this_options = [];
                                for (let option in options) {
                                    this_options.push({
                                        id: option,
                                        text: options[option].label
                                    });
                                }
                                return this_options;
                            }
                        });
                        input.parsley = input.elem.input.parsley({
                            required: true,
                            requiredMessage: 'Required'
                        });
                        break;
                    case 'number':
                        input.elem = {
                            root: $(number_input_tpl({name, label}))
                        };
                        this.parent.elem.inputs.append(input.elem.root);
                        input.elem.input = input.elem.root.fxFind('input');
                        input.input = FormInput.init(input.elem.input, {
                            type: NumberInput.Type[NumberTypes[number_type]],
                            right_align: true,
                            validation: (value) => {
                                value = value.trim();
                                return value !== '';
                            }
                        });
                        input.parsley = input.elem.input.parsley({
                            required: true,
                            requiredMessage: 'Required'
                        });
                        break;
                }
                this.state.inputs[name] = input;
            }
            this.state.form.refresh();
        } else {
            this.parent.elem.inputs.removeClass('t-has-inputs');
        }
        this.parent.elem.inputs_wrapper.addClass('t-show');
    };

    /**
     * Fill inputs from result data
     *
     * Used when page initially loads with a report and result chosen already.
     *
     * @param {object} data
     * @returns {Promise<void>}
     */
    async fillInputs(data) {
        for (let name of Object.keys(this.state.inputs)) {
            if (data[name] === undefined) {
                continue;
            }
            let input = this.state.inputs[name];
            switch (input.type) {
                case 'date':
                case 'dropdown':
                    input.elem.input.val(data[name]).trigger('change');
                    break;
                case 'text':
                case 'number':
                    input.elem.input.val(data[name]);
                    break;
            }
        }
    };

    /**
     * Handle error
     *
     * If response is found, meaning is an API related issue, then it's handled separately.
     *
     * @param {string} type
     * @param {*} error
     * @param {object} config
     */
    handleError(type, error, config) {
        let report = true,
            message = config.message;
        if (error.response) {
            let code = error.response.statusCode();
            switch (code) {
                case 401:
                    this.router.externalRedirect(window.fx_pages.AUTH_LOGIN);
                    return;
                case 404:
                    report = false;
                    message = `Unable to find ${type}`;
                    break;
            }
        }
        if (report) {
            log.error(`Unable to load ${type}`, {error});
        }
        if (config.navigate) {
            this.router.navigate(...config.navigate);
        }
        if (message) {
            let toast = createErrorMessage(message);
            this.parent.layout.toasts.addMessage(toast);
        }
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        this.parent.showLoader();
        await super.load(request, next);
        this.loadReport(request.params.report_id).then(async (report) => {
            if (request.query.result_id !== undefined) {
                this.loadResult(report, request.query.result_id).then(result => {
                    if (this.router.current_route.is_initial) {
                        report.inputs.then(this.fillInputs(result.inputs)); // @todo handle errors
                    }
                    this.parent.hideLoader();
                }, error => {
                    this.parent.hideLoader();
                    this.handleError('report result', error, {
                        navigate: ['generator', {report_id: this.state.report.data.id}],
                        message: 'Unable to load report result, please contact support'
                    });
                });
                return;
            }
            await report.inputs;
            this.parent.hideLoader();
        }, error => {
            this.parent.hideLoader();
            this.handleError('report', error, {
                navigate: ['main'],
                message: 'Unable to load report, please contact support'
            });
        });
    };

    /**
     * Handle page refresh
     *
     * When a new report is run, result generated, and the query string updates, this method is fired.
     *
     * @param {object} request
     */
    refresh(request) {
        this.parent.showLoader();
        this.loadReport(request.params.report_id).then(report => {
            if (this.state.result !== null && request.query.result_id === undefined) {
                this.resetResult();
                this.parent.hideLoader();
            } else if (request.query.result_id !== undefined) {
                this.loadResult(report, request.query.result_id).then(() => this.parent.hideLoader(), error => {
                    this.parent.hideLoader();
                    this.handleError('report result', error, {
                        navigate: ['generator', {report_id: this.state.report.data.id}],
                        message: 'Unable to load report result, please contact support'
                    });
                });
            } else {
                this.parent.hideLoader();
            }
        }, error => {
            this.parent.hideLoader();
            this.handleError('report', error, {
                navigate: ['main'],
                message: 'Unable to load report, please contact support'
            });
        });
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.resetReport();
        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return generator_tpl();
    };
}

module.exports = Generator;
