@use "~@cac-sass/config/global" with (
    $legacy: false
);
@use "~@cac-sass/base";
@use "~@cac-sass/app/form";
@use "~@cas-layout-sass/layout";
@use "~@fancyapps/fancybox/dist/jquery.fancybox";

.a-pic-logo,
.a-p-image {
    display: none;
}

:root {
    /* brand */
    --blue‑50: #e9f1ff;
    --blue‑500: #2970ff;

    /* greys */
    --gray‑25: #fafbfd;
    --gray‑50: #f4f6fa;
    --gray‑100: #e5e9f0;
    --gray‑300: #c9d2e1;
    --gray‑500: #6b7280;
    --gray‑700: #344155;
    --gray-light-3: #BBCBDD;

    --radius‑pill: 9999px;
}

/* ---------- Reset / Base ---------- */
*,
*::before,
*::after {
    box-sizing: border-box;
}
html {
    font-family:
        "Inter",
        system-ui,
        -apple-system,
        BlinkMacSystemFont,
        "Segoe UI",
        <PERSON><PERSON>,
        "Helvetica Neue",
        Arial,
        sans-serif;
    line-height: 1.4;
    font-size: 14px;
    color: var(--gray‑700);
    scroll-behavior: smooth;
    height: 100%;
    overflow: hidden;
}
body {
    margin: 0;
    background: var(--gray‑50);
    height: 100%;
    overflow: hidden;
}
a {
    color: var(--blue‑500);
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
img {
    display: block;
    max-width: 100%;
    height: auto;
}
button {
    font-family: inherit;
    font-size: inherit;
    color: inherit;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
}

.m-customer-portal {
    background: #fff;

    border-radius: 104px var(--page-radius, 12px) var(--page-radius, 12px)
        var(--page-radius, 12px);
    margin: 24px;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    height: calc(100vh - 48px);
    max-height: calc(100vh - 48px);
    overflow: hidden;

    .t-hidden {
        display: none !important;
    }

    .section-loading {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.5) url('~@cac-public/images/loading_blue.svg') no-repeat center;
        background-size: base.unit-rem-calc(80px) base.unit-rem-calc(80px);
        z-index: 120;
        display: none;
    }

    .c-header {
        display: flex;
        align-items: flex-start;
        gap: 0;
        padding: 24px 0 0 24px;
        background: #fff;
        overflow-y: visible;

        @include base.respond-to("<=xlarge") {
            margin-bottom: base.unit-rem-calc(-2.5px);
        }

        .c-h-logo {
            width: base.unit-rem-calc(320px);
            height: base.unit-rem-calc(160px);
            padding: base.unit-rem-calc(24px);
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border-radius: 1000px;
            border: 1px solid var(--gray-light-4, #e1e8f0);
            background: #fff;
            margin-bottom: base.unit-rem-calc(-78px);
            z-index: 10;
            flex-shrink: 0;
            display: flex;
            position: relative;

            @include base.respond-to("<=xlarge") {
                width: base.unit-rem-calc(240px);
                height: base.unit-rem-calc(120px);
                padding: base.unit-rem-calc(24px);
                border-radius: 120px 60px 0 120px;
                margin-bottom: 0;
            }

            .c-hl-info-button {
                display: none;
                position: absolute;
                bottom: 8px;
                right: 8px;
                width: 18px;
                height: 18px;
                background: transparent;
                border-radius: 50%;
                align-items: center;
                justify-content: center;
                color: var(--gray‑500);
                z-index: 3;
                opacity: 0;
                transform: scale(0.8);
                transition: color 0.15s ease-in-out;
                cursor: pointer;

                @include base.respond-to("<=xlarge") {
                    display: flex;
                    animation: c-hl-info-button-appear 0.4s ease-out 0.15s forwards;
                }

                &::before {
                    content: '';
                    position: absolute;
                    top: -2px;
                    left: -2px;
                    right: -2px;
                    bottom: -2px;
                    border: 1px solid var(--blue‑500);
                    border-radius: 50%;
                    opacity: 0;
                    transform: scale(0.1);
                    pointer-events: none;

                    @include base.respond-to("<=xlarge") {
                        animation: c-hl-info-button-border-pulse 0.8s ease-out 0.15s forwards;
                    }
                }

                svg {
                    width: 18px;
                    height: 18px;
                    fill: currentColor;
                }

                @include base.respond-to("hover") {
                    &:hover {
                        color: var(--gray‑700);
                    }
                }
            }
        }

        @keyframes c-hl-info-button-appear {
            0% {
                opacity: 0;
                transform: scale(0.4);
            }
            10% {
                opacity: 1;
                transform: scale(1);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes c-hl-info-button-border-pulse {
            0% {
                opacity: 0;
                transform: scale(1);
            }
            45% {
                opacity: 0.8;
                transform: scale(1.2);
            }
            90% {
                opacity: 0.4;
                transform: scale(1);
            }
            100% {
                opacity: 0;
                transform: scale(1.2);
            }
        }

        .c-h-navigation {
            display: flex;
            flex-direction: column;
            border-bottom: 1px solid var(--gray-light-4, #e1e8f0);
            position: relative;
            justify-content: flex-end;
            overflow-x: hidden;
            overflow-y: visible;
            @include base.full-width-height;

            @include base.respond-to("<=xlarge") {
                margin-top: base.unit-rem-calc(-2.5px);
            }

            .c-hn-extra-navigation {
                display: none;
                height: base.unit-rem-calc(60px);
                padding: 16px 12px 16px 14px;
                justify-content: flex-end;
                align-items: center;
                align-self: stretch;
                overflow-y: visible;


                @include base.respond-to("<=xlarge") {
                    display: flex;
                    padding: 0 12px 0 14px;
                }

                @include base.respond-to("<=xlarge") {
                    border-bottom: 1px solid var(--gray-light-4, #E1E8F0);
                }
            }

            .c-hn-main-navigation {
                display: flex;
                width: 100%;
                gap: base.unit-rem-calc(8px);
                justify-content: flex-start;
                align-items: center;
                padding: base.unit-rem-calc(12px) base.unit-rem-calc(12px);
                overflow-x: auto;
                overflow-y: hidden;

                .c-hn-user-profile {
                    @include base.respond-to("<=xlarge") {
                        display: none;
                    }
                }

                &::before {
                    content: "";
                    position: absolute;
                    right: base.unit-rem-calc(-8px);
                    top: 0;
                    width: base.unit-rem-calc(16px);
                    height: base.unit-rem-calc(56px);
                    background: linear-gradient(
                        90deg,
                        rgba(255, 255, 255, 0) 0%,
                        #ffffff 60%
                    );
                }
            }
            &::after {
                content: "";
                position: absolute;
                left: base.unit-rem-calc(-8px);
                top: 0;
                width: base.unit-rem-calc(16px);
                height: base.unit-rem-calc(56px);
                background: linear-gradient(
                    270deg,
                    rgba(255, 255, 255, 0) 0%,
                    #ffffff 60%
                );
            }
            @include base.respond-to("<small") {
                margin: 0;
                &::before {
                    right: 0;
                }
                &::after {
                    left: 0;
                }
            }
                .c-hnm-item {
                    cursor: pointer;
                    height: base.unit-rem-calc(40px);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: base.unit-rem-calc(8px);
                    border-radius: base.unit-rem-calc(40px);
                    padding: 0 base.unit-rem-calc(12px);
                    transition:
                        background-color 0.35s linear,
                        padding 0.25s ease-in-out;
                    @include base.respond-to("hover") {
                        &:hover {
                            padding: 0 base.unit-rem-calc(24px);
                            background-color: #edf1f7;
                        }
                    }
                    &.t-active {
                        color: base.$color-primary-default;
                        background-color: base.$color-primary-light-4;
                        padding: 0 base.unit-rem-calc(24px);
                        .c-hnmi-icon,
                        .c-hnmi-title {
                            color: base.$color-primary-default;
                        }
                    }
                    &.t-disabled {
                        cursor: not-allowed;
                        color: base.$color-grey-light-4;
                        .c-hnmi-icon,
                        .c-hnmi-title {
                            cursor: not-allowed;
                            color: base.$color-grey-light-4;
                        }
                        @include base.respond-to("hover") {
                            &:hover {
                                padding: 0 base.unit-rem-calc(12px);
                                background: transparent;
                                color: base.$color-grey-light-4;
                                .c-hnmi-icon,
                                .c-hnmi-title {
                                    color: base.$color-grey-light-4;
                                }
                            }
                        }
                    }
                }
                .c-hnmi-icon {
                    flex: 0 0 auto;
                    @include base.svg-icon("default-18");
                    color: base.$color-grey-dark-1;
                }
                .c-hnmi-title {
                    flex: 0 0 auto;
                    @include base.typo-header(
                        $size: 14px,
                        $line-height: base.unit-rem-calc(20px)
                    );
                    color: base.$color-grey-dark-1;
                    white-space: nowrap;
                }
            .c-hn-user-profile {
                margin-left: auto;
                padding-right: base.unit-rem-calc(10px);

                .c-hnup-pill {
                    --pill-bg: var(--clr-primary-050, #d0d8ee);
                    --pill-fg: var(--clr-primary-600, #2563eb);
                    --pill-border: var(--clr-neutral-200, #d0d8ee);

                    display: inline-flex;
                    align-items: center;
                    height: base.unit-rem-calc(40px);

                    padding: 0 base.unit-rem-calc(18px) 0 base.unit-rem-calc(12px);
                    margin-right: base.unit-rem-calc(12px);
                    background: var(--pill-bg);
                    border-radius: 32px 0 0 32px;
                    font: 600 14px/1 var(--font-sans, "Inter", sans-serif);
                    color: var(--pill-fg);
                    cursor: pointer;
                    position: relative;
                    transition: box-shadow 0.15s;
                }
                .c-hnup-pill:focus-visible {
                    outline: 2px solid var(--pill-fg);
                }

                .c-hnup-pill:hover {
                    box-shadow: 0 0 0 2px var(--pill-bg) inset;
                }

                .c-hnup-text {
                    margin: 0 base.unit-rem-calc(24px) 0 base.unit-rem-calc(24px);
                }

                .c-hnup-icon {
                    flex: 0 0 40px;
                    width: base.unit-rem-calc(40px);
                    height: base.unit-rem-calc(40px);

                    margin-right: base.unit-rem-calc(-40px);
                    border: 1px solid var(--pill-border);
                    border-radius: 50%;
                    display: grid;
                    place-content: center;
                    box-sizing: border-box;
                    background: #fff;
                    z-index: 10;
                }
                .c-hnup-icon svg {
                    width: base.unit-rem-calc(18px);
                    height: base.unit-rem-calc(18px);
                    color: var(--pill-fg);
                }
            }
        }
    }



    .c-container-scrollable {
        display: flex;
        flex: 1;
        min-height: 0;
        overflow: hidden;
    }
    .c-cs-tab-sections {
        display: flex;
        flex: 1;
        min-height: 0;
        overflow: hidden;
    }

    .m-pages {
        flex: 1;
        display: flex;
        overflow: hidden;
        min-height: 0;
        position: relative;
    }
    .c-page {
        display: flex;
        flex: 1;
        min-height: 0;

        .c-p-column {
            flex: 1;
            padding: 0;
            display: flex;
            flex-direction: column;
            gap: 0;
            min-height: 0;
            overflow: hidden;
        }

        .c-p-column.appointments-bids-column {
            min-width: base.unit-rem-calc(290px);
        }

        .c-p-column.invoices-column {
            min-width: base.unit-rem-calc(311px);
        }
    }

    .c-card {
        background: #fff;
        border: 1px solid var(--gray‑100);
        display: flex;
        flex-direction: column;
        flex: 1;
        border-top: none;
        border-right: none;
        min-height: 0;
        overflow: hidden;
    }

    .c-card:first-child {
    }

    .c-card > *:last-child {
        flex: 1;
        min-height: 0;
        overflow-y: auto;
        overflow-x: hidden;
    }

    .c-card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        border-bottom: 1px solid var(--gray‑100);
    }
    .c-card-header h3 {
        font-size: 14px;
        font-weight: 600;
        color: var(--gray‑700);
    }

    .c-see-all {
        font-size: 12px;
        font-weight: 500;
        color: var(--gray‑700);
        padding: 4px 8px;
        border: 1px solid var(--gray‑100);
        border-radius: 4px;
        display: flex;
        align-items: center;
        gap: 4px;
    }
    .c-see-all:hover {
        background: var(--gray‑50);
    }

    .c-empty {
        padding: 32px 16px;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
        min-height: 180px;
        justify-content: center;

        .c-empty img {
            opacity: 0.55;
        }
        .c-empty .title {
            font-weight: 600;
            color: var(--gray‑700);
        }
        .c-empty .subtitle {
            font-size: 12px;
            color: var(--gray‑500);
            line-height: 1.3;
        }
    }

    .m-content-box {
        background: #fff;
        border: 1px solid var(--gray‑100);
        border-radius: 8px;
        box-shadow:
            0 1px 3px 0 rgba(0, 0, 0, 0.1),
            0 1px 2px 0 rgba(0, 0, 0, 0.06);
        padding: 16px;
        margin-bottom: 12px;

        transition:
            box-shadow 0.15s ease-in-out,
            border-color 0.15s ease-in-out;

        @include base.respond-to("hover") {
            &:hover {
                box-shadow:
                    0 4px 6px -1px rgba(0, 0, 0, 0.1),
                    0 2px 4px -1px rgba(0, 0, 0, 0.06);
                border-color: var(--gray‑300);
            }
        }

        &:last-child {
            margin-bottom: 0;
        }

        .m-content-box.invoices-card {
            min-width: base.unit-rem-calc(388px);
        }
    }

    /* ---------- Tab-specific styles ---------- */
    .m-appointments,
    .m-bids,
    .m-invoices {
        .m-content-box {
            max-width: base.unit-rem-calc(400px);
        }

    }

    .m-gallery {
        .m-date-group .c-dg-grid {
            grid-template-columns: repeat(auto-fit, minmax(base.unit-rem-calc(285px), 1fr));
        }
    }

    /* ---------- Grid layouts for customer portal cards ---------- */
    .c-ac-grid,
    .c-bc-grid,
    .c-ic-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 24px;

        .m-content-box {
            display: flex;
            flex-direction: column;
            flex: 0 0 auto;
            width: base.unit-rem-calc(320px);
            min-width: base.unit-rem-calc(320px);
            margin-bottom: 0;
        }
    }

    .c-bc-grid,
    .c-ic-grid {
        .m-content-box {
            min-width: base.unit-rem-calc(388px);
            width: base.unit-rem-calc(388px);
        }

    }



    /* Overview page - CSS Grid layout for all screen sizes */
    .m-overview {
        .c-page {
            display: grid;
            gap: 0;
            height: 100%;

            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: 1fr;
            grid-template-areas: "appointments-bids invoices uploads";

            .c-p-column {
                display: flex;
                flex-direction: column;
                overflow: hidden;

                &:nth-child(1) {
                    grid-area: appointments-bids;

                    .c-card {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        min-height: 0;
                        border-right: 1px solid var(--gray‑100);

                        &:first-child {
                            border-bottom: 1px solid var(--gray‑100);
                        }

                        .c-appointments-container,
                        .c-bids-container {
                            flex: 1;
                            overflow-y: auto;
                        }
                    }
                }

                &:nth-child(2) {
                    grid-area: invoices;

                    .c-card {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        min-height: 0;
                        border-right: 1px solid var(--gray‑100);

                        .c-invoices-container {
                            flex: 1;
                            overflow-y: auto;
                        }
                    }
                }

                &:nth-child(3) {
                    grid-area: uploads;

                    .c-card {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        min-height: 0;

                        .c-uploads-container {
                            flex: 1;
                            overflow-y: auto;
                        }
                    }
                }
            }
        }

        .c-ac-grid,
        .c-bc-grid,
        .c-ic-grid {
            flex-direction: column;

            .m-content-box {
                width: 100%;
                min-width: auto;
            }
        }
    }

    .m-appointment-card {
        .c-ac-type {
            font-size: 14px;
            font-weight: 600;
            color: var(--gray‑700);
            margin-bottom: 16px;
            line-height: 1.2;
        }

        .c-ac-details {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 10px;
        }

        .c-acd-row {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 13px;
            line-height: 1.4;

            svg {
                width: 16px;
                height: 16px;
                flex-shrink: 0;
                color: var(--gray‑500);
            }
        }

        .c-acdr-text {
            min-width: 60px;
            font-weight: 500;
            color: var(--gray‑700);
        }

        .c-acdr-value {
            font-weight: 400;
            color: var(--gray‑700);
        }

        .c-ac-actions {
            padding: 0 0 0 4px;

            .c-aca-btn {
                display: inline-flex;
                align-items: center;
                gap: 8px;
                border: 1px solid var(--gray‑100);
                background: #fff;
                color: var(--gray‑700);
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                box-shadow:
                    0 4px 8px -4px rgba(119, 141, 166, 0.2),
                    0 1px 8px -8px rgba(119, 141, 166, 0.2);
            }
            @include base.respond-to("hover") {
                .c-icfa-btn:hover {
                    background: var(--gray‑50);
                }
            }
        }
    }

    /* ---------- Bid Card Module ---------- */
    .m-bid-card {
        .c-bc-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .c-bch-title-section {
            flex: 1;
            min-width: 0;
        }

        .c-bchts-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray‑700);
            line-height: 1.3;
            margin-bottom: 4px;
        }

        .c-bchts-number {
            font-size: 13px;
            font-weight: 400;
            color: var(--gray‑500);
            line-height: 1.2;
            font-style: italic;
        }

        .c-bch-download {
            flex-shrink: 0;
            margin-left: 12px;
        }

        .c-bchd-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--gray-dark-1, #5c6f85);
            text-align: center;
            font-feature-settings:
                "liga" off,
                "clig" off;
            font-family: Roboto, sans-serif;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 142.857% */
            background: none;
            border: none;
            padding: 6px 8px;
            cursor: pointer;
            text-decoration: none;
            transition: color 0.15s ease-in-out;
            border-radius: 4px;

            svg {
                width: 14px;
                height: 14px;
                flex-shrink: 0;
            }

            @include base.respond-to("hover") {
                &:hover {
                    color: var(--gray‑700);
                    background: var(--gray‑50);
                }
            }
        }

        .c-bc-address {
            margin-bottom: 20px;
        }

        .c-bca-line {
            font-size: 14px;
            font-weight: 400;
            color: var(--gray‑700);
            line-height: 1.4;
            margin-bottom: 2px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        .c-bc-total {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            margin-bottom: 16px;
        }

        .c-bct-label {
            color: var(--gray-dark-2, #435162);
            font-feature-settings:
                "liga" off,
                "clig" off;
            font-family: Roboto, sans-serif;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            flex: 0 0 auto;
        }

        .c-bct-divider {
            height: 1px;
            background: var(--gray‑100);
            flex: 1 1 auto;
            border-radius: 1px;
        }

        .c-bct-amount {
            color: var(--gray-dark-2, #435162);
            font-feature-settings:
                "liga" off,
                "clig" off;
            font-family: Roboto, sans-serif;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            flex: 0 0 auto;
        }

        .c-bcf-action {
            display: flex;
            justify-content: flex-end;

            button {
                display: flex;
                height: 32px;
                padding: 0 16px;
                justify-content: center;
                align-items: center;
                border-radius: 100px;
                border: 1px solid rgba(0, 0, 0, 0);
                background: var(--Primary-Initial, #1551d8);
                box-shadow:
                    0 4px 8px -4px rgba(119, 141, 166, 0.3),
                    0 1px 8px -8px rgba(119, 141, 166, 0.3);
                text-transform: none;
            }
        }

        .c-bc-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .c-bcfa-btn {
            background: var(--blue‑500);
            color: #fff;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            transition: background-color 0.15s ease-in-out;

            @include base.respond-to("hover") {
                &:hover {
                    background: #1d4ed8;
                }
            }
        }

        .c-bcf-date {
            flex: 1;
            text-align: right;
            margin-left: 12px;
        }

        .c-bcfd-text {
            color: var(--gray-dark-1, #5c6f85);
            font-feature-settings:
                "liga" off,
                "clig" off;
            font-family: Roboto, sans-serif;
            font-size: 13px;
            font-style: italic;
            font-weight: 400;
            line-height: 20px; /* 153.846% */
        }
    }

    .c-appointments-container,
    .c-bids-container,
    .c-invoices-container {
        padding: 16px;
    }

    .c-bids-container {
        padding: 16px;
    }

    .m-invoice-card {
        .c-ic-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray‑700);
            line-height: 1.3;
            margin-bottom: 8px;
        }

        .c-ic-subtitle {
            font-size: 13px;
            color: var(--gray‑700);
            margin-bottom: 16px;
            .c-ics-note {
                color: var(--gray‑500);
                font-style: italic;
            }
        }

        .c-ic-rows {
            display: flex;
            flex-direction: column;
            gap: 1px;
        }
        .c-ic-row {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .c-icr-label {
            color: var(--gray-dark-2, #435162);
            font-feature-settings:
                "liga" off,
                "clig" off;
            font-family: Roboto, sans-serif;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            flex: 0 0 auto;
        }
        .c-icr-divider {
            height: 1px;
            background: var(--gray‑100);
            flex: 1 1 auto;
            border-radius: 1px;
        }
        .c-icr-amount {
            color: var(--gray-dark-2, #435162);
            font-feature-settings:
                "liga" off,
                "clig" off;
            font-family: Roboto, sans-serif;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            flex: 0 0 auto;
            min-width: base.unit-rem-calc(62px);
        }

        .c-ic-footer {
            display: flex;
            flex-direction: column;
            margin-top: 16px;

            .c-icf-action {
                order: 1;
                align-self: flex-start;
                margin-bottom: 8px;
            }
            .c-icf-date {
                order: 2;
                align-self: flex-end;
            }
        }

        .c-icfa-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            border: 1px solid var(--gray‑100);
            background: #fff;
            color: var(--gray‑700);
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 13px;
            box-shadow:
                0 4px 8px -4px rgba(119, 141, 166, 0.2),
                0 1px 8px -8px rgba(119, 141, 166, 0.2);
        }
        @include base.respond-to("hover") {
            .c-icfa-btn:hover {
                background: var(--gray‑50);
            }
        }
        .c-icfab-icon {
            width: 14px;
            height: 14px;
        }
        .c-icf-date {
            font-size: 12px;
            color: var(--gray‑500);
            font-style: italic;
        }
    }

    /* ---------- Invoices Container Component ---------- */
    .c-invoices-container {
        padding: 16px;
    }

    .c-uploads-container {
        padding: 16px;
        display: flex;
        flex-direction: column;
        gap: 24px;
    }

    .m-date-group {
        .c-dg-header {
            color: var(--gray-dark-1, #5c6f85);
            font-feature-settings:
                "liga" off,
                "clig" off;
            font-family: Roboto, sans-serif;
            font-size: 14px;
            font-style: normal;
            font-weight: 500;
            line-height: 20px;
            margin-bottom: 16px;
        }

        .c-dg-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(base.unit-rem-calc(180px), 1fr));
            gap: 12px;
        }
    }

    .m-month-group {
        margin-bottom: 32px;

        &:last-child {
            margin-bottom: 0;
        }

        .c-mg-header {
            color: var(--gray-dark-1, #5c6f85);
            font-feature-settings:
                    "liga" off,
                    "clig" off;
            font-family: Roboto, sans-serif;
            font-size: 14px;
            font-style: normal;
            font-weight: 500;
            line-height: 20px;
            margin-bottom: 16px;
        }
    }

    .m-image-card {
        position: relative;
        background: #fff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow:
            0 1px 3px 0 rgba(0, 0, 0, 0.1),
            0 1px 2px 0 rgba(0, 0, 0, 0.06);
        transition:
            box-shadow 0.15s ease-in-out,
            transform 0.15s ease-in-out;
        cursor: pointer;
        aspect-ratio: 4/3;

        &:hover {
            box-shadow:
                0 4px 6px -1px rgba(0, 0, 0, 0.1),
                0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transform: translateY(-1px);

            .c-imc-image img {
                transform: scale(1.05);
            }
        }

        .c-imc-image {
            background: var(--gray‑50);
            position: relative;
            overflow: hidden;

            img {
                object-fit: cover;
                object-position: center;
                display: block;
                transition: transform 0.15s ease-in-out;
            }

            .c-imci-placeholder {
                display: flex;
                align-items: center;
                justify-content: center;
                background: var(--gray‑50);
                color: var(--gray‑300);

                svg {
                    width: 32px;
                    height: 32px;
                }
            }
        }

        .c-imc-number {
            position: absolute;
            top: 8px;
            right: 8px;
            background: var(--blue‑500);
            color: #fff;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            line-height: 1;
            z-index: 3;
        }

        .c-imc-info-button {
            position: absolute;
            bottom: 8px;
            left: 8px;
            width: 34px;
            height: 34px;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            z-index: 3;
            transition: background-color 0.15s ease-in-out;

            svg {
                width: 18px;
                height: 18px;
                fill: currentColor;
            }

            &:hover {
                background: rgba(0, 0, 0, 0.8);

            }
        }
    }
}

@include base.respond-to('<=medium') {
    .m-customer-portal {
        .m-overview {
            .c-page {
                display: grid;
                grid-template-columns: 1fr 1fr;
                grid-template-rows: 1fr 1fr;
                grid-template-areas:
                    "appointments-bids invoices"
                    "appointments-bids uploads";
                background-color: #ffebee;

            .c-p-column {
                min-width: 0;
                display: flex;
                flex-direction: column;
                overflow: hidden;

                &:nth-child(1) {
                    // Appointments & Bids column - left (spans both rows, contains 2 cards stacked)
                    grid-area: appointments-bids !important;

                    .c-card {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        min-height: 0;
                        border-right: 1px solid var(--gray‑100);

                        &:first-child {
                            border-bottom: 1px solid var(--gray‑100);
                        }

                        .c-appointments-container,
                        .c-bids-container {
                            flex: 1;
                            overflow-y: auto;
                        }
                    }
                }

                &:nth-child(2) {
                    // Open Invoices component - top right (50% height)
                    grid-area: invoices !important;

                    .c-card {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        min-height: 0;
                        border-bottom: 1px solid var(--gray‑100);

                        .c-invoices-container {
                            flex: 1;
                            overflow-y: auto;
                        }
                    }
                }

                &:nth-child(3) {
                    // Recent Uploads component - bottom right (50% height)
                    grid-area: uploads !important;

                    .c-card {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        min-height: 0;

                        .c-uploads-container {
                            flex: 1;
                            overflow-y: auto;
                        }
                    }
                }
            }
        }
    }
    }

    .c-cards-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 12px;
        padding: 12px;
    }

    /* Keep overview page single column on medium screens */
    .m-overview {
        .c-ac-grid,
        .c-bc-grid,
        .c-ic-grid {
            flex-direction: column;

            .m-content-box {
                min-width: auto;
            }
        }
    }

    .m-customer-portal {
        .c-cards-grid {
            grid-template-columns: 1fr;
            gap: 8px;
            padding: 8px;
        }

        .m-date-group {
            .c-dg-grid {
                grid-template-columns: 1fr;
                gap: 8px;
            }
        }

        .m-month-group {
            margin-bottom: 24px;

            .c-mg-header {
                font-size: 14px;
                margin-bottom: 16px;
            }
        }

        .m-image-card {
            .c-imc-info {
                padding: 10px;

                .c-imci-name {
                    font-size: 12px;
                }

                .c-imci-meta {
                    font-size: 11px;
                }
            }
        }

        /* Mobile responsive - single column for all card types */
        .c-ac-grid,
        .c-bc-grid,
        .c-ic-grid {
            flex-direction: column;

            .m-content-box {
                min-width: auto;
            }
        }
    }

}

.s-modal .m-company-info {
    display: flex;
    width: 100%;
    margin: 0;
    padding: 0;
    background: transparent;
    border: none;
    box-shadow: none;
}

.m-company-info {
    display: flex;
    padding: 90px 12px 40px 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    align-self: stretch;
    min-height: 0;
    overflow-y: auto;

    @include base.respond-to('<=xlarge') {
        display: none;
    }
    h4 {
        color: #000;
        font-variant-numeric: slashed-zero;
        font-feature-settings:
                "liga" off,
                "clig" off;
        font-family: Barlow, sans-serif;
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 140%;
    }
    .c-ci-name {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 16px;
    }
    .c-ci-block {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 16px;
        width: 100%;
        min-width: base.unit-rem-calc(340px);
        .c-ci-text {
            flex: 1 1 auto;
            min-width: 0;
            h6 {
                color: var(--gray-dark-4, #1f252c);
                font-feature-settings:
                        "liga" off,
                        "clig" off;
                font-family: Barlow, sans-serif;
                font-size: 14px;
                font-style: normal;
                font-weight: 600;
                line-height: 140%;
            }
        }
        .c-action-icon {
            display: flex;
            padding: 10px;
            justify-content: center;
            align-items: center;
            gap: 8px;
            border-radius: var(--border-radius, 100px);
            border: 1px solid var(--Secondary-Stroke, #e1e8f0);
            background: var(--Secondary-Background, #fff);
            box-shadow:
                    0 4px 8px -4px rgba(119, 141, 166, 0.2),
                    0 1px 8px -8px rgba(119, 141, 166, 0.2);
            svg,
            img {
                width: 16px;
                height: 16px;
            }
        }
    }
    .c-ci-title {
        font-size: 12px;
        font-weight: 600;
        color: var(--gray‑500);
        margin-bottom: 6px;
    }
    .c-ci-field {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;
        position: relative;
        .c-cif-meta {
            flex: 1 1 0;
            min-width: 0;
            display: flex;
            flex-direction: column;
        }
        .c-cif-label {
            color: var(--gray-dark-4, #1f252c);
            font-feature-settings:
                    "liga" off,
                    "clig" off;
            font-family: Roboto, sans-serif;
            font-size: 13px;
            font-style: normal;
            font-weight: 500;
            line-height: 20px;
        }
        .c-cif-value {
            color: var(--gray-dark-2, #435162);
            font-feature-settings:
                    "liga" off,
                    "clig" off;
            font-family: Roboto, sans-serif;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
        }
    }
    .c-salesman {
        flex: 1;
        gap: 12px;
        align-items: flex-start;
        margin: 12px 0;
        .c-s-wrapper {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .c-sw-avatar {
            display: flex;
            justify-content: center;
            align-items: center;
            width: var(--avatar-size, 40px);
            height: var(--avatar-size, 40px);
            border-radius: 40px;
            background: lightgray 50% / cover no-repeat;
            box-shadow:
                    0 1px 3px 0 rgba(119, 141, 166, 0.08),
                    0 1px 2px 0 rgba(119, 141, 166, 0.04);
        }
        .c-sw-name {
            font-weight: 600;
            color: var(--gray-700);
        }
    }
}