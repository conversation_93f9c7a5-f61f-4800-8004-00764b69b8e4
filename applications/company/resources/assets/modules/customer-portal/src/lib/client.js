import {Request} from '@ca-package/api';


class Client {
    /**
     * Constructor
     *
     * @param {string} customerUUID
     */
    constructor(customerUUID) {
        this.state = {};
        this.state.customerUUID = customerUUID;
    }

    /**
     * Fetch data from Customer Portal API endpoints
     *
     * @returns {Promise<object>}
     * @param endpoint str
     */
    async fetchData(endpoint) {
        const customer_uuid = this.state.customerUUID;

        if (customer_uuid) {
            try {
                const url = `/api/customer-portal/${customer_uuid}/${endpoint}`
                const request = new Request()
                    .endpoint(url, true)
                    .method(Request.Method.GET);

                const response = await request.call();
                if (response && response?.success && response?.data) {
                    return response.data;
                } else {
                    console.error(`No data returned from: ${url}`);
                    return [];
                }
            } catch (error) {
                console.error(`Error fetching: ${url}`, error);
            }
        }
    }
}





export {Client};