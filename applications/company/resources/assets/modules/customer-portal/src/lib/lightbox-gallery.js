'use strict';

/**
 * Lightbox Gallery Utility for Customer Portal
 * 
 * Provides FancyBox lightbox functionality for image galleries
 * Compatible with customer portal upload data structure
 */
export class LightboxGallery {
    constructor() {
        this.gallery_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff'];
    }

    /**
     * Check if a file is an image based on content type or extension
     * 
     * @param {object} upload - Upload object with contentType property
     * @returns {boolean}
     */
    isImage(upload) {
        if (!upload) return false;
        
        // Check content type first
        if (upload.contentType) {
            return upload.contentType.startsWith('image/');
        }
        
        // Fallback to checking file extension from name
        if (upload.name) {
            const extension = upload.name.split('.').pop().toLowerCase();
            return this.gallery_extensions.includes(extension);
        }
        
        return false;
    }

    /**
     * Format uploads data for FancyBox
     * 
     * @param {Array} uploads - Array of upload objects from API
     * @returns {Array} - Array formatted for FancyBox
     */
    formatImagesForFancyBox(uploads) {
        if (!Array.isArray(uploads)) {
            return [];
        }

        return uploads
            .filter(upload => this.isImage(upload))
            .map(upload => ({
                src: upload.url,
                opts: {
                    caption: upload.name || 'Image',
                    thumb: upload.url,
                    uploadData: upload
                }
            }));
    }

    /**
     * Open lightbox gallery starting at specific image
     * 
     * @param {Array} uploads - Array of all upload objects
     * @param {string} startFileId - File ID to start gallery on (fileID property)
     * @param {object} options - Additional FancyBox options
     */
    openGallery(uploads, startFileId = null, options = {}) {
        const images = this.formatImagesForFancyBox(uploads);

        if (images.length === 0) {
            console.warn('No images found to display in gallery');
            return;
        }

        // Find the starting index if fileId is provided
        let startIndex = 0;
        if (startFileId) {
            const foundIndex = images.findIndex(image =>
                image.opts.uploadData.fileID === startFileId
            );
            if (foundIndex !== -1) {
                startIndex = foundIndex;
            } else {
                console.warn('Start file ID not found in images:', startFileId);
            }
        }

        const defaultOptions = {
            buttons: ['thumbs', 'download', 'close'],
            thumbs: {
                autoStart: true
            },
            // Add custom download functionality
            btnTpl: {
                download: '<button data-fancybox-download class="fancybox-button fancybox-button--download" title="Download">' +
                         '<svg><use xlink:href="#remix-icon--system--download-line"></use></svg>' +
                         '</button>'
            },
            // Updating the download button
            afterShow: (instance, current) => {
                this.setupDownloadHandler(current);
            },
            beforeShow: (instance, current) => {
                this.setupDownloadHandler(current);
            },
            afterClose: () => {
                this.cleanupDownloadHandler();
            }
        };

        const finalOptions = Object.assign({}, defaultOptions, options);

        $.fancybox.open(images, finalOptions, startIndex);
    }

    /**
     * Setup download handler for the current FancyBox slide
     *
     * @param {object} current - Current FancyBox slide object
     */
    setupDownloadHandler(current) {
        this.cleanupDownloadHandler()
        $(document).on('click.fancybox-download', '[data-fancybox-download]', (event) => {
            event.preventDefault();
            event.stopPropagation();

            if (current && current.opts && current.opts.uploadData && current.opts.uploadData.url) {
                const downloadUrl = `${current.opts.uploadData.url}?download=true`;
                window.open(downloadUrl, '_blank');
            } else {
                console.warn('Unable to download: No upload data found for current image', current);
            }
        });
    }

    /**
     * Cleanup download handler when lightbox is closed
     */
    cleanupDownloadHandler() {
        $(document).off('click.fancybox-download', '[data-fancybox-download]');
    }

    /**
     * Setup click handlers for image cards in a container
     * 
     * @param {jQuery} container - Container element to search for image cards
     * @param {Array} uploads - Array of upload objects
     * @param {string} cardSelector - CSS selector for image cards (default: '.m-image-card')
     * @param {string} fileIdAttribute - Data attribute containing file ID (default: 'data-file-id')
     */
    setupImageCardHandlers(container, uploads, cardSelector = '.m-image-card', fileIdAttribute = 'data-file-id') {
        if (!container || !container.length) {
            console.warn('Container not found for lightbox setup');
            return;
        }

        container.off('click.lightbox', cardSelector);
        container.on('click.lightbox', cardSelector, (event) => {
            event.preventDefault();

            const card = $(event.currentTarget);
            const fileId = card.attr(fileIdAttribute);

            if (fileId) {
                if (uploads && uploads.length > 0) {
                    this.openGallery(uploads, fileId);
                } else {
                    console.warn('No uploads available, opening image in new tab');
                    const imageUrl = `/media/project-files/original/${fileId}`;
                    window.open(imageUrl, '_blank');
                }
            } else {
                console.warn('No file ID found on clicked element');
            }
        });
    }

    /**
     * Remove lightbox event handlers from container
     * 
     * @param {jQuery} container - Container element
     * @param {string} cardSelector - CSS selector for image cards
     */
    removeImageCardHandlers(container, cardSelector = '.m-image-card') {
        if (container && container.length) {
            container.off('click.lightbox', cardSelector);
        }
    }

    /**
     * Get image count from uploads array
     * 
     * @param {Array} uploads - Array of upload objects
     * @returns {number}
     */
    getImageCount(uploads) {
        if (!Array.isArray(uploads)) {
            return 0;
        }
        return uploads.filter(upload => this.isImage(upload)).length;
    }
}

// Export singleton instance for convenience
export const lightboxGallery = new LightboxGallery();
