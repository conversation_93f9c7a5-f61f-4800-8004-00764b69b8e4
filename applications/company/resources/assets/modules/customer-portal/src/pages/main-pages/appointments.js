'use strict';

import Page from '@ca-package/router/src/page';
import appointments_tpl from '@cam-customer-portal-tpl/pages/main-pages/appointments.hbs';
import {Client} from '../../lib/client';
import { createErrorMessage } from '@cas-notification-toast-js/message/error';
import { groupByMonth } from '../../lib/helpers';

/**
 * Appointments page for customer portal
 *
 * @memberof module:CustomerPortal/Pages/MainPages
 */
export class AppointmentsPage extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);

        Object.assign(this.state, {
            parent: parent,
            customer_data: window.customer_portal_data || {},
            appointments: [],
            loading: true,
            loaded: false,
            error: null
        });

        this.client = new Client(this.state.customer_data?.customerUUID);
    }

    /**
     * Fetch data from an API endpoint
     *
     * @returns {Promise<void>}
     */
    async fetchData() {
        try {
            this.state.loading = true;
            this.state.error = null;

            const appointments = await this.client.fetchData('appointments');

            // Update state with fetched data
            Object.assign(this.state, {
                appointments
            });

            this.state.loaded = true;
            console.log('Data loaded successfully:', this.state.data);
        } catch (error) {
            console.error('Error fetching overview data:', error);
            this.state.error = error;
            const message = createErrorMessage('Unable to load overview data. Please refresh the page and try again.');
            this.router.main_route.layout.toasts.addMessage(message);
        } finally {
            this.state.loading = false;
        }
    }

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        if (this.state.parent && typeof this.state.parent.showLoader === 'function') {
            this.state.parent.showLoader();
        }

        try {
            await this.fetchData();
            this.updateContent();
        } finally {
            if (this.state.parent && typeof this.state.parent.hideLoader === 'function') {
                this.state.parent.hideLoader();
            }
        }

        await super.load(request, next);
    }

    /**
     * Update the page content after data changes
     */
    updateContent() {
        if (this.elem && this.elem.root) {
            this.elem.root.html(this.render());
        }
    }

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        // Group appointments by month for the dedicated appointments page
        const groupedAppointments = groupByMonth(this.state.appointments);

        return appointments_tpl({
            customer_data: this.state.customer_data,
            appointments: this.state.appointments,
            groupedAppointments: groupedAppointments
        });
    }
}