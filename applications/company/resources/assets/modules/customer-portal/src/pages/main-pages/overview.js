'use strict';

import Page from '@ca-package/router/src/page';
import overview_tpl from '@cam-customer-portal-tpl/pages/main-pages/overview.hbs';
import {Client} from '../../lib/client';
import {createErrorMessage} from '@cas-notification-toast-js/message/error';
import {lightboxGallery} from '../../lib/lightbox-gallery';
const {findChild, jsSelector} = require("@ca-package/dom");

/**
 * Overview page for customer portal
 *
 */
export class OverviewPage extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);

        Object.assign(this.state, {
            parent: parent,
            customer_data: window.customer_portal_data || {},
            data: {},
                loading: true,
                loaded: false,
                error: null
        });

        this.client = new Client(this.state.customer_data?.customerUUID);
    }

    async boot(root) {
        super.boot(root);
        this.setupImageCardHandlers();
        this.setupSeeAllButtons();
    }

    /**
     * Setup click handlers for image cards
     */
    setupImageCardHandlers() {
        if (this.elem && this.elem.root) {
            const uploads = this.state.data?.uploads || [];
            lightboxGallery.setupImageCardHandlers(
                this.elem.root,
                uploads,
                '.m-image-card',
                'data-file-id'
            );
        }
    }

    /**
     * Setup "See All" buttons
     */
    setupSeeAllButtons() {
        if (this.elem && this.elem.root) {
            this.elem.seeAllAppointments = findChild(this.elem.root, jsSelector('see-all-appointments'));
            this.elem.seeAllBids = findChild(this.elem.root, jsSelector('see-all-bids'));
            this.elem.seeAllInvoices = findChild(this.elem.root, jsSelector('see-all-invoices'));
            this.elem.seeAllGallery = findChild(this.elem.root, jsSelector('see-all-gallery'));

            if (this.elem.seeAllAppointments && this.elem.seeAllAppointments.length > 0) {
                $(this.elem.seeAllAppointments[0]).on('click', (event) => {
                    event.preventDefault();
                    this.router.navigate('appointments');
                });
            }

            if (this.elem.seeAllBids && this.elem.seeAllBids.length > 0) {
                $(this.elem.seeAllBids[0]).on('click', (event) => {
                    event.preventDefault();
                    this.router.navigate('bids');
                });
            }

            if (this.elem.seeAllInvoices && this.elem.seeAllInvoices.length > 0) {
                $(this.elem.seeAllInvoices[0]).on('click', (event) => {
                    event.preventDefault();
                    this.router.navigate('invoices');
                });
            }

            if (this.elem.seeAllGallery && this.elem.seeAllGallery.length > 0) {
                $(this.elem.seeAllGallery[0]).on('click', (event) => {
                    event.preventDefault();
                    this.router.navigate('gallery');
                });
            }
        }
    }

    /**
     * Open lightbox gallery starting at specific image
     * @param {string} fileId - File ID to open
     */
    // openImage(fileId) {
    //     if (this.state.data.uploads && this.state.data.uploads.length > 0) {
    //         lightboxGallery.openGallery(this.state.data.uploads, fileId);
    //     } else {
    //         const imageUrl = `/media/project-files/original/${fileId}`;
    //         window.open(imageUrl, '_blank');
    //     }
    // }

    /**
     * Fetch all data from API endpoints
     *
     * @returns {Promise<void>}
     */
    async fetchAllData() {
        try {
            this.state.loading = true;
            this.state.error = null;

            // Fetch all data concurrently for better performance
            const [appointments, bids, invoices, uploads] = await Promise.all([
                this.client.fetchData('appointments'),
                this.client.fetchData('bids'),
                this.client.fetchData('invoices'),
                this.client.fetchData('uploads')
            ]);

            // Update state with fetched data
            Object.assign(this.state.data, {
                appointments,
                bids,
                invoices,
                uploads
            });

            this.state.loaded = true;
            console.log('Data loaded successfully:', this.state.data);
        } catch (error) {
            console.error('Error fetching overview data:', error);
            this.state.error = error;

            // Show user-friendly error message
            const message = createErrorMessage('Unable to load overview data. Please refresh the page and try again.');
            this.router.main_route.layout.toasts.addMessage(message);
        } finally {
            this.state.loading = false;
        }
    }

    /**
     * Update the page content after data changes
     */
    updateContent() {
        if (this.elem && this.elem.root) {
            this.elem.root.html(this.render());
            // Re-setup event handlers after content update
            this.setupImageCardHandlers();
            this.setupSeeAllButtons();
        }
    }

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        if (this.state.parent && typeof this.state.parent.showLoader === 'function') {
            this.state.parent.showLoader();
        }

        try {
            await this.fetchAllData();
            // Update content after data is loaded
            this.updateContent();
        } finally {
            // Hide loader
            if (this.state.parent && typeof this.state.parent.hideLoader === 'function') {
                this.state.parent.hideLoader();
            }
        }

        await super.load(request, next);
    }

    /**
     * Unload page and cleanup
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        // Clean up lightbox event handlers
        if (this.elem && this.elem.root) {
            lightboxGallery.removeImageCardHandlers(this.elem.root, '.m-image-card');
        }

        await super.unload(request, next);
    }

    /**
     * Refresh page data
     *
     * @param {object} request
     */
    async refresh(request) {
        // Clear existing data
        this.state.data = {};
        this.state.loaded = false;
        this.state.error = null;

        // Fetch fresh data
        await this.fetchAllData();

        // Re-render the page
        this.updateContent();
    }

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return overview_tpl({
            customer_data: this.state.customer_data,
            appointments: this.state.data.appointments,
            bids: this.state.data.bids,
            invoices: this.state.data.invoices,
            uploads: this.state.data.uploads,
            loading: this.state.loading,
            loaded: this.state.loaded,
            error: this.state.error
        });
    }
}
