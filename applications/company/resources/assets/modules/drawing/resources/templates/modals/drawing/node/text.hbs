<form data-js="form">
    <div class="s-grid t-space-bottom">
        <div class="i-g-row">
            <div class="i-gr-column t-size-12">
                <div class="f-field">
                    <div class="f-f-input"><textarea rows="5" data-js="content"></textarea></div>
                </div>
            </div>
        </div>
        <div class="i-g-row">
            <div class="i-gr-column t-size-3">
                <div class="f-field t-inline">
                    <div class="f-f-left">
                        <label class="f-f-label">Size:</label>
                    </div>
                    <div class="f-f-right">
                        <select class="f-f-input" data-js="size">
{{#each sizes}}
                            <option value="{{this}}">{{this}}</option>
{{/each}}
                        </select>
                    </div>
                </div>
            </div>
            <div class="i-gr-column t-size-3">
                <div class="f-field t-inline">
                    <div class="f-f-left">
                        <label class="f-f-label">Color:</label>
                    </div>
                    <div class="f-f-right">
                        <input type="text" data-input-type="color" data-js="color">
                    </div>
                </div>
            </div>
            <div class="i-gr-column t-size-6">
                <div class="f-field t-inline">
                    <div class="f-f-left">
                        <label class="f-f-label">Justification:</label>
                    </div>
                    <div class="f-f-right">
                        <select class="f-f-input" data-js="justification">
{{#each justifications}}
                            <option value="{{@key}}">{{this}}</option>
{{/each}}
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>