'use strict';

const Styled = require('../styled');
const Tool = require('../../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Ellipse/Styled
 */
class PoolRound extends Styled {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: PoolRound.Entity.Type.POOL_ROUND,
            tool_type: Tool.Type.POOL_ROUND,
            handles: Object.assign(this.properties.handles, {
                scale: true,
                resize: true,
                rotate: true
            }),
            show_dimensions: true,
            label: 'Pool',
            text_color: '#fff'
        });
    };

    /**
     * Get default state for node type
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            color: 'rgba(0,112,255,0.5)',
            border_width: 0
        });
        return state;
    };
}

module.exports = PoolRound;
