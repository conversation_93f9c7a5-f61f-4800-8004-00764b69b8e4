'use strict';

const LineDimension = require('../line_dimension');
const Angle = require('../../utils/angle');
const Tool = require('../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/LineDimension
 */
class Attachment extends LineDimension {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: Attachment.Entity.Type.LINE_DIMENSION_ATTACHMENT,
            tool_type: Tool.Type.LINE_DIMENSION_ATTACHMENT,
            attachment_name: 'attachment',
            hit_test_priority: 1
        });
    };

    /**
     * Get default state for node type
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            dimension_points: null,
            distance: 25
        });
        return state;
    };

    /**
     * Clear all dimension points
     */
    clearDimensionPoints() {
        this.state.dimension_points = null;
    };

    /**
     * Build dimension points list
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line/Segment.Dimension} line
     * @returns {{point: function, offset: function}[]}
     */
    buildDimensionPoints(line) {
        let dimension_points = [],
            half_width = line.half_width,
            from = line.from,
            length = line.vector.length,
            rounded_length = Math.round(length),
            endpoints = {from: false, to: false},
            has_points = false;
        line.tracked_nodes.forEach((node) => {
            if (!node.valid) {
                return;
            }
            let points = node.getDimensionPoints(line, this);
            for (let point of points) {
                let distance = point.subtract(from).length,
                    rounded_distance = Math.round(distance);
                if (rounded_distance === 0) {
                    endpoints.from = true;
                } else if (rounded_distance === rounded_length) {
                    endpoints.to = true;
                } else if (!has_points) {
                    has_points = true;
                }
                dimension_points.push({
                    distance,
                    offset: node.getDimensionStemOffset(line, this) + this.properties.offset
                });
            }
        });
        if (!has_points) {
            dimension_points = [];
        } else {
            dimension_points.sort((a, b) => a.distance - b.distance);
            // add start and end of line after distance sorting is done (if node doesn't already exist at endpoint)
            // since we know the position for these points
            if (!endpoints.from) {
                dimension_points.unshift({distance: 0, offset: this.properties.offset + half_width});
            }
            if (!endpoints.to) {
                dimension_points.push({distance: length, offset: this.properties.offset + half_width});
            }
        }
        return dimension_points;
    };

    /**
     * Get points which can dimensioned
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line/Segment.Dimension} line
     * @returns {{point: function, distance: number, offset: number}[]}
     */
    getDimensionPoints(line) {
        if (this.state.dimension_points === null) {
            this.state.dimension_points = this.buildDimensionPoints(line);
        }
        return this.state.dimension_points;
    };

    /**
     * Calculate base minimum distance so dimension will sit above main attachment and look good
     *
     * @returns {*}
     */
    calculateBaseMinDistance() {
        return this.properties.font_size + (this.properties.text_offset * 2) + this.line.half_width;
    };

    /**
     * Get minimum distance dimension line should be based on attached nodes
     *
     * @param {boolean} [reset=false] - determines if dimension points are cleared
     * @returns {number}
     */
    getMinDistance(reset = false) {
        if (reset) {
            this.clearDimensionPoints();
        }
        let distances = [this.getBaseMinDistance()],
            // get all offsets, add another offset to it to make sure some stem is showing
            offsets = this.getDimensionPoints(this.line).map(({offset}) => offset + this.properties.offset);
        if (offsets.length > 0) {
            distances = distances.concat(offsets);
        }
        return Math.max(...distances);
    };

    /**
     * Render node again
     */
    reload() {
        this.clearDimensionPoints();
        super.reload();
    };

    /**
     * Draw node and register
     */
    draw() {
        let line = this.line;
        if (!line.valid || !line.isDimensionVisible(this.name)) {
            return;
        }
        let dimension_points = this.getDimensionPoints(line);
        if (dimension_points.length === 0) {
            return;
        }
        let vector = line.vector,
            angle = Math.round(vector.angle);

        // normalize 180 angle since it can be positive or negative
        if (angle === 180) {
            angle = -180;
        }
        // set -90 degree angles into quadrant three so they are rotated (currently they are quadrant 4 for consistency)
        let quadrant = angle === -90 ? 3 : Angle.quadrant(angle);

        let group = new this.paper.ps.Group({
            parent: this.getLayer(this.selected ? 'selected' : 'default')
        });
        this.setPaperItem('main', group, true);

        let path_config = {
                strokeColor: this.selected ? this.properties.selected_color : '#808080',
                strokeWidth: this.properties.stroke_width,
                parent: group
            },
            path = null,
            side = this.state.side,
            last_dimension_point,
            last_dimension_distance,
            rotation = 90 * side,
            vert_vector = vector.rotate(rotation),
            stem_vector = vector.rotate(-rotation),
            rotate_text = [2, 3].indexOf(quadrant) !== -1,
            text_offset = this.properties.text_offset;
        if (
            (rotate_text && side === LineDimension.Side.LEFT) ||
            ([1, 4].indexOf(quadrant) !== -1 && side === LineDimension.Side.RIGHT)
        ) {
            text_offset += this.properties.font_size * 0.75;
        }
        for (let i = 0; i < dimension_points.length; i++) {
            let {distance, offset} = dimension_points[i],
                point = null;
            if (path === null) {
                path = new this.paper.ps.Path(path_config);
                if (last_dimension_point !== undefined) {
                    path.add(last_dimension_point);
                }
            }
            // if first point, then we start with from
            if (i === 0) {
                point = line.from.add(vert_vector.normalize(this.state.distance));
                path.add(point.add(stem_vector.normalize(this.state.distance - offset)));
                path.add(point);
            } else {
                let dimension_distance = distance - last_dimension_distance;
                if (dimension_distance > 0) {
                    point = last_dimension_point.add(vector.normalize(dimension_distance));
                    path.add(point);
                    path.add(point.add(stem_vector.normalize(this.state.distance - offset)));
                    let text = new this.paper.ps.PointText({
                            content: this.paper.getUnitsFromPixels(dimension_distance),
                            fillColor: this.selected ? this.properties.selected_color : '#808080',
                            fontFamily: this.properties.font_family,
                            fontSize: this.properties.font_size,
                            justification: 'center',
                            parent: group
                        });
                    text.pivot = text.parentToLocal(text.bounds.bottomCenter);
                    text.rotation = rotate_text ? angle - 180 : angle;
                    // have to position text after rotation is applied since coordinates won't match up properly afterwards
                    text.point = last_dimension_point.add(vector.normalize(dimension_distance / 2))
                        .add(vert_vector.normalize(text_offset)).round();
                    path = null;
                }
            }
            if (point !== null) {
                last_dimension_point = point;
            }
            last_dimension_distance = distance;
        }
    };
}

module.exports = Attachment;
