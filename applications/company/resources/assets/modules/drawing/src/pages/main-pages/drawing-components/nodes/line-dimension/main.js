'use strict';

const LineDimension = require('../line_dimension');
const Angle = require('../../utils/angle');
const Tool = require('../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/LineDimension
 */
class Main extends LineDimension {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: Main.Entity.Type.LINE_DIMENSION_MAIN,
            tool_type: Tool.Type.LINE_DIMENSION_MAIN,
            attachment_name: 'main',
            hit_test_priority: 2
        });
    };

    /**
     * Get default state for node type
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            distance: 0
        });
        return state;
    };

    /**
     * Calculate base min distance for dimension line
     *
     * @returns {number}
     */
    calculateBaseMinDistance() {
        return (this.properties.offset * 2) + this.line.half_width;
    };

    /**
     * Draw node and register
     */
    draw() {
        let line = this.line;
        if (!line.valid || !line.isDimensionVisible(this.name)) {
            return;
        }
        let side = this.state.side,
            side_line = line.getSideLine(side),
            vector = side_line.to.subtract(side_line.from),
            length = vector.length,
            angle = Math.round(vector.angle);
        // normalize 180 angle since it can be positive or negative
        if (angle === 180) {
            angle = -180;
        }
        let quadrant = Angle.quadrant(angle);
        // move -90 degree angles into quadrant three so they are rotated (currently they are quadrant 4 for consistency)
        if (angle === -90) {
            quadrant = 3;
        }
        let path = new this.paper.ps.Path({
                strokeColor: this.selected ? this.properties.selected_color : '#808080',
                strokeWidth: this.properties.stroke_width,
                insert: false
            }),
            distance = this.state.distance,
            rotate = 90 * side;
        if (distance > this.properties.offset) {
            distance -= this.properties.offset;
        }
        if (distance > 0) {
            path.add(side_line.from.add(vector.normalize(this.properties.offset).rotate(rotate)));
            path.lineBy(vector.normalize(distance).rotate(rotate));
            path.lineBy(vector);
            path.lineBy(vector.normalize(distance).rotate(-rotate));
        }
        let rotate_text = [2, 3].indexOf(quadrant) !== -1,
            text_offset = this.properties.text_offset;
        if (
            (rotate_text && side === LineDimension.Side.LEFT) ||
            ([1, 4].indexOf(quadrant) !== -1 && side === LineDimension.Side.RIGHT)
        ) {
            text_offset += this.properties.font_size * 0.75;
        }
        let midpoint_vector = vector.divide(2).add(vector.normalize(this.state.distance + text_offset).rotate(rotate)),
            text = new this.paper.ps.PointText({
                content: this.paper.getUnitsFromPixels(length),
                fillColor: this.selected ? this.properties.selected_color : '#808080',
                fontFamily: this.properties.font_family,
                fontSize: this.properties.font_size,
                justification: 'center',
                insert: false
            });
        text.pivot = text.parentToLocal(text.bounds.bottomCenter);
        text.rotation = rotate_text ? angle - 180 : angle;
        // have to position text after rotation is applied since coordinates won't match up properly afterwards
        text.point = side_line.from.add(midpoint_vector).round();
        let item = new this.paper.ps.Group({
            children: [path, text],
            parent: this.getLayer(this.selected ? 'selected' : 'default')
        });
        this.setPaperItem('main', item, true);
    };
}

module.exports = Main;
