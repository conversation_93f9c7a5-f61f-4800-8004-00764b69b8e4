'use strict';

const Styled = require('../styled');
const Tool = require('../../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Rectangle/Styled
 */
class Mudjacking extends Styled {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: Mudjacking.Entity.Type.MUDJACKING,
            tool_type: Tool.Type.MUDJACKING,
            handles: Object.assign(this.properties.handles, {
                scale: true,
                resize: true,
                rotate: true
            }),
            show_dimensions: true,
            // label: 'Mudjacking'
        });
    };

    /**
     * Get default state for node type
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            color: 'rgba(255, 0, 0, 0.5)'
        });
        return state;
    };
}

module.exports = Mudjacking;
