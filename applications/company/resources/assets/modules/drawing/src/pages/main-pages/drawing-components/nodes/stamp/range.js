'use strict';

const Paper = require('../../paper');
const Stamp = require('../stamp');
const Tool = require('../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Stamp
 */
class Range extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: Range.Entity.Type.RANGE,
            tool_type: Tool.Type.RANGE,
            layer: Paper.Layer.BACKGROUND_STAMP,
            handles: Object.assign(this.properties.handles, {
                rotate: true
            }),
            sizes: {
                template: this.paper.getSizeFromUnits({inches: 30}, {inches: 28}),
                min: paper.getSizeFromUnits({feet: 1})
            }
        });
    };

    /**
     * Get default state for node
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            size: this.paper.getSizeFromUnits({inches: 30}, {inches: 28})
        });
        return state;
    };

    /**
     * Create paper.js path template to clone from
     *
     * @returns {*}
     */
    getPathTemplate() {
        let y_pos = this.paper.getPixelsFromUnit({inches: 2});
        let group = new this.paper.ps.Group({
            position: this.state.point,
            parent: this.getLayer(this.selected ? 'selected' : 'default'),
            visible: this.valid,
            applyMatrix: false
        });
        new this.paper.ps.Path.Rectangle({
            point: [0, 0],
            size: this.properties.sizes.template,
            fillColor: '#fff',
            strokeWidth: 1,
            strokeColor: this.selected ? this.properties.selected_color : '#000',
            parent: group
        });
        new this.paper.ps.Path.Line({
            from: [0, y_pos],
            to: [this.properties.sizes.template.width, y_pos],
            strokeWidth: 1,
            strokeColor: '#000',
            parent: group
        });
        new this.paper.ps.Path.Circle({
            center: [18, 20],
            radius: 8,
            strokeWidth: 1,
            strokeColor: this.selected ? this.properties.selected_color : '#000',
            parent: group
        });
        new this.paper.ps.Path.Circle({
            center: [18, 40],
            radius: 8,
            strokeWidth: 1,
            strokeColor: this.selected ? this.properties.selected_color : '#000',
            parent: group
        });
        new this.paper.ps.Path.Circle({
            center: [40, 20],
            radius: 8,
            strokeWidth: 1,
            strokeColor: this.selected ? this.properties.selected_color : '#000',
            parent: group
        });
        new this.paper.ps.Path.Circle({
            center: [40, 40],
            radius: 8,
            strokeWidth: 1,
            strokeColor: this.selected ? this.properties.selected_color : '#000',
            parent: group
        });

        return new this.paper.ps.Group({
            children: [group]
        });
    };
}

module.exports = Range;
