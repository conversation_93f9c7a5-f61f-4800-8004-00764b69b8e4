'use strict';

const Styled = require('../styled');
const Node = require('../../../nodes/base');
const ConfigPanel = require('../../../config-panels/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Ellipse/Styled
 */
class PoolRound extends Styled {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, PoolRound.Type.POOL_ROUND);
        Object.assign(this.state, {
            label: 'Pool (Round)',
            icon: 'module--drawing--tools--pool-round',
            node_type: Node.Entity.Type.POOL_ROUND,
            config_panel_type: ConfigPanel.Type.RECTANGLE_INFO_ONLY
        });
    };
}

module.exports = PoolRound;
