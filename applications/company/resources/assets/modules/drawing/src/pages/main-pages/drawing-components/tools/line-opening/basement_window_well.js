'use strict';

const LineOpening = require('../line_opening');
const Node = require('../../nodes/base');
const ConfigPanel = require('../../config-panels/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/LineOpening
 */
class BasementWindowWell extends LineOpening {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, BasementWindowWell.Type.BASEMENT_WINDOW_WELL);
        Object.assign(this.state, {
            label: 'Basement Wdw',
            icon: 'module--drawing--tools--basement-well',
            node_type: Node.Entity.Type.BASEMENT_WINDOW_WELL,
            config_panel_type: ConfigPanel.Type.DEFAULT,
            side_snap: false
        });
    };
}

module.exports = BasementWindowWell;
