'use strict';

const Entry = require('../entry');
const Node = require('../../../nodes/base');
const ConfigPanel = require('../../../config-panels/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/LineOpening/Entry
 */
class SingleCantilever extends Entry {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, SingleCantilever.Type.SINGLE_CANTILEVER);
        Object.assign(this.state, {
            label: 'Sin. Cantilever',
            icon: 'module--drawing--tools--single-cantilever',
            node_type: Node.Entity.Type.SINGLE_CANTILEVER,
            config_panel_type: ConfigPanel.Type.LINE_OPENING_FULL
        });
    };
}

module.exports = SingleCantilever;
