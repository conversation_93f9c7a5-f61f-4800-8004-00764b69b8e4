'use strict';

const Pier = require('../pier');
const Node = require('../../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/LineStamp/Pier
 */
class Interior extends Pier {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Interior.Type.INTERIOR_PIER);
        Object.assign(this.state, {
            label: 'Interior Pier',
            icon: 'module--drawing--tools--interior-pier',
            node_type: Node.Entity.Type.INTERIOR_PIER
        });
    };
}

module.exports = Interior;
