'use strict';

const Dimension = require('../dimension');
const Node = require('../../../../nodes/base');
const ConfigPanel = require('../../../../config-panels/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Line/Segment/Dimension
 */
class ExistingFence extends Dimension {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, ExistingFence.Type.EXISTING_FENCE);
        Object.assign(this.state, {
            label: 'Existing Fence',
            icon: 'module--drawing--tools--existing-fence',
            node_type: Node.Entity.Type.EXISTING_FENCE,
            config_panel_type: ConfigPanel.Type.LINE_DIMENSION_ONLY
        });
    };
}

module.exports = ExistingFence;
