'use strict';

const Stamp = require('../stamp');
const Node = require('../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Stamp
 */
class Vegetation extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Vegetation.Type.VEGETATION);
        Object.assign(this.state, {
            label: 'Vegetation',
            icon: 'module--drawing--tools--vegetation',
            node_type: Node.Entity.Type.VEGETATION
        });
    };
}

module.exports = Vegetation;
