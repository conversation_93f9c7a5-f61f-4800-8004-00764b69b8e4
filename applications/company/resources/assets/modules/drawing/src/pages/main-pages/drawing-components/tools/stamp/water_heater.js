'use strict';

const Stamp = require('../stamp');
const Node = require('../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Stamp
 */
class WaterHeater extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, WaterHeater.Type.WATER_HEATER);
        Object.assign(this.state, {
            label: 'Water Heater',
            icon: 'module--drawing--tools--water-heater',
            node_type: Node.Entity.Type.WATER_HEATER
        });
    };
}

module.exports = WaterHeater;
