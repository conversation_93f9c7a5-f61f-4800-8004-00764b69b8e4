/**
 * @module Drawing/Repositories/Drawing
 */

'use strict';

const moment = require('moment-timezone');
const isArray = require('lodash/isArray');

const getIso8601DateTime = require('@cac-js/utils/iso8601_datetime');

const db = require('../db');
const network = require('../network');
const log = require('../log');

const Api = require('../api');
const Error = require('../error');
const DrawingEntity = require('../entities/drawing');
const ImageEntity = require('../entities/drawing/image');
const ImageRepo = require('./drawing/image');
const NodeRepo = require('./drawing/node');
const UserRepo = require('./user');
const Repository = require('./base');
const PubSub = require('../pubsub');

/**
 * @memberof module:Drawing/Repositories
 */
class Drawing extends Repository {
    /**
     * Search drawings on server
     *
     * @param {string} term
     * @returns {Promise<module:Drawing/Entities.Drawing[]>}
     */
    async search(term) {
        try {
            let {data} = await Api.Resources.Drawings().version(2)
                .accept('application/vnd.adg.fx.drawing-v2-search+json')
                .search(term)
                .all();
            return data.entities.map(drawing => new DrawingEntity(Object.assign(drawing.data, {
                is_published: true,
                sync_status: DrawingEntity.SyncStatus.SYNCED
            }), true));
        } catch (e) {
            throw this.getError(e);
        }
    };

    /**
     * Fetch all drawings
     *
     * @param {string[]} [relations=[]] - list of relationships to load
     * @param {boolean} [with_deleted=false] - determines if deleted records are included
     * @returns {Promise<module:Drawing/Entities.Drawing[]>}
     */
    async all(relations = [], with_deleted = false) {
        let drawings;
        if (!with_deleted) {
            drawings = await db.current_user.drawings.where('is_deleted').equals(0).toArray();
        } else {
            drawings = await db.current_user.drawings.toArray();
        }
        return DrawingEntity.bulkLoad(drawings, relations);
    };

    /**
     * Find drawing by id
     *
     * @param {string} id - drawing uuid
     * @param {string[]} [relations=[]] - list of relationships to load
     * @returns {Promise<module:Drawing/Entities.Drawing|null>}
     */
    async findById(id, relations = []) {
        let data = await db.current_user.drawings.get(id);
        if (data === undefined) {
            return null;
        }
        data = new DrawingEntity(data, true);
        await data.load(relations);
        return data;
    };

    /**
     * Find drawings by list of ids
     *
     * @param {string[]} ids - list of drawing uuids
     * @param {string[]} [relations=[]] - list of relationships to load
     * @returns {Promise<module:Drawing/Entities.Drawing[]>}
     */
    async findByIds(ids, relations = []) {
        let drawings = await db.current_user.drawings.where(':id').anyOf(ids).toArray();
        return await DrawingEntity.bulkLoad(drawings, relations);
    };

    /**
     * Fetch drawing from server by id
     *
     * @param {string} id - drawing uuid
     * @param {string[]} [relations=[]] - list of relationships to load
     * @returns {Promise<module:Drawing/Entities.Drawing|null>}
     */
    async fetchById(id, relations = []) {
        if (!network.online) {
            throw new Error.Network.Offline;
        }
        let request = Api.Resources.Drawings().version(2)
            .accept('application/vnd.adg.fx.drawing-v2+json');
        if (relations.length > 0) {
            for (let relation of relations) {
                request.relation(relation);
            }
        }
        try {
            let {data: drawing} = await request.retrieve(id);
            let server_entity = new DrawingEntity(Object.assign(drawing.data, {
                is_published: true,
                sync_status: DrawingEntity.SyncStatus.SYNCED
            }), true);
            server_entity.created_by_user = await UserRepo.retrieve(server_entity.created_by_user_id);
            // @todo handle image relations
            return server_entity;
        } catch (e) {
            if (e instanceof Error.Api.EntityNotFound) {
                return null;
            }
            throw this.getError(e);
        }
    };

    /**
     * Fetch all drawing which have changes since the passed date
     *
     * @param {string} last_synced_at - UTC datetime of last sync
     * @returns {Promise<{last_synced_at: string, drawings: module:Drawing/Entities.Drawing[]}>}
     */
    async fetchChanged(last_synced_at) {
        let {data: {drawings, last_synced_at: new_synced_at}} = await Api.Resources.Drawings().version(2)
            .method(Api.Request.Method.PUT)
            .custom('sync', {
                last_synced_at: last_synced_at,
                additional_ids: []
            });
        return {
            drawings: drawings.map(drawing => new DrawingEntity(Object.assign(drawing, {
                is_published: true,
                sync_status: DrawingEntity.SyncStatus.SYNCED
            }), true)),
            last_synced_at: new_synced_at
        };
    };

    /**
     * Retrieve drawing from local stores or remote server if necessary
     *
     * @param {string} id - drawing uuid
     * @param {boolean} [store_new=true] - determines if we store entity returned from server which doesn't exist locally
     * @returns {Promise<module:Drawing/Entities.Drawing>}
     */
    async retrieve(id, store_new = true) {
        let entity = await this.findById(id, ['nodes']);
        // if entity has never been synced, we just return it
        if (entity !== null) {
            if (entity.is_deleted) {
                throw new Error.Api.EntityNotFound;
            }
            if (!entity.is_published) {
                return entity;
            }
        }
        // if we are online and the local drawing is currently synced, we see if it needs to be updated
        // we leave any drawings in pending or failed sync state alone so no data is lost
        if (network.online && (entity === null || entity.sync_status === DrawingEntity.SyncStatus.SYNCED)) {
            log.info(`Fetching drawing ${id} from server`);
            let server_entity = await this.fetchById(id, ['nodes']);
            if (entity === null && server_entity === null) {
                throw new Error.Api.EntityNotFound;
            }
            // if storing on server entity is disabled for entities which don't exist locally, we just return early
            if (!store_new && entity === null) {
                return server_entity;
            }
            // if server version has been deleted and we have a local copy
            if (server_entity.is_deleted && entity !== null) {
                log.info(`Drawing ${id} deleted on server, removing local copy`);
                await this.forceDelete(id);
                throw new Error.Api.EntityNotFound;
            }
            // if server version has been modified after our local one, we overwrite our local one with the servers version
            if (entity === null || server_entity.getLastModifiedAt(true).isAfter(entity.getLastModifiedAt(true), 'second')) {
                log.info(`Updating local drawing ${id} with server version due to modified timestamp difference`);
                await this.store(server_entity);
                entity = server_entity;
            }
        } else if (!network.online && entity === null) {
            throw new Error.Api.EntityNotFound;
        }
        return entity;
    };

    /**
     * Fetch drawing ids by specified field name and value
     *
     * @param {string} field - name of entity field to filter by
     * @param {*} value - value to filter by
     * @returns {Promise<module:Drawing/Entities.Drawing[]>}
     */
    async getIdsByField(field, value) {
        let operator = isArray(value) ? 'anyOf' : 'equals';
        return await db.current_user.drawings.where(field)[operator](value).primaryKeys();
    };

    /**
     * Store entity
     *
     * @param {module:Drawing/Entities.Drawing} entity
     * @param {boolean} [notify=true]
     * @returns {Promise<string>}
     */
    async store(entity, notify = true) {
        if (!(entity instanceof DrawingEntity)) {
            throw new Error('entity must be an instance of Drawing');
        }
        let user_db = db.current_user;
        let id = await user_db.transaction('rw', user_db.drawings, user_db.drawing_nodes, user_db.drawing_images, async () => {
            if (entity.image !== null) {
                await ImageRepo.store(entity.image);
            }
            let nodes = entity.nodes;
            if (nodes !== null) {
                await NodeRepo.sync(entity.id, nodes);
            }
            return await user_db.drawings.put(entity.getPayload());
        });
        if (notify) {
            PubSub.Handler.publish(PubSub.Topics.Drawing.STORE, {
                entity: entity
            });
        }
        return id;
    };

    /**
     * Partially update entity by id
     *
     * @param {string} id - drawing uuid
     * @param {object} data - changes
     * @param {boolean} [notify=true]
     * @returns {Promise<number>}
     */
    async partialUpdate(id, data, notify = true) {
        let changes = await db.current_user.drawings.update(id, data);
        if (notify && changes === 1) {
            PubSub.Handler.publish(PubSub.Topics.Drawing.UPDATE, {id, data});
        }
        return changes;
    };

    /**
     * Push drawing to server
     *
     * @param {module:Drawing/Entities.Drawing} drawing
     * @returns {Promise<void>}
     */
    async push(drawing) {
        let id = drawing.id;
        log.info(`Pushing drawing ${id} to server`);
        if (drawing.sync_status === DrawingEntity.SyncStatus.SYNCING) {
            if (drawing.getSyncStartedAt().isAfter(moment().subtract(3, 'minutes'), 'second')) {
                log.info(`Drawing ${id} already being synced, skipping`);
                return;
            }
            log.notice(`Drawing ${id} stuck in syncing status, allowing another sync attempt`);
        } else if (drawing.sync_status !== DrawingEntity.SyncStatus.PENDING) {
            log.notice(`Drawing ${id} not proper status for sync`);
            return;
        }
        try {
            // update drawing to syncing status and record time we started
            await this.partialUpdate(id, {
                sync_status: DrawingEntity.SyncStatus.SYNCING,
                sync_started_at: getIso8601DateTime(new Date)
            });
            // if drawing has never been synced and was already marked deleted, we just force delete immediately since
            // it isn't on the server. may move this responsibility to delete modal in the future.
            if (!drawing.is_published && drawing.is_deleted) {
                try {
                    log.info(`Force deleting drawing ${id} since it was never synced to server and is marked deleted`);
                    await this.forceDelete(id, false);
                } catch (error) {
                    log.error('Unable to force delete drawing', {id, drawing});
                    throw error;
                }
            }

            // set status to syncing so subsequent pushes don't touch it until this is done incase it takes awhile
            let request = Api.Resources.Drawings().version(2);
            if (drawing.is_deleted) {
                log.info(`Deleting drawing ${drawing.id} from server`);
                request = request.delete(drawing.id, {
                    last_modified_at: drawing.last_modified_at
                });
            } else {
                log.info(`Update drawing ${drawing.id} on server`);
                let image_entity;
                try {
                    const SvgExport = require('../pages/main-pages/drawing-components/svg_export');
                    let image_data = await SvgExport.run(drawing);
                    image_entity = new ImageEntity({id, data: image_data, content_type: 'image/svg+xml'});
                    await ImageRepo.store(image_entity);
                } catch (e) {
                    throw new Error.General('Unable to generate image, drawing may be corrupt').setPrevious(e);
                }
                request = request.accept('application/vnd.adg.fx.drawing-v2+json')
                    .file(image_entity.getData(true), 'image')
                    .update(drawing.id, drawing.getSyncPayload(['nodes']));
            }
            let {data} = await request;
            if (drawing.is_deleted) {
                await this.forceDelete(id);
            } else {
                await this.partialUpdate(id, {
                    last_modified_at: data.get('last_modified_at'),
                    is_published: 1,
                    sync_status: DrawingEntity.SyncStatus.SYNCED,
                    sync_started_at: null
                });
            }
        } catch (error) {
            // standardize error
            error = this.getError(error);
            try {
                await this.partialUpdate(id, {
                    sync_status: DrawingEntity.SyncStatus.FAILED,
                    sync_started_at: null
                });
            } catch (e) {
                log.error('Unable to update drawing sync status to failed', {error: e});
            }
            throw error;
        }
    };

    /**
     * Push drawing to server by id
     *
     * Fetches drawing from local storage and pushes it to server
     *
     * @param {string} id
     * @returns {Promise<void>}
     */
    async pushById(id) {
        if (!network.online) {
            throw new Error.Network.Offline;
        }
        // @todo make this smarter on when we grab nodes and images (not always needed)
        let drawing = await this.findById(id, ['nodes']);
        await this.push(drawing);
    };

    /**
     * Soft delete drawing by id
     *
     * @param {string} id - drawing uuid
     * @param {boolean} [notify=true]
     * @returns {Promise<void>}
     */
    async delete(id, notify = true) {
        await this.partialUpdate(id, {
            is_deleted: true,
            sync_status: DrawingEntity.SyncStatus.PENDING
        }, false);
        if (notify) {
            PubSub.Handler.publish(PubSub.Topics.Drawing.DELETE, {
                id: id
            });
        }
    };

    /**
     * Force delete drawing by id
     *
     * @param {string} id - drawing uuid
     * @param {boolean} [notify=true]
     * @returns {Promise<void>}
     */
    async forceDelete(id, notify = true) {
        let user_db = db.current_user;
        await user_db.transaction('rw', user_db.drawings, user_db.drawing_nodes, async () => {
            await Promise.all([
                NodeRepo.deleteByDrawingId(id),
                user_db.drawings.delete(id)
            ]);
        });
        if (notify) {
            PubSub.Handler.publish(PubSub.Topics.Drawing.DELETE, {
                id: id
            });
        }
    };
}

module.exports = new Drawing;
