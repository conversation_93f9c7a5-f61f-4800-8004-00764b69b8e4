'use strict';

const db = require('../db');

const Repository = require('./base');

/**
 * @memberof module:Drawing/Repositories
 */
class Setting extends Repository {
    constructor() {
        super();
        Object.assign(this.state, {
            cache: null,
            name_map: {}
        });
    };

    /**
     * Get and cache all settings
     *
     * @returns {Promise<void>}
     */
    async all() {
        if (this.state.cache !== null) {
            return;
        }
        let cache = new Map,
            name_map = {};
        let settings = await db.main.settings.toArray();
        for (let setting of settings) {
            cache.set(setting.id, setting);
            name_map[setting.name] = setting.id;
        }
        this.state.cache = cache;
        this.state.name_map = name_map;
    };

    /**
     * Get setting by name
     *
     * @param {string} name
     * @param {*} [_default=null]
     * @returns {Promise<*>}
     */
    async get(name, _default = null) {
        if (this.state.cache === null) {
            await this.all();
        }
        if (this.state.name_map[name] === undefined) {
            return _default;
        }
        return this.state.cache.get(this.state.name_map[name]).value;
    };

    /**
     * Set setting to value
     *
     * @param {string} name
     * @param {*} value
     * @returns {Promise<void>}
     */
    async set(name, value) {
        if (this.state.cache === null) {
            await this.all();
        }
        let entity = {
            name, value
        };
        let exists = this.state.name_map[name] !== undefined;
        if (exists) {
            entity.id = this.state.name_map[name];
        }
        let id = await db.main.settings.put(entity);
        // if setting doesn't exist, add it to cache
        if (!exists) {
            entity.id = id;
            this.state.name_map[name] = id;
        }
        this.state.cache.set(id, entity);
    };
}

module.exports = new Setting;
