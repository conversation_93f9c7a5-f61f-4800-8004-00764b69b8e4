'use strict';

const db = require('../db');
const network = require('../network');
const log = require('../log');

const Api = require('../api');
const Error = require('../error');
const SettingRepo = require('./setting');
const UserEntity = require('../entities/user');
const Repository = require('./base');

/**
 * @memberof module:Drawing/Repositories
 */
class User extends Repository {
    /**
     * Fetch from server by id
     *
     * @param {(number|string)} id
     * @returns {Promise<module:Drawing/Entities.User|void>}
     */
    async fetchById(id) {
        try {
            let {data: user} = await Api.Resources.Users().accept('application/vnd.adg.fx.drawing-v2+json').retrieve(id);
            return new UserEntity(user.data);
        } catch (e) {
            throw this.getError(e);
        }
    };

    /**
     * Find by id
     *
     * @param {number} id
     * @returns {Promise<module:Drawing/Entities.User|null>}
     */
    async findById(id) {
        let data = await db.main.users.get(id);
        if (data === undefined) {
            return null;
        }
        return new UserEntity(data, true);
    };

    /**
     * Find users by ids
     *
     * @param {number[]} ids
     * @returns {module:Drawing/Entities.User[]}
     */
    async findByIds(ids) {
        let users = await db.main.users.where(':id').anyOf(ids).toArray();
        for (let idx in users) {
            users[idx] = new UserEntity(users[idx], true);
        }
        return users;
    };

    /**
     * Get current user
     *
     * @returns {Promise<module:Drawing/Entities.User>}
     */
    async getCurrent() {
        let user;
        // @todo maybe pull from db anyway and only update if data has changed for user
        if (network.online) {
            try {
                user = await this.fetchById('current');
                await this.store(user);
            } catch (error) {
                if (!(error instanceof Error.Network.Offline)) {
                    log.error('Unable to fetch or store user', {user, error});
                }
            }
        }
        if (user === undefined) {
            let user_id = await SettingRepo.get('current_user_id');
            if (user_id === null) {
                throw new Error.DB.RecordNotFound;
            }
            user = await this.findById(user_id);
        }
        return user;
    };

    /**
     * Get all user ids
     *
     * @returns {Dexie.Promise<number[]>}
     */
    async getAllIds() {
        return await db.main.users.primaryKeys();
    };

    /**
     * Retrieve by id
     *
     * Fetches user from internal database first. If not found and user is online, we fetch from server and store
     * locally.
     *
     * @param {number} id
     * @returns {Promise<module:Drawing/Entities.User>}
     */
    async retrieve(id) {
        let user = await this.findById(id);
        if (user === null) {
            if (!network.online) {
                throw new Error.DB.RecordNotFound;
            }
            user = await this.fetchById(id);
            await this.store(user);
        }
        return user;
    };

    /**
     * Store user entity
     *
     * @param {module:Drawing/Entities.User} user
     * @returns {Dexie.Promise<Key | void>}
     */
    async store(user) {
        return await db.main.users.put(user.getPayload());
    };
}

module.exports = new User;
