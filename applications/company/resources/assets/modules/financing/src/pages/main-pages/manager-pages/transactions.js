'use strict';

import Page from '@ca-package/router/src/page';

import {Base as Table} from '@ca-submodule/table';

import TransactionsTable from "./transactions-components/table";

import transactions_tpl from '@cam-financing-tpl/pages/main-pages/manager-pages/transactions.hbs';

export class TransactionPage extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            parent: parent,
            layout: parent.state.layout,
            table_scope: {}
        });
    };

    /**
     * Create the Transactions DataTable and apply settings and defaults
     */
    initTable() {
        this.state.table = new TransactionsTable(this.elem.table, this.state);
        this.state.table.create();
        this.setListeners();
    };

    /**
     * Set page listeners
     */
    setListeners() {
        // Filter period change listener
        this.state.parent.on('transactions_period_filter_change', (data) => {
            this.state.table.updateScope({
                filters: {
                    created_at: [Table.Operators.BETWEEN, [data.start, data.end]]
                }
            }, true);

            this.state.table.refresh()
        })
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        await super.load(request, next);
        if (!this.state.table) {
            this.initTable();
        }

        let filter_period = this.state.parent.state.filter_period;
        if (!!this.state.parent.state.filter_period) {
            this.state.table.updateScope({
                filters: {
                    created_at: [Table.Operators.BETWEEN, [filter_period.start, filter_period.end]]
                }
            }, true);
            this.state.table.refresh()
        }
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.table = root;
    };

    /**
     * Render page
     */
    render() {
        return transactions_tpl();
    };
}
