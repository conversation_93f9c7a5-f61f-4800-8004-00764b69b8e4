'use strict';

const EventEmitter = require('events');
import moment from 'moment-timezone';

import Page from '@ca-package/router/src/page';

const Tooltip = require('@ca-submodule/tooltip');
const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/date_range'));

const Number = require('@cac-js/utils/number');
import { getWisetackMetricsReport} from "../../../../common/javascript/wisetack";

import { Manager } from './main-pages/manager';

import main_tpl from '@cam-financing-tpl/pages/main.hbs';
import { report_period_options, report_period_types } from './constants';

export class MainPage extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        let [start_date, end_date] = report_period_options[report_period_types.LAST_30_DAYS].range();

        Object.assign(this.state, {
            parent: parent,
            events: new EventEmitter,
            period_filter: {
                start_date,
                end_date
            }
        });
    };

    /**
     * Get child routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            manager: {
                default: true,
                page: Manager
            }
        };
    };

    /**
     * On events listener
     *
     * @param event
     * @param closure
     * @returns {*}
     */
    on(event, closure) {
        return this.state.events.on(event, closure);
    };

    /**
     * Set layout
     *
     * @param {module:Layout.Controller} instance
     */
    set layout(instance) {
        this.state.layout = instance;
    };

    /**
     * Get layout
     *
     * @readonly
     *
     * @returns {module:Layout.Controller}
     */
    get layout() {
        return this.state.layout;
    };

    /**
     * Get container for child pages to be rendered
     *
     * @returns {jQuery}
     */
    getPageContainer() {
        return this.elem.pages;
    };

    /**
     * Set background gradient
     *
     * @param elem
     * @param percentage
     */
    setBackground(elem, percentage) {
        elem.removeClass('red-gradient orange-gradient green-gradient');
        elem.css('width', `${percentage}%`);

        if (percentage <= 25) {
            elem.addClass('red-gradient');
        } else if (percentage < 75) {
            elem.addClass('orange-gradient');
        } else {
            elem.addClass('green-gradient');
        }
    }

    /**
     * Refresh metrics data
     *
     * @returns {Promise<void>}
     */
    async refreshMetrics(start_date, end_date) {
        let {start, end} = this.getParsedTimestamp(start_date, end_date),
            metrics = await getWisetackMetricsReport(start, end);

        if (!metrics) {
            metrics = {
                total_approved_amount: 0,
                total_pending_amount: 0,
                total_paid_amount: 0,
                average_transaction_size: 0,
                pre_approved_transactions_percentage: 0,
                approved_transactions_percentage: 0,
                converted_transactions_percentage: 0
            };
        }

        this.elem.approved_amount.text(Number.toCurrency(metrics.total_approved_amount));
        this.elem.pending_amount.text(Number.toCurrency(metrics.total_pending_amount));
        this.elem.paid_amount.text(Number.toCurrency(metrics.total_paid_amount));
        this.elem.average_loan_size.text(Number.toCurrency(metrics.average_transaction_size));

        let pre_approved = `${metrics.pre_approved_transactions_percentage || 0}%`,
            approved = `${metrics.approved_transactions_percentage || 0}%`,
            converted = `${metrics.converted_transactions_percentage || 0}%`;

        this.elem.percentage_pre_approved.text(pre_approved);
        this.elem.percentage_approved.text(approved);
        this.elem.percentage_converted.text(converted);

        this.setBackground(this.elem.pre_approved_graph_bar, metrics.pre_approved_transactions_percentage);
        this.setBackground(this.elem.approved_graph_bar, metrics.approved_transactions_percentage);
        this.setBackground(this.elem.converted_graph_bar, metrics.converted_transactions_percentage);
    };

    /**
     * Load sidebar metrics and event listeners
     * @returns {Promise<void>}
     */
    async loadSidebar() {
        let { start_date, end_date } = this.state.period_filter;
        await this.refreshMetrics(start_date, end_date);

        let updatePeriodFilter = (start, end) => {
            this.state.period_filter = { start, end };
            this.refreshMetrics(start, end);
            let period_change = this.getParsedTimestamp(start, end);
            this.state.events.emit('period_filter_change', period_change);
        };

        // load flatpickr
        this.state.period = FormInput.init(this.elem.period, {
            pickr_config: {
                mode: "range",
                dateFormat: "m/d/Y",
                defaultDate: [this.state.period_filter.start_date, this.state.period_filter.end_date],
                altFormat: 'm/d/Y',
                onChange: (selected_dates) => {
                    if (selected_dates.length === 2) {
                        let start = moment(selected_dates[0]).format('MM/DD/YYYY'),
                            end = moment(selected_dates[1]).format('MM/DD/YYYY');
                        updatePeriodFilter(start, end);
                        this.elem.report_period.val(report_period_types.CUSTOM).trigger('change');
                    }
                }
            }
        });

        // load sidebar event listeners
        this.elem.report_period.on('change', (event) => {
            let val = $(event.target).find("option:selected").val();
            if (val === report_period_types.CUSTOM) return;
            if (!report_period_options[parseInt(val)].range()) return;

            let selection = parseInt(val),
                [start, end] = report_period_options[selection].range();

            this.state.period.state.pickr.setDate([start, end]);
            updatePeriodFilter(start, end);
        });

        this.elem.sidebar_control.on('click', () => {
            let sidebarVisible = this.elem.sidebar.hasClass('t-visible');
            this.elem.sidebar.toggleClass('collapsed', sidebarVisible).toggleClass('t-visible', !sidebarVisible);
            this.elem.pages.toggleClass('expanded', !sidebarVisible);
        });
    };

    getParsedTimestamp(start, end) {
        let timezone = this.state.layout.user.timezone;
        return {
            start: moment.tz(start, 'MM/DD/YYYY', timezone).startOf('day').utc().format('YYYY-MM-DDTHH:mm:ss[Z]'),
            end: moment.tz(end, 'MM/DD/YYYY', timezone).add(1, 'day').endOf('day').utc().format('YYYY-MM-DDTHH:mm:ss[Z]')
        }
    };
    
    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        Tooltip.initAll(this.elem.root, {
            max_width: 200
        });
        this.elem.pages = root.fxFind('pages');

        this.elem.table = this.elem.root.fxFind('table-container');
        this.elem.sidebar = this.elem.root.fxFind('sidebar');
        this.elem.sidebar_control = this.elem.root.fxFind('sidebar-control');

        this.elem.approved_amount = this.elem.root.fxFind('approved-amount');
        this.elem.pending_amount = this.elem.root.fxFind('pending-amount');
        this.elem.paid_amount = this.elem.root.fxFind('paid-amount');
        this.elem.average_loan_size = this.elem.root.fxFind('average-loan-size');
        this.elem.percentage_pre_approved = this.elem.root.fxFind('percentage-pre-approved');
        this.elem.percentage_approved = this.elem.root.fxFind('percentage-approved');
        this.elem.percentage_converted = this.elem.root.fxFind('percentage-converted');
        this.elem.report_period = this.elem.root.fxFind('report-period');
        this.elem.period = this.elem.root.fxFind('period');
        this.elem.converted_graph_bar = this.elem.root.fxFind('converted-graph-bar');
        this.elem.pre_approved_graph_bar = this.elem.root.fxFind('pre-approved-graph-bar');
        this.elem.approved_graph_bar = this.elem.root.fxFind('approved-graph-bar');

        this.loadSidebar();
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return main_tpl({
            report_period_options
        });
    };
}