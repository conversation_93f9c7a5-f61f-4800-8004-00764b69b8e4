'use strict';

const Api = require('../../api');
const Page = require('@ca-package/router/src/page');

const EventKeyCodes = require('@cac-js/data/event_key_codes');
const debounce = require('@cac-js/utils/debounce');
require('@cac-js/handlebars-helpers/nl2br');

const forms_tpl = require('@cam-marketplace-tpl/pages/main-pages/forms.hbs');
const category_tpl = require('@cam-marketplace-tpl/pages/main-pages/forms-components/category.hbs');
const category_children_tpl = require('@cam-marketplace-tpl/pages/main-pages/forms-components/category_children.hbs');
const form_tpl = require('@cam-marketplace-tpl/pages/main-pages/forms-components/form.hbs');

/**
 * @memberof module:Marketplace/Pages/MainPages
 */
class Forms extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            scope: null,
            categories_promise: null,
            categories: {},
            active_category: null,
            form_height: 120,
            min_open_form_height: 175,
            forms: {},
            open_forms: [],
            search_term: null
        });
    };

    /**
     * Add category
     *
     * @param {{id: string, name: string, highlight: boolean, categories: []}} category
     * @param {string|null} [parent_id=null]
     * @returns {boolean}
     */
    addCategory(category, parent_id = null) {
        let parent_elem = parent_id === null ? this.elem.categories : this.state.categories[parent_id].elem.children,
            info = {
                data: category,
                elem: {}
            },
            tpl_vars = {
                id: category.id,
                name: category.name,
                highlight: category.highlight || false
            },
            has_children = category.categories.length > 0,
            root = has_children ? category_children_tpl(tpl_vars) : category_tpl(tpl_vars);
        info.elem.root = $(root);
        info.elem.link = has_children ? info.elem.root.fxFind('link') : info.elem.root;
        this.state.categories[category.id] = info;
        parent_elem.append(info.elem.root);
        if (has_children) {
            info.open = false;
            info.elem.children = info.elem.root.fxFind('children');
            for (let nested_category of category.categories) {
                this.addCategory(nested_category, category.id);
            }
        }
        return has_children;
    };

    /**
     * Toggle category menu to show/hide children (if available)
     *
     * @param {string} id
     * @param {boolean|null} [open=null]
     */
    toggleCategory(id, open = null) {
        let category = this.state.categories[id];
        if (open === null) {
            open = !category.open;
        } else if (category.open === open) {
            return;
        }
        category.elem.root.toggleClass('t-open', open);
        category.elem.children[open ? 'slideDown' : 'slideUp']('fast', () => {
            this.sizeOpenForms().catch(e => console.log(e));
        });
        if (open && category.data.parent_id !== null) {
            this.toggleCategory(category.data.parent_id, open);
        }
        category.open = open;
    };

    /**
     * Deselect category by id
     *
     * @param {string} id
     */
    deselectCategory(id) {
        let category = this.state.categories[id];
        category.elem.link.removeClass('t-active');
    };

    /**
     * Select specific category
     *
     * If nested, all parents will be opened.
     *
     * @param {string} id
     */
    selectCategory(id) {
        let category = this.state.categories[id];
        if (category === undefined) {
            return false;
        }
        if (this.state.active_category !== null) {
            this.deselectCategory(this.state.active_category);
        }
        category.elem.link.addClass('t-active');
        if (category.data.parent_id !== null) {
            this.toggleCategory(category.data.parent_id, true);
        }
        this.state.active_category = id;
        return true;
    };

    /**
     * Fetch categories from server into sidebar
     *
     * @returns {Promise<void>}
     */
    async fetchCategories() {
        let {data: {entities: categories}} = await Api.Resources.SystemFormCategories()
            .accept('application/vnd.adg.fx.list-v1+json')
            .all();
        this.addCategory({
            id: 'all',
            parent_id: null,
            name: 'All Forms',
            highlight: true,
            categories: []
        });
        let has_children = false;
        for (let {data: category} of categories) {
            // if category doesn't have children, we continue
            if (!this.addCategory(category)) {
                continue;
            }
            if (!has_children) {
                has_children = true;
            }
        }
        if (!has_children) {
            this.elem.categories.addClass('t-no-children');
        }
    };

    /**
     * Load categories from server
     *
     * @returns {Promise<void>}
     */
    loadCategories() {
        if (this.state.categories_promise === null) {
            this.state.categories_promise = this.fetchCategories();
        }
        return this.state.categories_promise;
    };

    /**
     * Create image instance from url and return promise
     *
     * @param {string} url
     * @returns {Promise<jQuery>}
     */
    createImage(url) {
        return new Promise((resolve, reject) => {
            let image = new Image;
            image.src = `${url}?rand=${Math.random() * 99999999}`;
            image.onload = () => resolve($(image))
            image.onerror = () => reject()
        });
    };

    /**
     * Add form
     *
     * @param {{id: string, display_name: string, description: null|string, image_media_urls: {original: string}}} form
     */
    addForm(form) {
        let info = {
            data: form,
            elem: {
                root: $(form_tpl({
                    id: form.id,
                    name: form.display_name,
                    description: form.description
                }))
            },
            open: false
        };
        info.elem.toggle_link = info.elem.root.fxFind('toggle-link');
        info.elem.image_wrapper = info.elem.root.fxFind('image');
        info.elem.content = info.elem.root.fxFind('content');
        info.elem.content_inner = info.elem.content.fxFind('content-inner');
        info.image_promise = this.createImage(form.image_media_urls.original);
        info.image_promise.then(image => {
            info.elem.image_wrapper.append(image).addClass('t-loaded');
            info.elem.image = image;
        });
        this.state.forms[form.id] = info;
        this.elem.forms.append(info.elem.root);
    };

    /**
     * Toggle form to show or hide contents
     *
     * @param {string} id
     * @param {boolean|null} [open=null]
     */
    toggleForm(id, open = null) {
        let form = this.state.forms[id];
        if (open === null) {
            open = form.open;
        } else if (form.open === open) {
            return;
        } else {
            open = !open;
        }
        if (open) {
            form.open = false;
            form.elem.root.removeClass('t-open').animate({
                height: this.state.form_height
            }, 500, () => {
                this.state.open_forms = this.state.open_forms.filter(open_id => open_id !== id);
                form.elem.content.removeClass('t-at-top t-at-bottom');
                form.elem.content_inner.off('scroll.ca');
                form.elem.toggle_link.text('Show More');
            });
            return;
        }
        form.image_promise.then(image => {
            form.elem.root.addClass('t-open');
            form.elem.content.toggleClass('t-at-top', form.elem.content_inner.scrollTop() === 0)
            form.elem.root.animate({
                height: Math.max(this.state.min_open_form_height, image.height())
            }, 500, () => {
                form.open = true;
                this.state.open_forms.push(id);
                form.elem.toggle_link.text('Show Less');
                let content_height = Math.round(form.elem.content_inner.outerHeight());
                form.elem.content_inner.on('scroll.ca', () => {
                    let scroll_top = form.elem.content_inner.scrollTop();
                    form.elem.content
                        .toggleClass('t-at-top', scroll_top === 0)
                        .toggleClass('t-at-bottom', form.elem.content_inner[0].scrollHeight - scroll_top === content_height);
                }).trigger('scroll.ca');
            });
        });
    };

    /**
     * Size form according to current image height
     *
     * This is done since the image sizes itself based on the browser width.
     *
     * @param form
     * @returns {Promise<void>}
     */
    async sizeForm(form) {
        let image = await form.image_promise;
        form.elem.root.height(Math.max(this.state.min_open_form_height, image.height()));
        form.elem.content_inner.trigger('scroll.ca');
    };

    /**
     * Resize all open forms
     *
     * @returns {Promise<void>}
     */
    async sizeOpenForms() {
        if (this.state.open_forms.length === 0) {
            return;
        }
        let promises = [];
        for (let form_id of this.state.open_forms) {
            promises.push(this.sizeForm(this.state.forms[form_id]));
        }
        await Promise.all(promises);
    };

    /**
     * Load forms for a specific category or show all
     *
     * @param {object} scope
     * @param {number} scope.page
     * @param {string|undefined} scope.category_id
     * @param {string|undefined} scope.search
     * @returns {Promise<void>}
     */
    async loadForms(scope) {
        let request = Api.Resources.SystemFormItems().accept('application/vnd.adg.fx.collection-v1+json');
        if (scope.search !== undefined) {
            request.search(scope.search);
        } else if (scope.category_id !== undefined) {
            request.filter('category_list', scope.category_id);
        }
        let {data: {entities: forms}, response} = await request.page(scope.page).perPage(10).all();
        let has_forms = forms.length > 0;
        this.elem.no_forms.toggle(!has_forms);
        this.elem.forms.toggle(has_forms);
        if (has_forms) {
            for (let {data: form} of forms) {
                this.addForm(form);
            }
        }
        this.elem.load_more_wrapper.toggle(response.meta('pagination.next_page') !== null);
    };

    /**
     * Load forms for next page
     */
    loadMoreForms() {
        this.elem.load_more.addClass('t-loader');
        this.state.scope.page += 1;
        this.loadForms(this.state.scope).then(() => {
            this.elem.load_more.removeClass('t-loader');
        });
    };

    /**
     * Pull scope info from router request
     *
     * @param {object} request
     * @returns {object}
     */
    getScopeFromRequest(request) {
        let scope = {page: 1};
        if (request.query.search !== undefined) {
            let search = request.query.search.trim();
            if (search.length > 0) {
                scope.search = search;
            }
        }
        if (scope.search === undefined && request.query.category_id !== undefined) {
            scope.category_id = request.query.category_id;
        }
        return scope;
    };

    /**
     * Handle current request scope
     *
     * @param {object} scope
     * @returns {Promise<void>}
     */
    async handleScope(scope) {
        this.state.scope = scope;
        if (scope.search !== undefined) {
            this.elem.header.text(`Search Results for "${scope.search}"`);
            this.state.search_term = scope.search;
            this.elem.search_action.toggleClass('t-clear', true);
            this.elem.search.val(scope.search);
            if (this.state.active_category !== null) {
                this.deselectCategory(this.state.active_category);
                this.state.active_category = null;
            }
        } else {
            if (this.state.search_term !== null) {
                this.elem.search_action.removeClass('t-clear');
                this.elem.search.val('');
                this.state.search_term = null;
            }
            let category = scope.category_id || 'all';
            this.loadCategories().then(() => {
                // if category doesn't exist, we redirect
                if (!this.selectCategory(category)) {
                    this.router.navigate('forms');
                    return;
                }
                this.elem.header.text(this.state.categories[category].data.name);
            });
        }
        this.elem.forms.empty();
        this.elem.no_forms.hide();
        this.elem.load_more_wrapper.hide();
        this.state.open_forms = [];
        this.elem.forms.removeClass('t-loaded');
        await this.loadForms(scope);
        this.elem.forms.addClass('t-loaded');
    };

    /**
     * Get current search term
     *
     * @returns {string}
     */
    getSearchTerm() {
        return this.elem.search.val().trim();
    };

    /**
     * Search forms for specific term
     *
     * @param {string} term
     */
    searchForms(term) {
        if (term === '') {
            return;
        }
        this.router.navigate('forms', {}, {search: term});
    };

    /**
     * Clear search and reset to all forms state
     */
    clearFormSearch() {
        this.router.navigate('forms');
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await super.load(request, next);
        let scope = this.getScopeFromRequest(request);
        this.loadCategories().catch(e => console.log(e));
        this.handleScope(scope).catch(e => console.log(e));
    };

    /**
     * Handle page refresh
     *
     * When new categories are chosen and the query string updates, this method is fired.
     *
     * @param request
     */
    refresh(request) {
        let scope = this.getScopeFromRequest(request);
        this.handleScope(scope).catch(e => console.log(e));
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);

        this.elem.search = root.fxFind('search');
        this.elem.search_action = root.fxFind('search-action');
        this.elem.categories = root.fxFind('categories');
        this.elem.header = root.fxFind('header');
        this.elem.no_forms = root.fxFind('no-forms');
        this.elem.forms = root.fxFind('forms');
        this.elem.load_more_wrapper = root.fxFind('load-more-wrapper');
        this.elem.load_more = root.fxFind('load-more');

        const that = this;
        this.elem.search.fxEvent('keydown', (e) => {
            if (e.keyCode !== EventKeyCodes.ENTER) {
                return;
            }
            this.searchForms(this.getSearchTerm());
        });
        this.elem.search_action.fxClick(() => {
            if (this.state.search_term !== null) {
                this.clearFormSearch();
                return;
            }
            this.searchForms(this.getSearchTerm());
        }, true);
        this.elem.categories.fxClickWatcher('arrow', function (e) {
            e.stopPropagation();
            that.toggleCategory($(this).parent().fxFindFirst('link').data('id'));
        }, true);
        this.elem.categories.fxClickWatcher('link', function () {
            let category_id = $(this).data('id');
            that.router.navigate('forms', null, category_id !== 'all' ? {category_id} : null);
        }, true);
        this.elem.forms.fxClickWatcher('image-overlay', function () {
            that.toggleForm($(this).fxClosest('form').data('id'));
        }, true);
        this.elem.forms.fxClickWatcher('toggle-link', function () {
            that.toggleForm($(this).fxClosest('form').data('id'));
        }, true);
        this.elem.forms.fxClickWatcher('add', function () {
            that.router.externalRedirect(window.fx_pages.CP_FORM_CREATE, null, {
                system_form_id: $(this).fxClosest('form').data('id')
            });
        });
        this.elem.load_more.fxClick(() => this.loadMoreForms(), true);

        $(window).fxEvent('resize', debounce(() => {
            this.sizeOpenForms().catch(e => console.log(e));
        }, 100));
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return forms_tpl({
            link: window.fx_pages.CP_FORMS
        });
    };
}

module.exports = Forms;
