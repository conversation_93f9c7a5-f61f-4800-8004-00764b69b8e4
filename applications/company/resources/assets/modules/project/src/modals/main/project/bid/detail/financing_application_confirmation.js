'use strict';

const Modal = require('@ca-submodule/modal').Base;

const content_tpl = require('@cam-project-tpl/modals/main/project/bid/detail/financing_application_confirmation.hbs');
const { createTransaction } = require("@cac-js/wisetack.js");

const FormValidator = require('@ca-submodule/validator');
const FormInput = require('@ca-submodule/form-input');
const NumberInput = require('@ca-submodule/form-input/src/number');
require('@ca-submodule/validator/src/validators/number');
FormInput.use(NumberInput);

class Financing_application_confirmation extends Modal {
    constructor() {
        super(content_tpl, Object.assign({
            size: Modal.Size.SMALL,
            closable: true,
            wrapper: true,
            classes: ['t-financing-application-confirmation']
        }, {}));

        this.setTitle('Send Financing Application');
        this.boot();
    };


    /**
     * Boot page
     */
    boot() {
        this.elem.form = this.elem.content.fxFind('form');
        this.elem.amount_input = this.elem.content.fxFind('amount');
        this.elem.amount_error = this.elem.content.fxFind('amount-error');

        this.state.validator = FormValidator.create(this.elem.form, {
            amount: {
                pattern: '^\\d+(\\.\\d{1,2})?$',
                patternMessage: 'Invalid format. Must have two decimal places.',
                required: true,
                requiredMessage: 'Requested Loan Amount is required.',
                between: '500,25000',
                betweenMessage: 'The value must be between $500 and $25,000.'
            },
        })
        .on('submit', () => this.save());

        this.initializeAmountInput();
        this.initializeActions();
    };

    /**
     * Initialize amount input
     */
    initializeAmountInput() {
        this.state.amount = FormInput.init(this.state.validator.getInputElem('amount'), {
            type: NumberInput.Type.CURRENCY,
            allow_minus: false
        });
    }

    /**
     * Initialize actions
     *
     */
    initializeActions() {
        this.on('close', () => {
            if (this.state.promise !== null && !this.state.external_close) {
                this.state.promise.resolve(null);
            }
            this.state.validator.reset();

        });

        this.addAction({
            type: Modal.Action.CANCEL,
            handler: () => this.cancel()
        });
        this.addAction({
            type: Modal.Action.SAVE,
            label: 'Send',
            handler: () => this.elem.form.submit()
        });
    }


    /**
     * Open modal
     *
     * @param {object} $0
     * @param {string} $0.bid_id
     * @param {object} $0.promise
     * @returns {Modal}
     */
    open({bid_id, bid_total, promise}) {
        this.elem.amount_input.val(bid_total);
        this.state.bid_id = bid_id;
        this.state.promise = promise;
        return super.open();
    };

    /**
     * Handle 'yes' response
     */
    save() {
        if (!this.state.validator.state.instance.validate()) {
            return;
        }

        this.state.amount = this.state.validator.getInputElem('amount').val();
        this.startWorking();
        const data = {
            'appSource': 'bid-details',
            'amount': this.state.amount
        }

        createTransaction(this.state.bid_id, data).then((data) => {
            this.resetWorking();
            this.state.promise.resolve(true);
            this.close();
        }).catch((e) => {
            this.resetWorking();
            this.state.promise.reject(e);
            this.close();
        });

    };

    /**
     * Handle 'no' response
     */
    cancel() {
        this.resetWorking();
        this.state.promise.resolve(null);
        this.close();
    };

}

module.exports = Financing_application_confirmation;
