'use strict';

const Page = require('@ca-package/router/src/page');
const Number = require('@cac-js/utils/number');
const Table = require('@ca-submodule/table').Base;

const line_items_tpl = require('@cam-project-tpl/pages/main-pages/detail-pages/bids-pages/detail-pages/line_items.hbs');
const line_item_table_tpl = require('@cam-project-tpl/pages/main-pages/detail-pages/bids-pages/detail-pages/line-items-components/table.hbs');
const wisetack_promo_tpl = require('@cam-project-tpl/pages/main-pages/detail-pages/bids-pages/detail-pages/line-items-components/wisetack_promo.hbs');
const Tooltip = require('@ca-submodule/tooltip');
const {onClick} = require("@ca-package/dom");
const Api = require("@ca-package/api");
const {createSuccessMessage} = require("@cas-notification-toast-js/message/success");
const {createErrorMessage} = require("@cas-notification-toast-js/message/error");
const { getWisetackPromoMessage, getWisetackTransaction, STATUS_DETAILS } = require("@cac-js/wisetack.js");

const LineItemTypes = {
    GENERAL: 1,
    PRODUCT: 2,
    DISCOUNT: 3,
    FEE: 4
};
const AdjustmentTypes = {
    TOTAL: 1,
    PERCENTAGE: 2
};
const AdjustmentModes = {
    PLUS: 1,
    MINUS: 2
};
const LineItemGroupConfig = {
    section: {},
    discount: {name: 'Discounts'},
    fee: {name: 'Fees'}
};
const LineItemTypeConfig = new Map([
    [LineItemTypes.GENERAL, {group: 'section'}],
    [LineItemTypes.PRODUCT, {group: 'section'}],
    [LineItemTypes.DISCOUNT, {group: 'discount'}],
    [LineItemTypes.FEE, {group: 'fee'}]
]);

const InactiveTransactionStatuses = ['DECLINED', 'EXPIRED', 'CANCELED', 'REFUNDED'];

class LineItems extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            wisetack_data: project_data.wisetack.merchant,
            wisetack_promo: null,
            wisetack_bid_transaction: null,
            is_transaction_inactive: null,
            is_wisetack_enabled: project_data.features.wisetack_api,
            is_financing_enabled_for_project: false,
            is_financing_required_for_all_projects: project_data.company.is_financing_required_for_all_projects,
            groups: null,
            router,
        });
    };

    /**
     * Check if bid is accepted
     * @async
     * @returns {Promise<boolean>}
     */
    async isBidFinalizedOrAccepted() {
        const bid_id = this.state.router.state.current.request.params.bid_id;
        let {data: entity} = await Api.Resources.BidItems().accept('application/vnd.adg.fx.detail-v1+json').retrieve(bid_id);
        this.state.is_financing_enabled_for_project = entity.project.is_financing_enabled;

        return entity.status === Api.Constants.BidItems.Status.FINALIZED || entity.status === Api.Constants.BidItems.Status.ACCEPTED;
    };

    /**
     * Send financing application
     * @async
     * @returns {Promise<*|null>}
     */
    async sendFinancingApplication() {
        try {
            const bid_total = this.state.bid_total;
            const response = await this.parent.openFinancingApplicationConfirmationModal(bid_total);
            if (response) {
                this.router.main_route.layout.toasts.addMessage(createSuccessMessage('Financing application has been sent to customer!'));
                this.elem.wisetack_promo_section.addClass('t-hidden');
                await this.refreshWisetackInfo();
            }
        } catch (e) {
            let message = createErrorMessage('Something went wrong. Please contact support.');
            this.router.main_route.layout.toasts.addMessage(message);
            console.error(e);
            this.elem.wisetack_promo_section.addClass('t-hidden');
            await this.refreshWisetackInfo();
            return null;
        }
    };

    /**
     * Set wisetack state
     *
     */
    setWisetackState() {
        this.elem.wisetack_loading_section.addClass('t-hidden');
        const { wisetack_bid_transaction, wisetack_promo, is_transaction_inactive } = this.state;

        const show_promo_section = wisetack_promo && (!wisetack_bid_transaction || (wisetack_bid_transaction && is_transaction_inactive));
        this.elem.wisetack_promo_section.toggleClass('t-hidden', !show_promo_section);

        if (wisetack_bid_transaction) {
            this.elem.wisetack_bid_section.removeClass('t-hidden');
            const { status, createdAt, approvedLoanAmount, requestedLoanAmount, paymentLink, actionsRequired } = wisetack_bid_transaction;

            const { class: color, label } = STATUS_DETAILS[status] || { class: 't-yellow', label: 'Pending' };

            if (status !== 'PENDING') {
                this.elem.status_callout.removeClass('t-hidden');
                const status_html = `<span class="h-text ${color}">${label}</span>`;
                this.elem.wisetack_financing_application_status.html(status_html);
            }

            const created_at_text = `Created at ${moment(createdAt).format('MM/DD/YYYY HH:mm')}`;
            this.elem.wisetack_financing_application_created_at.text(created_at_text);

            if (requestedLoanAmount) {
                const requested_amount = '$' + parseFloat(requestedLoanAmount).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                this.elem.wisetack_financing_application_requested_amount.text(`Requested Amount: ${requested_amount}`);
            }

            if (approvedLoanAmount) {
                this.elem.wisetack_financing_application_requested_amount.text('');
                const amount = '$' + parseFloat(approvedLoanAmount).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                this.elem.wisetack_financing_application_approved_amount.text(`Approved Amount: ${amount}`);
            }

            if (paymentLink && !is_transaction_inactive) {
                this.elem.wisetack_payment_link.val(wisetack_bid_transaction.paymentLink);
                this.elem.wisetack_bid_link_section.removeClass('t-hidden');
            }

            if (status === 'ACTIONS_REQUIRED' && actionsRequired.length > 0) {
                this.elem.required_actions_section.removeClass('t-hidden');
                const actions = actionsRequired.map(action => {
                    if (action === Api.Constants.Wisetack.Transactions.ActionsRequired.ADD_DEBIT_CARD) {
                        action = 'Add debit card';
                    }
                    else if (action === Api.Constants.Wisetack.Transactions.ActionsRequired.ADD_DRIVERS_LICENSE) {
                        action = 'Add drivers license';
                    }
                    return `<li>${action}</li>`;
                });

                this.elem.required_actions.append(`${actions.join('')}`);
            }
        }

        if (wisetack_promo && (is_transaction_inactive || !wisetack_bid_transaction)) {
            const {monthlyPayment: monthly_payment} = wisetack_promo;
            const content = `<label> Or as low as <strong>$${monthly_payment}/mo</strong>`;
            this.elem.wisetack_promo_content.html(content);
        }

    };

    /**
     * Create line item table with specified data
     *
     * @param {jQuery} elem - container for table
     * @param {object[]} data
     * @param {string} group
     * @returns {module:Table.Base}
     */
    createLineItemTable(elem, data, group) {
        let table = new Table(elem, {
            class: 't-static',
            paging_enabled: false
        });
        table.setToolbar({
            filter: false,
            settings: false
        });

        if (group === 'section') {
            table.setButtons({
                export_csv: {
                    label: 'Export',
                    type_class: 't-tertiary-icon',
                    icon: 'system--download-line',
                    action: (e) => {
                        let button = $(e.target);
                        button.prop('disabled', true);
                        setTimeout(() => button.prop('disabled', false), 4000);
                        let url = window.fx_url.API + 'export/bid-line-items?bid_item_id=' + this.state.router.state.current.request.params.bid_id;
                        window.location.href = url;
                    }
                }
            });

            table.setColumns({
                name: {
                    label: 'Name'
                },
                quantity: {
                    label: 'Quantity',
                    value: data => parseFloat(data.quantity).toFixed(2)
                },
                original_amount: {
                    label: 'Orig. Price',
                    value: data => Number.toCurrency(data.original_amount)
                },
                amount: {
                    label: 'Unit Price',
                    value: data => Number.toCurrency(data.amount)
                },
                subtotal: {
                    label: 'Subtotal',
                    value: (data) => {
                        if (data.original_amount !== data.amount) {
                            return Number.toCurrency(data.amount * data.quantity);
                        }
                        return Number.toCurrency(data.original_amount * data.quantity);
                    }
                },
                adjustment: {
                    label: 'Adjustment',
                    value: (data) => {
                        if (data.item === undefined) {
                            return;
                        }
                        let value = data.item.adjustment;
                        if (value === null) {
                            return;
                        }
                        if (data.item.adjustment_type === AdjustmentTypes.PERCENTAGE) {
                            value = (data.original_amount * data.quantity) * (data.item.adjustment / 100);
                        }
                        value = data.item.adjustment_mode === AdjustmentModes.PLUS ? value : `-${value}`;
                        return Number.toCurrency(value);
                    }
                },
                total: {
                    label: 'Total',
                    value: data => Number.toCurrency(data.total)
                }
            });
        } else {
            table.setColumns({
                name: {
                    label: 'Name'
                },
                quantity: {
                    label: 'Quantity',
                    value: data => parseFloat(data.quantity).toFixed(2)
                },
                amount: {
                    label: 'Amount',
                    value: data => Number.toCurrency(data.amount)
                },
                total: {
                    label: 'Total',
                    value: data => Number.toCurrency(data.total)
                }
            });
        }
        table.setData(data);
        table.build();

        return table;
    };

    /**
     * Determines if the Wisetack section should be shown based on various flags and data.
     *
     * - is_wisetack_enabled: Flag indicating if Wisetack is enabled.
     * - wisetack_data: Data returned from the Wisetack API indicating application approval.
     * - is_financing_required_for_all_projects: Company setting that requires financing for all projects.
     * - is_financing_enabled_for_project: Flag indicating if financing is enabled for the specific project.
     *
     * @returns {boolean} True if the Wisetack section should be shown, otherwise false.
     */
    shouldShowWisetackSection() {
        const {
            is_wisetack_enabled,
            wisetack_data,
            is_financing_required_for_all_projects,
            is_financing_enabled_for_project,
        } = this.state;

        if (!is_wisetack_enabled || !wisetack_data || wisetack_data.status !== 'APPLICATION_APPROVED') {
            return false;
        }

        if (is_financing_required_for_all_projects) {
            return true;
        }

        return !!is_financing_enabled_for_project;
    }

    /**
     * Refresh Wisetack info
     *
     * @returns {Promise<void>}
     */
    async refreshWisetackInfo() {
        const is_bid_finalized_or_accepted = await this.isBidFinalizedOrAccepted();
        const is_bid_amount_valid = this.state.bid_total > 500;

        if (this.shouldShowWisetackSection() && is_bid_finalized_or_accepted && is_bid_amount_valid) {
            this.state.wisetack_bid_transaction = null;
            this.elem.wisetack_loading_section.removeClass('t-hidden');
            const bid_id = this.state.router.state.current.request.params.bid_id;
            this.state.wisetack_bid_transaction = await getWisetackTransaction(bid_id);

            this.state.is_transaction_inactive = this.state.wisetack_bid_transaction &&
                InactiveTransactionStatuses.includes(this.state.wisetack_bid_transaction.status);

            if (!this.state.wisetack_bid_transaction || this.state.is_transaction_inactive) {
                const merchant_id = this.state.wisetack_data.wisetackMerchantID;
                const amount = this.state.bid_total;
                this.state.wisetack_promo = await getWisetackPromoMessage(merchant_id, amount);
            }

            this.setWisetackState();
        }
    };

    async openDeleteLoanModal() {
        try {
            const wisetack_transaction_id =  this.state.wisetack_bid_transaction.wisetackTransactionID;
            const response = await this.parent.openDeleteLoanModal(wisetack_transaction_id);
            if (response) {
                this.router.main_route.layout.toasts.addMessage(createSuccessMessage('Financing application has been deleted!'));
                this.elem.wisetack_promo_section.addClass('t-hidden');
                this.elem.wisetack_bid_section.addClass('t-hidden');
                await this.refreshWisetackInfo();
            }
        } catch (e) {
            let message = createErrorMessage('Something went wrong. Please contact support.');
            this.router.main_route.layout.toasts.addMessage(message);
            console.error(e);
            this.elem.wisetack_promo_section.addClass('t-hidden');
            await this.refreshWisetackInfo();
        }


    }

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        await super.load(request, next);
        let bid = request.data.bid;

        let groups = {};
        for (let line_item of bid.line_items) {
            let {group} = LineItemTypeConfig.get(line_item.type);
            if (groups[group] === undefined) {
                groups[group] = {
                    line_items: []
                };
            }
            groups[group].line_items.push(line_item);
        }
        let totalBidAmount = Number.of('0.00');
        for (let group of ['section', 'discount', 'fee']) {
            let config = groups[group];
            if (config === undefined) {
                continue;
            }
            let total = config.line_items.reduce((carry, {total}) => carry.plus(total), Number.of('0.00')),
                elem = $(line_item_table_tpl({
                    name: LineItemGroupConfig[group].name,
                    total: Number.toCurrency(total)
                }));

            totalBidAmount = totalBidAmount.plus(total);

            config.elem = {
                root: elem,
                table: elem.fxFind('table')
            };
            this.elem.root.append(elem);
            config.table = this.createLineItemTable(config.elem.table, config.line_items, group);
        }
        this.state.bid_total = parseFloat(totalBidAmount).toFixed(2);
        this.state.groups = groups;

        // Wisetack Promo Message
        this.loadElements();
        await this.refreshWisetackInfo();
    };

    loadElements() {
        Tooltip.initAll(this.elem.root);
        const wisetack_component = $(wisetack_promo_tpl());
        this.elem.root.append(wisetack_component);

        this.elem.wisetack_loading_section = this.elem.root.fxFind('wt-loading-section');

        this.elem.wisetack_bid_section = this.elem.root.fxFind('wt-bid-section');
        this.elem.wisetack_financing_application_status = this.elem.root.fxFind('wt-financing-application-status');
        this.elem.wisetack_financing_application_created_at = this.elem.root.fxFind('wt-financing-application-createdAt');
        this.elem.wisetack_financing_application_approved_amount = this.elem.root.fxFind('wt-financing-application-approved-amount');
        this.elem.wisetack_financing_application_requested_amount = this.elem.root.fxFind('wt-financing-application-requested-amount');
        this.elem.status_callout = this.elem.root.fxFind('wt-status-callout');


        this.elem.wisetack_bid_link_section = this.elem.root.fxFind('wt-bid-link-section');
        this.elem.copy_payment_link_button = this.elem.root.fxFind('copy-payment-link-button');
        this.elem.wisetack_payment_link = this.elem.root.fxFind('wisetack-payment-link');
        this.elem.delete_loan_button = this.elem.root.fxFind('delete-loan-button');

        this.elem.required_actions_section = this.elem.root.fxFind('wt-required-actions-section');
        this.elem.required_actions = this.elem.root.fxFind('wt-required-actions');

        this.elem.wisetack_promo_section = this.elem.root.fxFind('wt-promo-section');
        this.elem.wisetack_promo_content = this.elem.root.fxFind('wt-promo-content');
        this.elem.wisetack_financing_application_button = this.elem.root.fxFind('wt-financing-application-button');

        onClick(this.elem.wisetack_financing_application_button, async () => {
            await this.sendFinancingApplication();
        });

        onClick(this.elem.copy_payment_link_button, async () => {
            navigator.clipboard.writeText(this.elem.wisetack_payment_link.val()).then(() => {
                this.router.main_route.layout.toasts.addMessage(createSuccessMessage('Link was copied.'));
            })
        });

        onClick(this.elem.delete_loan_button, async () => {
            this.openDeleteLoanModal();
        });
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        await super.unload(request, next);
        if (this.state.groups !== null) {
            for (let group of Object.keys(this.state.groups)) {
                this.state.groups[group].table.destroy();
            }
        }
        this.elem.root.empty();
    };

    boot(root) {
        super.boot(root);
        this.elem.root.show();
    };

    /**
     * Render page
     */
    render() {
        return line_items_tpl();
    };
}

module.exports = LineItems;
