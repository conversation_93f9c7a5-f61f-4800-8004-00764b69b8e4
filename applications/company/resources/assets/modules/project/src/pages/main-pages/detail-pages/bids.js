'use strict';

const Page = require('@ca-package/router/src/page');

const bids_tpl = require('@cam-project-tpl/pages/main-pages/detail-pages/bids.hbs');

class Bids extends Page {
    /**
     * Get child routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            list: {
                default: true,
                page: require('./bids-pages/list')
            },
            detail: {
                path: '/{bid_id}',
                page: require('./bids-pages/detail'),
                bindings: {
                    bid_id: 'uuid'
                }
            }
        };
    };

    /**
     * Toggle loader display
     *
     * @param {boolean} [show=true]
     */
    showLoader(show = true) {
        this.elem.loader.toggle(show);
    };

    /**
     * Get container for child pages
     *
     * @returns {jQuery}
     */
    getPageContainer() {
        return this.elem.pages;
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.loader = this.elem.root.fxFind('loader');
        this.elem.pages = this.elem.root.fxFind('pages');
    };

    /**
     * Render page
     */
    render() {
        return bids_tpl();
    };
}

module.exports = Bids;
