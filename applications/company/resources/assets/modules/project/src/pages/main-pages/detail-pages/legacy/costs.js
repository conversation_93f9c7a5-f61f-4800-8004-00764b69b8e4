(function($){
    var state = {};
    module.exports = {};

    var projectCostsArray = [];

    var costTypesDefault = {
        'GENERAL': 6,
        'COMMISSION': 7
    };

    var pendingDeleteID = '';

    function setup() {
        //Edit Project Costs
        $('#addProjectCost').click(addProjectCost);

        $('#cancelProjectCostEdit').click(closeProjectCostEdit);

        $('#cancelProjectCost').click(closeProjectCost);

        $('.projectCostDate').datepicker();

        $('#addProjectCostType').click(addProjectCostType);

        $('#saveProjectCost').click(saveProjectCost);

        $('#deleteProjectCost').click(deleteProjectCost);

        $('#saveProjectCostTypes').click(saveProjectCostTypes);

        $('#cancelProjectCostTypes').click(cancelProjectCostTypes);

        $('.confirmDeleteProjectCost').click(confirmDeleteProjectCost);

        $('.cancelDeleteProjectCost').click(cancelDeleteProjectCost);


        $('select[name="costType"]').change(projectCostTemplate);

        var pageHeight = $( window ).height() * .85;
        $('#projectCostTypesModal').css('height',pageHeight);

    }
    module.exports.setup = setup;

    function setProjectCostTypes(costTypes, costCategories){
        //Cost Types
        var costTypesTable = $('.projectCostEditTable tbody');
        costTypesTable.empty();
        var costTypeDelete = '';
        var costTypesSelect = $('#projectCostEditModal select[name="costType"]');
        costTypesSelect.empty();
        costTypesSelect.append('<option value="">--</option>');
        var costCategorySelect = $('<select name="projectCostCategoryID"></select>');
        costCategorySelect.append('<option value="">--</option>');

        $.each(costCategories, function (i, item) {
            costCategorySelect.append('<option value="'+item.projectCostCategoryID+'">'+item.name+'</option>');
        });

        $.each(costTypes, function (i, item) {
            if (item.type === costTypesDefault.GENERAL) {
                costTypeDelete = '<a class="icon-minus deleteProjectCostType"></a>';
            } else if (item.type === costTypesDefault.COMMISSION) {
                costTypeDelete = '';
            }
            var new_row = $('<tr data-cost-type="'+item.projectCostTypeID+'"><td></td><td><input name="title" type="text" value="'+item.title+'"><input type="hidden" name="projectCostTypeID" value="'+item.projectCostTypeID+'"></td><td>'+costTypeDelete+'</td></tr>');
            var new_select = costCategorySelect[0].cloneNode(true);
            $(new_select).val(item.projectCostCategoryID);
            new_row.find('td:first-child').append(new_select);
            costTypesTable.append(new_row);

            costTypesSelect.append('<option data-type="'+item.type+'" value="'+item.projectCostTypeID+'">'+item.title+'</option>');

            costTypesTable.find('tr[data-cost-type="'+item.projectCostTypeID+'"] td:last-child a.deleteProjectCostType').click(deleteProjectCostType);
        });
    }

    function setProjectCosts(array){
        var projectCostsTable = $('.projectCostsTable');
        if (Object.keys(array['projectCosts']).length > 0 || array['crewmanCosts'].length > 0) {
            projectCostsTable.show();
            $('.noProjectCosts').hide();
            var projecCostsTableBody = projectCostsTable.find('tbody');
            projecCostsTableBody.empty();
            var projectCostTotal = 0;
            $.each(array['projectCosts'], function (i, item) {
                var editCostType = '';
                if (projectCostsArray['settings']['editCosts'] === true) {
                    editCostType = '<td class="editProjectCost"><a class="icon-pencil tiny"></a></td>';
                } else {
                    editCostType = '<td></td>';
                }
                projectCostTotal += Number(item.cost.amount);
                projecCostsTableBody.append('<tr data-cost="'+item.projectCostID+'"><td class="viewProjectCost"><a>'+moment(item.incurredAt).format('MM/DD/YYYY')+'</a></td><td class="viewProjectCost"><a>'+item.cost_type.title+'</a></td><td class="viewProjectCost"><a>'+item.cost.amount+'</a></td>'+editCostType+'</tr>');
                $('[data-cost='+item.projectCostID+'] .viewProjectCost').click(viewProjectCost);
                $('[data-cost='+item.projectCostID+'] .editProjectCost').click(editProjectCost);
            });

            if (projectCostsArray['settings']['commissions'] === true){
                var crewmanLaborTotal = 0;
                var projectCrewmanLaborTable = $('#projectCrewmanLaborModal .projectCrewmanLaborTable');

                projectCrewmanLaborTable.find('tbody').empty();
                $.each(array['crewmanCosts'], function (i, item) {
                    crewmanLaborTotal += Number(item.total);
                    projectCrewmanLaborTable.append('<tr><td>' + item.firstName + ' ' + item.lastName + '</td><td>' + item.hours + '</td><td>' + item.total + '</td></tr>');
                });
                if (crewmanLaborTotal > 0) {
                    crewmanLaborTotalFormatted = parseFloat(crewmanLaborTotal).toFixed(2)
                    projecCostsTableBody.append('<tr data-open="projectCrewmanLaborModal"><td><a data-open="projectCrewmanLaborModal">-</a></td><td><a>Crewman Labor</a></td><td><a>' + crewmanLaborTotalFormatted + '</a></td><td></td></tr>');
                    projectCostTotal = projectCostTotal + crewmanLaborTotal;
                }

                projectCostTotal = parseFloat(projectCostTotal).toFixed(2);
                projecCostsTableBody.append('<tr><td></td><td><strong>Total</strong></td><td><strong>'+projectCostTotal+'</strong></td><td></td></tr>');
            }

        } else {
            projectCostsTable.hide();
            $('.noProjectCosts').show();
        }
    }

    var projectCosts = function(){
        $('#loading-image').show();
        $.ajax({
            url: window.fx_url.BASE + 'project-costs.php',
            dataType: "json",
            type: "POST",
            contentType: "application/x-www-form-urlencoded",
            data: {
                action: 'costs',
                projectID: state.id
            },
            success: function (response) {
                if (response.status === 1) {
                    projectCostsArray['projectCosts'] = response.result['projectCosts'];
                    projectCostsArray['crewmanCosts'] = response.result['crewmanCosts'];

                    //Project Costs
                    setProjectCosts(projectCostsArray);

                    $('#loading-image').hide();
                } else if (response.error) {
                    $('#projectCosts').hide();
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }

        });
    };

    function projectCostSettings(config) {
        Object.assign(state, config);

        $('#loading-image').show();
        $.ajax({
            url: window.fx_url.BASE + 'project-costs.php',
            dataType: "json",
            type: "POST",
            contentType: "application/x-www-form-urlencoded",
            data: {
                action: 'costSettings',
                projectID: state.id
            },
            success: function (response) {
                if (response.status === 1) {

                    projectCostsArray['costTypes'] = response.result['costTypes'];
                    projectCostsArray['costCategories'] = response.result['costCategories']
                    projectCostsArray['settings'] = response.result['settings'];
                    projectCostsArray['paymentTypes'] = response.result['paymentTypes'];
                    projectCostsArray['users'] = response.result['users'];

                    //Cost Types
                    setProjectCostTypes(projectCostsArray.costTypes, projectCostsArray.costCategories);

                    //Payment Types
                    var paymentTypesSelect = $('#projectCostEditModal select[name="paymentType"]');
                    paymentTypesSelect.empty();
                    paymentTypesSelect.append('<option value="">--</option>');
                    $.each(projectCostsArray.paymentTypes, function (i, item) {
                        paymentTypesSelect.append('<option value="'+item.projectPaymentTypeID+'">'+item.title+'</option>');
                    });

                    //Commission User
                    var commissionUserSelect = $('#projectCostEditModal select[name="commissionUser"]');
                    commissionUserSelect.empty();
                    commissionUserSelect.append('<option value="">--</option>');
                    $.each(projectCostsArray.users, function (i, item) {
                        commissionUserSelect.append('<option value="'+item.userID+'">'+item.userFirstName+' '+item.userLastName+'</option>');
                    });

                    if (projectCostsArray['settings']['editCostTypes'] !== true) {
                        $('#projectCostEditModal #editProjectCostTypes').hide();
                    }

                    if (projectCostsArray['settings']['editCosts'] !== true) {
                        $('#addProjectCost').hide();
                    }

                    projectCosts();

                    $('#loading-image').hide();
                } else if (response.error) {
                    $('#projectCosts').hide();
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }

        });
    };
    module.exports.get = projectCostSettings;

    function clearProjectCostEdit(){
        var projectCostModal = $('#projectCostEditModal');
        projectCostModal.find('input[name="costDate"]').val('');
        projectCostModal.find('select[name="costType"]').val('');
        projectCostModal.find('textarea[name="costDescription"]').val('');
        projectCostModal.find('select[name="paymentType"]').val('');
        projectCostModal.find('input[name="costGeneralAmount"]').val('');
        projectCostModal.find('select[name="commissionUser"]').val('');
        projectCostModal.find('input[name="costCommissionAmount"]').val('');
        projectCostModal.find('#projectCostID, #costID').val('');

        projectCostModal.find('select[name="costType"] option[temp]').remove();
        projectCostModal.find('select[name="commissionUser"] option[temp]').remove();

        projectCostModal.find('.projectCostTemplate, .costError').hide();

        projectCostModal.find('select[name="costType"]').attr('disabled', false);

        projectCostModal.find('[required]').each(function(){
            var $this = $(this);
            $this.parent().removeClass('is-invalid-label');
            $this.removeClass('is-invalid-input');
            $this.parent().find('.form-error').removeClass('is-visible');
        });

        if (projectCostsArray['settings']['editCostTypes'] === true) {
            projectCostModal.find('#editProjectCostTypes').show();
        }

        projectCostModal.find('.saveButtonGroup').addClass('add');
        projectCostModal.find('.deleteButtonGroup').hide();

        projectCostModal.foundation('close');
    }

    var addProjectCost = function() {
        var projectCostModal = $('#projectCostEditModal');
        projectCostModal.find('select[name="costType"]').attr('disabled', false);
        projectCostModal.foundation('open');
    };

    var closeProjectCostEdit = function(){
        clearProjectCostEdit();
    };

    var closeProjectCost = function(){
        var projectCostModal = $('#projectCostModal');
        projectCostModal.find('p.costDate').empty();
        projectCostModal.find('p.costType').empty();
        projectCostModal.find('p.costDescription').empty();
        projectCostModal.find('p.paymentType').empty();
        projectCostModal.find('p.costGeneralAmount').empty();
        projectCostModal.find('p.commissionUser').empty();
        projectCostModal.find('p.costCommissionAmount').empty();

        projectCostModal.find('.projectCostTemplate').hide();

        projectCostModal.foundation('close');
    };

    var viewProjectCost = function() {
        var projectCostModal = $('#projectCostModal');
        var costID = $(this).parent().attr('data-cost');

        var projectCost = projectCostsArray.projectCosts[costID];

        projectCostModal.find('.costDate').text(moment(projectCost.incurredAt).format('MM/DD/YYYY'));
        projectCostModal.find('.costType').text(projectCost.cost_type.title);
        projectCostModal.find('[data-type='+projectCost.type+']').show();
        if (projectCost.type === costTypesDefault.GENERAL) {
            if (projectCost.cost.projectPaymentTypeID) {
                var paymentType = $.grep(projectCostsArray.paymentTypes,function(e){
                    return e.projectPaymentTypeID == projectCost.cost.projectPaymentTypeID;
                });
                paymentType = paymentType[0];
                projectCostModal.find('.paymentType').text(paymentType.title);
            } else {
                projectCostModal.find('.paymentType').html('<span>N/A</span>');
            }

            projectCostModal.find('.costDescription').text(projectCost.cost.description);
            projectCostModal.find('.costGeneralAmount').text(projectCost.cost.amount);
        } else if (projectCost.type === costTypesDefault.COMMISSION) {
            projectCostModal.find('.commissionUser').text(projectCost.cost.user.firstName + ' ' + projectCost.cost.user.lastName);
            projectCostModal.find('.costCommissionAmount').text(projectCost.cost.amount);
        }

        projectCostModal.foundation('open');
    };

    var editProjectCost = function() {
        var projectCostModal = $('#projectCostEditModal');
        var costID = $(this).parent().attr('data-cost');

        var projectCost = projectCostsArray.projectCosts[costID];

        projectCostModal.find('#projectCostID').val(projectCost.projectCostID);
        projectCostModal.find('#costID').val(projectCost.costID);
        projectCostModal.find('input[name="costDate"]').val(moment(projectCost.incurredAt).format('MM/DD/YYYY'));
        if (projectCostModal.find('select[name="costType"] option[value="'+projectCost.projectCostTypeID+'"]').length >= 1) {
            projectCostModal.find('select[name="costType"] option[value="'+projectCost.projectCostTypeID+'"]').prop('selected', true);
        } else {
            projectCostModal.find('select[name="costType"]').append('<option data-type="'+projectCost.cost_type.type+'" value="'+projectCost.projectCostTypeID+'" temp>'+projectCost.cost_type.title+'</option>');
            projectCostModal.find('select[name="costType"] option[value="'+projectCost.projectCostTypeID+'"]').prop('selected', true);
        }

        projectCostModal.find('[data-type='+projectCost.type+']').show();
        if (projectCost.type === costTypesDefault.GENERAL) {
            projectCostModal.find('textarea[name="costDescription"]').val(projectCost.cost.description);

            projectCostModal.find('select[name="paymentType"] option[value="'+projectCost.cost.projectPaymentTypeID+'"]').prop('selected', true);
            projectCostModal.find('input[name="costGeneralAmount"]').val(projectCost.cost.amount);
        } else if (projectCost.type === costTypesDefault.COMMISSION) {
            if (projectCostModal.find('select[name="commissionUser"] option[value="'+projectCost.cost.userID+'"]').length >= 1) {
                projectCostModal.find('select[name="commissionUser"] option[value="'+projectCost.cost.userID+'"]').prop('selected', true);
            } else {
                projectCostModal.find('select[name="commissionUser"]').append('<option temp value="'+projectCost.cost.userID+'">'+projectCost.cost.user.firstName+' '+projectCost.cost.user.lastName+'</option>');
                projectCostModal.find('select[name="commissionUser"] option[value="'+projectCost.cost.userID+'"]').prop('selected', true);
            }
            projectCostModal.find('input[name="costCommissionAmount"]').val(projectCost.cost.amount);
        }

        projectCostModal.find('#editProjectCostTypes').hide();

        projectCostModal.find('select[name="costType"]').attr('disabled', true);

        projectCostModal.find('.saveButtonGroup').removeClass('add');
        projectCostModal.find('.deleteButtonGroup').show();

        projectCostModal.foundation('open');

        autosize.update(document.querySelector('textarea[name="costDescription"]'));
    };

    var projectCostTemplate = function() {
        var projectCostModal = $('#projectCostEditModal');
        var dataType = $('select[name="costType"] option:selected').attr('data-type');

        projectCostModal.find('.projectCostTemplate').hide();
        projectCostModal.find('[data-type='+dataType+']').show();

        if (dataType == costTypesDefault.GENERAL) {
            autosize.update(document.querySelector('textarea[name="costDescription"]'));
        }

        projectCostModal.find('[required]').each(function(){
            var $this = $(this);
            $this.parent().removeClass('is-invalid-label');
            $this.removeClass('is-invalid-input');
            $this.parent().find('.form-error').removeClass('is-visible');
        });
    };

    var addProjectCostType = function() {
        var costTypesTable = $('.projectCostEditTable tbody');
        var categories = projectCostsArray.costCategories;

        var costCategorySelect = $('<select name="projectCostCategoryID"></select>');
        costCategorySelect.append('<option value="">--</option>');

        $.each(categories, function (i, item) {
            costCategorySelect.append('<option value="'+item.projectCostCategoryID+'">'+item.name+'</option>');
        });

        if (costTypesTable.find('tr:last-child td input[name="title"]').val() != '') {
            var new_row = $('<tr data-cost-type=""><td></td><td><input name="title" type="text" value=""><input type="hidden" name="projectCostTypeID" value=""></td><td><a class="icon-minus deleteProjectCostType"></a></td></tr>');
            var new_select = costCategorySelect[0].cloneNode(true);
            new_row.find('td:first-child').append(new_select);
            costTypesTable.append(new_row);
            costTypesTable.find('tr:last-child td:last-child a.deleteProjectCostType').click(deleteProjectCostType);
        }
    };


    var saveProjectCostTypes = function () {
        var costTypesTable = $('.projectCostEditTable tbody');
        var projectCostTypesModal = $('#projectCostTypesModal');

        if (projectCostTypesModal.find('.costError').is(':visible')){
            projectCostTypesModal.find('.costError').hide();
        }

        var projectCostTypes = new Array();

        costTypesTable.find('tr').each(function (i) {
            var $this = $(this);
            var title = $this.find('td:eq(1)').find('input[name="title"]').val();
            var category = $this.find('td:eq(0)').find('select[name="projectCostCategoryID"]').val();

            if (title !== '' && category !== ''){
                projectCostTypes.push({
                    'projectCostCategoryID': category,
                    'projectCostTypeID': $this.find('td:eq(1)').find('input[name="projectCostTypeID"]').val(),
                    'title': title
                });
            } else {
                projectCostTypesModal.find('.costError').show().text('Category and type are required');
            }
        });

        if (!projectCostTypesModal.find('.costError').is(':visible')) {
            $('#loading-image').show();
            $.ajax({
                url: window.fx_url.BASE + 'project-costs.php',
                dataType: "json",
                type: "POST",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    action: 'editProjectCostTypes',
                    projectID: state.id,
                    projectCostTypes: projectCostTypes
                },
                success: function (response) {
                    if (response.status === 1) {
                        projectCostSettings();

                        var myDiv = document.getElementById('projectCostTypesModal');
                        myDiv.scrollTop = 0;

                        $('#projectCostTypesModal').foundation('close');
                        $('#projectCostEditModal').foundation('open');

                        $('#loading-image').hide();
                    } else {
                        projectCostTypesModal.find('.costError').show().text(response.error.message);
                        $('#loading-image').hide();
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }
            });
        }
    };

    var cancelProjectCostTypes = function(){
        var myDiv = document.getElementById('projectCostTypesModal');
        myDiv.scrollTop = 0;

        var projectCostTypesModal = $('#projectCostTypesModal');

        if (projectCostTypesModal.find('.costError').is(':visible')){
            projectCostTypesModal.find('.costError').hide();
        }

        $('#projectCostTypesModal').foundation('close');
        $('#projectCostEditModal').foundation('open');
    };

    var deleteProjectCostType = function() {
        $(this).parent().parent().remove();
    };

    var saveProjectCost = function () {
        var projectCostModal = $('#projectCostEditModal');
        var saveProjectCost = false;

        if (projectCostModal.find('.costError').is(':visible')){
            projectCostModal.find('.costError').hide();
        }

        projectCostModal.find('[required]').each(function(){
            var $this = $(this);
            if ($this.is(':visible')) {
                if ($this.val().trim() == '') {
                    $this.parent().addClass('is-invalid-label');
                    $this.addClass('is-invalid-input');
                    $this.parent().find('.form-error').addClass('is-visible');
                } else {
                    $this.parent().removeClass('is-invalid-label');
                    $this.removeClass('is-invalid-input');
                    $this.parent().find('.form-error').removeClass('is-visible');
                }
            }
        });

        if (!$(".is-visible")[0]){
            saveProjectCost = true;
        }

        if (saveProjectCost) {
            var costType = projectCostModal.find('select[name="costType"] option:selected').attr('data-type');

            var costAction = 'edit';

            if (projectCostModal.find('#projectCostID').val() == '' && projectCostModal.find('#costID').val() == '') {
                costAction = 'new';
            }

            var projectCostItem = {
                'costAction' : costAction,
                'projectCostID': projectCostModal.find('#projectCostID').val(),
                'incurredAt': projectCostModal.find('input[name="costDate"]').val(),
                'projectCostTypeID': projectCostModal.find('select[name="costType"]').val(),
                'type': costType,
                'costID': projectCostModal.find('#costID').val()
            };

            if (costType == costTypesDefault.GENERAL) {
                $.extend(projectCostItem, {
                    'description': projectCostModal.find('textarea[name="costDescription"]').val(),
                    'projectPaymentTypeID': projectCostModal.find('select[name="paymentType"]').val(),
                    'amount': projectCostModal.find('input[name="costGeneralAmount"]').val()
                });
            } else if (costType == costTypesDefault.COMMISSION) {
                $.extend(projectCostItem, {
                    'userID': projectCostModal.find('select[name="commissionUser"]').val(),
                    'amount': projectCostModal.find('input[name="costCommissionAmount"]').val()
                });
            }

            $('#loading-image').show();
            $.ajax({
                url: window.fx_url.BASE + 'project-costs.php',
                dataType: "json",
                type: "POST",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    action: 'saveProjectCost',
                    projectID: state.id,
                    projectCostItem: projectCostItem
                },
                success: function (response) {
                    if (response.status === 1) {

                        projectCosts();

                        clearProjectCostEdit();

                        $('#projectCostSuccessModal p.successText').text('Project Cost has been saved.');

                        $('#projectCostSuccessModal').foundation('open');

                        $('#loading-image').hide();
                    } else {
                        projectCostModal.find('.costError').show().text(response.error.message);
                        $('#loading-image').hide();
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }
            });
        }

    };

    var deleteProjectCost = function () {
        var costID = $(this).parent().parent().find('#projectCostID').val();

        pendingDeleteID = costID;

        $('#projectCostDeleteModal').foundation('open');

    };

    var confirmDeleteProjectCost = function () {
        var projectCostDeleteModal = $('#projectCostDeleteModal');

        if (projectCostDeleteModal.find('.costError').is(':visible')){
            projectCostDeleteModal.find('.costError').hide();
        }

        $('#loading-image').show();
        $.ajax({
            url: window.fx_url.BASE + 'project-costs.php',
            dataType: "json",
            type: "POST",
            contentType: "application/x-www-form-urlencoded",
            data: {
                action: 'deleteProjectCost',
                projectID: state.id,
                projectCostID: pendingDeleteID
            },
            success: function (response) {
                if (response.status === 1) {

                    projectCosts();

                    clearProjectCostEdit();

                    pendingDeleteID = '';

                    var projectCostSuccessModal = $('#projectCostSuccessModal');

                    projectCostDeleteModal.foundation('close');

                    $('#loading-image').hide();
                } else {
                    //@TODO Show error in delete modal
                    projectCostDeleteModal.find('.costError').show().text(response.error.message);
                    $('#loading-image').hide();
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }
        });

    };

    var cancelDeleteProjectCost = function () {
        pendingDeleteID = '';

        $('#projectCostDeleteModal').foundation('close');
        $('#projectCostEditModal').foundation('open');
    };
})(e$);
