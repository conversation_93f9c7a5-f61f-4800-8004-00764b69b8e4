const anchorme = require('anchorme').default;

(function($){
    const page = require('page');
    var state = {};
    var elem = {};
    module.exports = {};

    function setup(config) {
        Object.assign(elem, config);
        //Add Note
        $('.newNote').click(() => {
            page(`/${state.id}/notes/add`);
        });

        //Save New Note
        $('#addNewNote').click(addNewNote);

        //Close Add Note Modal
        $('#closeAddNoteModal').click(closeAddNoteModal);

        //Save Edit Note Modal
        $('#saveNote').click(saveEditNote);

        //Close Edit Note Modal
        $('#closeEditNoteModal').click(closeEditNoteModal);

        //Open Delete Note Modal
        $('#deleteNote').click(deleteNote);

        //Yes Delete Note
        $('#yesDeleteNote').click(yesDeleteNote);

        //Close Delete Note Modal
        $('#closeDeleteNoteModal').click(closeDeleteNoteModal);
    }
    module.exports.setup = setup;

    function projectNotes(config) {
        Object.assign(state, config);

        $('#projectNotes.pinnedNotes').empty();
        $('#projectNotes.nonPinnedNotes').empty();

        $.ajax({
            url: window.fx_url.BASE + 'getProjectNotes.php',
            dataType: "json",
            type: "GET",
            contentType: "application/x-www-form-urlencoded",
            data: {
                projectID: state.id
            },
            success: function (response) {
                if (!response == '') {
                    $.each(response, function (i, item) {
                        var noteTag = '';

                        if (item.noteEdited != null){
                            noteAddedDisplayName = '';
                            if (item.noteEditedFirstName != null && item.noteEditedLastName != null){
                                noteAddedDisplayName = ' by '+item.noteEditedFirstName+' '+item.noteEditedLastName;
                            }
                            noteAddedDisplay = '<span style="font-size:.7rem;">Edited'+noteAddedDisplayName+' on '+item.noteEdited+'</span>';
                        } else {
                            noteAddedDisplayName = '';
                            if (item.noteAddedFirstName != null && item.noteAddedLastName != null){
                                noteAddedDisplayName = ' by '+item.noteAddedFirstName+' '+item.noteAddedLastName;
                            }
                            noteAddedDisplay = '<span style="font-size:.7rem;">Added'+noteAddedDisplayName+' on '+item.noteAdded+'</span>';
                        }

                        if (item.noteTag == 'PC') {
                            noteTag = '<span style="font-style:normal">Project Created - </span>';
                        } else if (item.noteTag == 'E') {
                            noteTag = '<span style="font-style:normal">'+item.evaluationDescription+' Evaluation - </span>';
                        }

                        var noteEditButton = '<button class="button editNote" style="float:right;margin-bottom: 0rem;">Edit</button>';
                        var notePinnedStatus = item.isPinned == 1 ? 'on' : 'off';
                        var notePinnedButton = '<button class="pinNote '+notePinnedStatus+'" style="float:right;"></button>';
                        var noteText = anchorme(item.note.replace(/(\r\n|\r|\n)/g, '<br>'));

                        if (item.isPinned == '1'){
                            $('#projectNotes.pinnedNotes').append('<div class="callout primary note" style="padding: .25rem .25rem .25rem .75rem;" id="PinnedNote' + item.noteID + '" noteId="' + item.noteID + '"><div class="row expanded"><div class="medium-9 columns no-pad"><p style="margin-top: .5rem;">' + noteText + '</p><p style="margin-bottom: 0;">' + noteTag + '' + noteAddedDisplay + '</p></div><div class="medium-3 columns no-pad"><p style="margin-bottom:2rem;">'+notePinnedButton+'</p><p style="margin-bottom: 0;margin-right: .4rem;">'+noteEditButton+'</p></div></div></div>');

                            //$('.note#Note'+item.noteID+' button.editNote').click(editNote);
                            //$('.note#Note'+item.noteID+' button.pinNote').click(pinNote);
                        } else {
                            $('#projectNotes.nonPinnedNotes').append('<div class="callout primary note" style="padding: .25rem .25rem .25rem .75rem;" id="Note' + item.noteID + '" noteId="' + item.noteID + '"><div class="row expanded"><div class="medium-9 columns no-pad"><p style="margin-top: .5rem;">' + noteText + '</p><p style="margin-bottom: 0;">' + noteTag + '' + noteAddedDisplay + '</p></div><div class="medium-3 columns no-pad"><p style="margin-bottom:2rem;">'+notePinnedButton+'</p><p style="margin-bottom: 0;margin-right: .4rem;">'+noteEditButton+'</p></div></div></div>');
                            //$('.note#Note'+item.noteID+' button.editNote').click(editNote);
                            //$('.note#Note'+item.noteID+' button.pinNote').click(pinNote);
                        }
                    });

                    $('.note button.editNote').click(editNote);
                    $('.note button.pinNote').click(pinNote);


                    if ($('.pinnedNotes .callout').length > 0){
                        $('#projectNoteDivider').show();
                    } else {
                        $('#projectNoteDivider').hide();
                    }
                } else {
                    $('#projectNoteDivider').hide();
                }
            }, error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }
        });
    }
    module.exports.projectNotes = projectNotes;

    var addNote = function(){
        $('#addNoteModal').foundation('open');
        $('#addNoteModal').find('textarea').focus();
    };
    module.exports.addNote = addNote;

    var addNewNote = function(){
        var noteText = $('#addNoteModal textarea[name="note"]').val();
        var projectID = state.id;

        if ($('#addNoteModal input[name="isPinnedAdd"]').is(':checked')) {
            var isPinned = 1;
        } else {
            var isPinned = 0;
        }

        if (noteText == '') {
            $('#addNoteModal textarea[name="note"]').parent().addClass('is-invalid-label');
            $('#addNoteModal textarea[name="note"]').addClass('is-invalid-input');
            $('#addNoteModal textarea[name="note"]').parent().find('.form-error').addClass('is-visible');

        } else {
            elem.loader.show();

            var noteTag = null;

            $.ajax({
                url: window.fx_url.API_V1 + 'project/notes',
                dataType: "json",
                type: "POST",
                contentType: "application/json",
                data: JSON.stringify({
                    project_id: parseInt(projectID),
                    note: noteText,
                    is_pinned: isPinned === 1,
                    tag: noteTag
                }),
                success: function (response) {
                    $('#addNoteModal textarea[name="note"]').val('');
                    $('#addNoteModal input[name="isPinnedAdd"]').prop('checked', true);

                    elem.loader.hide();

                    $('#addNoteModal').foundation('close');
                    page(`/${state.id}/notes`);

                }, error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                    alert('Unable to add note, please contact support');
                    elem.loader.hide();
                }

            });
        }
    };

    var closeAddNoteModal = function(redirect = true){
        $('#addNoteModal textarea[name="note"]').val('');
        $('#addNoteModal input[name="isPinnedAdd"]').prop('checked', true);

        $('#addNoteModal').foundation('close');

        if (redirect) {
            page(`/${state.id}/notes`);
        }
    };
    module.exports.closeAddNote = closeAddNoteModal;

    var editNote = function(){
        elem.loader.show();
        var idToEdit = $(this).parent().parent().parent().parent().attr('noteId');
        $('#editNoteModal .idToEdit').val(idToEdit);

        $.ajax({
            url: window.fx_url.API_V1 + 'project/notes/' + idToEdit,
            dataType: "json",
            type: "GET",
            success: function (response) {
                $('#editNoteModal textarea[name="note"]').val(response.note);

                $('#editNoteModal [name="isPinnedEdit"]').prop('checked', response.is_pinned);

                $('#editNoteModal').foundation('open');

                var ta = document.querySelector('#editNoteModal textarea[name="note"]');
                autosize.update(ta);

                elem.loader.hide();
            }, error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
                alert('Unable to get note details, please contact support');
                elem.loader.hide();
            }

        });
    };

    var pinNote = function(){
        elem.loader.show();

        var idToEdit = $(this).parent().parent().parent().parent().attr('noteId');

        if ($(this).hasClass('on')){
            isPinned = 0;
        } else if ($(this).hasClass('off')) {
            isPinned = 1;
        }

        $.ajax({
            url: window.fx_url.API_V1 + 'project/notes/' + idToEdit,
            dataType: "json",
            type: "PATCH",
            contentType: "application/json",
            data: JSON.stringify({
                is_pinned: isPinned === 1
            }),
            success: function () {

                projectNotes();

                elem.loader.hide();
            }, error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
                alert('Unable to edit note, please contact support');
                elem.loader.hide();
            }
        });
    };

    var saveEditNote = function(){
        var idToEdit = $('#editNoteModal .idToEdit').val();
        var noteText = $('#editNoteModal textarea[name="note"]').val();

        if ($('#editNoteModal input[name="isPinnedEdit"]').is(':checked')) {
            var isPinned = 1;
        } else {
            var isPinned = 0;
        }

        if (noteText == '') {
            $('#editNoteModal textarea[name="note"]').parent().addClass('is-invalid-label');
            $('#editNoteModal textarea[name="note"]').addClass('is-invalid-input');
            $('#editNoteModal textarea[name="note"]').parent().find('.form-error').addClass('is-visible');

        } else {
            elem.loader.show();

            $.ajax({
                url: window.fx_url.API_V1 + 'project/notes/' + idToEdit,
                dataType: "json",
                type: "PATCH",
                contentType: "application/json",
                data: JSON.stringify({
                    note: noteText,
                    is_pinned: isPinned === 1
                }),
                success: function () {
                    $('#editNoteModal .idToEdit').val('');
                    $('#editNoteModal textarea[name="note"]').val('');
                    $('#editNoteModal input[name="isPinnedEdit"]').prop('checked', false);

                    projectNotes();

                    elem.loader.hide();

                    $('#editNoteModal').foundation('close');
                }, error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                    alert('Unable to edit note, please contact support');
                    elem.loader.hide();
                }

            });
        }
    };

    var closeEditNoteModal = function(){
        $('#editNoteModal .idToEdit').val('');
        $('#editNoteModal textarea[name="note"]').val('');
        $('#editNoteModal input[name="isPinnedEdit"]').prop('checked', false);

        $('#editNoteModal').foundation('close');
    };

    var deleteNote = function(){
        var idToDelete = $('#editNoteModal .idToEdit').val();
        $('#deleteNoteModal #idToDelete').val(idToDelete);

        $('#deleteNoteModal').foundation('open');
    };

    var yesDeleteNote = function(){
        elem.loader.show();

        var idToDelete = $('#deleteNoteModal #idToDelete').val();

        $.ajax({
            url: window.fx_url.API_V1 + 'project/notes/' + idToDelete,
            dataType: "json",
            type: "DELETE",
            success: function () {
                $('#deleteNoteModal #idToDelete').val('');

                projectNotes();

                elem.loader.hide();

                $('#deleteNoteModal').foundation('close');
            }, error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
                alert('Unable to delete note, please contact support');
                elem.loader.hide();
            }

        });
    };

    var closeDeleteNoteModal = function(){
        $('#deleteNoteModal #idToDelete').val('');

        $('#deleteNoteModal').foundation('close');
    };

})(e$);
