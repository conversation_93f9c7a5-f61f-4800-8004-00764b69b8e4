(function($){
    var state = {
        current_date: window.project_data.current_date,
        reschedule_description: ''
    };
    var elem = {};
    var users = {};

    var roles = {
        sales: {
            label: 'Sales',
            column: 'sales',
            eventTypes: ['Evaluation']
        },
        installation: {
            label: 'Installation',
            column: 'installation',
            eventTypes: ['Installation']
        }
    };
    var groups = {
        roles: {
            label: 'Roles',
            class: 'role-list no-bullet',
            id: ''
        },
        users: {
            label: 'Users',
            class: 'user-list no-bullet',
            id: 'sortable'
        }
    };

    var evalMap = null;
    var is_idevice = /iPhone|iPad|iPod/i.test(navigator.userAgent);
    var all_day_flag;
    var scheduledStart;
    var scheduledEnd;
    var scheduledStartHidden;
    var scheduledEndHidden;
    var today = new Date();
    today.setHours(0,0,0,0);

    module.exports = {};

    function setup(config) {
        Object.assign(elem, config);
        all_day_flag = $('input[name="scheduledStartTimeAllDay"]');
        scheduledStart = $('input[name="scheduledStartTime"]');
        scheduledEnd = $('input[name="scheduledEndTime"]');
        scheduledStartHidden = $('div#scheduledStartTimeHidden');
        scheduledEndHidden = $('div#scheduledEndTimeHidden');

        //Calendar Button Clicks
        $('#today').click(function () {
            $('#calendar').fullCalendar('today')
        });
        $('#dailyView').click(function () {
            $('#resourceWeekView, #monthlyView').removeClass('active');
            $('#dailyView').addClass('active');
            $('#calendar').fullCalendar('changeView', 'timelineDay');
        });
        $('#resourceWeekView').click(function () {
            $('#dailyView, #monthlyView').removeClass('active');
            $('#resourceWeekView').addClass('active');
            $('#calendar').fullCalendar('changeView', 'resourceWeek');
        });
        $('#monthlyView').click(function () {
            $('#dailyView, #resourceWeekView').removeClass('active');
            $('#monthlyView').addClass('active');
            $('#calendar').fullCalendar('changeView', 'month');
        });
        $('#previous').click(function () {
            $('#calendar').fullCalendar('prev')
        });
        $('#next').click(function () {
            $('#calendar').fullCalendar('next')
        });

        $('#continueWithoutEmailSchedule').click(function() {
            confirmAppointment();
            $('#emailWontSendModalSchedule').foundation('close');
        });

        $('#editAppointmentClose').on('click.fx', function(){
            var eventModal = $('#editEvent');
            eventModal.find('input[name="projectScheduleID"]').val('');
            eventModal.find('textarea[name="scheduleDescription"]').val('');
            eventModal.foundation('close');
        });

        $('#editAppointment').on('click.fx', function(){
            var eventModal = $('#editEvent');
            var projectScheduleID = eventModal.find('input[name="projectScheduleID"]').val();
            var description = eventModal.find('textarea[name="scheduleDescription"]').val();

            if (projectScheduleID != '') {
                elem.loader.show();
                $.ajax({
                    url: window.fx_url.API + 'project/event',
                    dataType: "json",
                    type: "PATCH",
                    contentType: "application/x-www-form-urlencoded",
                    data: {
                        projectScheduleID: projectScheduleID,
                        description: description
                    },
                    success: function (response) {
                        elem.loader.hide();

                        //Update Schedule
                        $('#scheduleEvaluationButton').empty();
                        $('#evaluationSchedule').empty();
                        $('#scheduleInstallationButton').empty();
                        $('#installationSchedule').empty();
                        $('input[name="projectScheduleID"]').val('');
                        projectSchedule();

                        eventModal.foundation('close');
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                        console.log(jqXHR.responseText);
                        alert('Unable to edit appointment, please contact support');
                        elem.loader.hide();
                    }
                });
            }
        });

        //Reload Calendar When the Filter is Changed
        $('select#filterResources').change(filterCalendar);

        // Click Cancel on Appointment Modal
        $('#confirmAppointmentNoSelect').click(function () {

            $('.callout.small.alert.response').remove();

            $('#startDateErr').hide();
            $('input[name="scheduledStartDate"]').removeClass("required").removeClass("is-invalid-input");
            $('#startTimeErr').hide();
            $('input[name="scheduledStartTime"]').removeClass("required").removeClass("is-invalid-input");
            $('#endDateErr').hide();
            $('input[name="scheduledEndDate"]').removeClass("required").removeClass("is-invalid-input");
            $('#endTimeErr').hide();
            $('input[name="scheduledEndTime"]').removeClass("required").removeClass("is-invalid-input");

            //Get Temporary ID
            var tempID = $('input[name="tempID"]').val();

            var allEvents = $('#calendar').fullCalendar('clientEvents');

            allEvents = allEvents.filter(function (el) {
                return el._id !== tempID;
            });

            //Remove Last Rendered Event
            $('#calendar').fullCalendar('removeEvents', tempID);

            //Unselect Event on Calendar
            $('#calendar').fullCalendar('unselect');

            //Remove Temp ID from form
            $('input[name="tempID"]').val('');

            $('input[name="scheduledStartTimeAllDay"]').removeAttr('checked');
            $('input[name="scheduledStartTime"], input[name="scheduledEndTime"]').removeAttr('disabled');
            $('div#scheduledStartTimeHidden, div#scheduledEndTimeHidden').empty();

            //Unselect Resource from Dropdown
            $('select[name="salesperson"]').find("option").attr('selected', false);

            //Close Modal
            $('#addEvent').foundation('close');
        });

        $('input[name="scheduledStartTime"]').focusout(function(){
            if($('input[name="scheduledStartTime"]').val()!=''){
                $('#startTimeErr').hide();
                $('input[name="scheduledStartTime"]').removeClass("required");
                $('input[name="scheduledStartTime"]').removeClass("is-invalid-input");

            }else{
                $('#startTimeErr').show();
                $('input[name="scheduledStartTime"]').addClass("required");
                $('input[name="scheduledStartTime"]').addClass("is-invalid-input");
            }
        });

        $('input[name="scheduledEndTime"]').focusout(function(){
            if($('input[name="scheduledEndTime"]').val()!=''){
                $('#endTimeErr').hide();
                $('input[name="scheduledEndTime"]').removeClass("required");
                $('input[name="scheduledEndTime"]').removeClass("is-invalid-input");
            }else{
                $('#endTimeErr').show();
                $('input[name="scheduledEndTime"]').addClass("required");
                $('input[name="scheduledEndTime"]').addClass("is-invalid-input");
            }
        });

        // Click Save on Appointment Modal
        $('#confirmAppointmentYes').click(function () {
            state.reschedule_description = '';

            if (state.customer.email !== null && state.customer.is_unsubscribed === false) {
                confirmAppointment();
            }
            else {
                if (state.customer.email === null){
                    var text = 'no email.';
                }
                if (state.customer.is_unsubscribed === true){
                    var text = 'unsubscribed from emails.';
                }

                $('#addEvent').foundation('close');
                $('#emailWontSendModalSchedule').foundation('open');
                $('#emailWontSendModalSchedule p').text('Email will not be sent because the customer has ' + text + ' Continue?');
            }
        });

        $('#dontContinueWithoutEmail').click(function () {
            $('#startDateErr').hide();
            $('input[name="scheduledStartDate"]').removeClass("required");
            $('input[name="scheduledStartDate"]').removeClass("is-invalid-input");
            $('#startTimeErr').hide();
            $('input[name="scheduledStartTime"]').removeClass("required");
            $('input[name="scheduledStartTime"]').removeClass("is-invalid-input");
            $('#endDateErr').hide();
            $('input[name="scheduledEndDate"]').removeClass("required");
            $('input[name="scheduledEndDate"]').removeClass("is-invalid-input");
            $('#endTimeErr').hide();
            $('input[name="scheduledEndTime"]').removeClass("required");
            $('input[name="scheduledEndTime"]').removeClass("is-invalid-input");

            //Get Temporary ID
            var tempID = $('input[name="tempID"]').val();

            var allEvents = $('#calendar').fullCalendar('clientEvents');

            allEvents = allEvents.filter(function (el) {
                return el._id !== tempID;
            });

            //Remove Last Rendered Event
            $('#calendar').fullCalendar('removeEvents', tempID);

            //Unselect Event on Calendar
            $('#calendar').fullCalendar('unselect');

            //Remove Temp ID from form
            $('input[name="tempID"]').val('');

            $('input[name="scheduledStartTimeAllDay"]').removeAttr('checked');
            $('input[name="scheduledStartTime"], input[name="scheduledEndTime"]').removeAttr('disabled');
            $('div#scheduledStartTimeHidden, div#scheduledEndTimeHidden').empty();

            //Unselect Resource from Dropdown
            $('select[name="salesperson"]').find("option").attr('selected', false);

            //Close Modal
            $('#emailWontSendModalSchedule').foundation('close');
        });

        var showFilter = $('.filter-show, #filter');

        showFilter.click(function () {
            if ($('#filterGroup').hasClass('show')) {
                //document.setCookie('view_calendar_filter', 'false');
                closeFilter();
            } else {
                //document.setCookie('view_calendar_filter', 'true');
                openFilter();
            }
        });

        // if (document.getCookie('view_calendar_filter') == 'true'){
        //     openFilter();
        // }
        var filterGroup = $('#filterGroup');

        $.ajax({
            url: window.fx_url.API + 'calendar/resources',
            dataType: "json",
            type: "GET",
            contentType: "application/x-www-form-urlencoded",
            data: {
                filter: '[]'
            },
            success: function (response) {
                elem.loader.hide();
                if (response.status === 1) {
                    var resource_filter = filterGroup.find('.list');

                    for (var i in groups) {
                        var group = groups[i];
                        parent = $('<ul></ul>').attr('class', 'filter-section no-bullet').appendTo(resource_filter);
                        parent.prepend('<li class="section-title"><span>'+group.label+'</span></li>');
                        group.elem = $('<ul></ul>').attr('label', group.label).attr('class', group.class).attr('id', group.id);
                        parent.find('li span').after(group.elem);
                    }
                    for (var i in roles) {
                        var role = roles[i];
                        groups['roles'].elem.append('<li class="role-item" data-id="'+role.column+'" data-label="role">'+role.label+'</li>');
                    }

                    $.each(response.result, function (i, item) {
                        if (item.id > 0) {
                            var user = $('<li class="ui-state-default" data-label="user" data-id="' + item.id + '"><a class="handle"><span class="move"></span></a>' + item.title + '</li>');
                            var dataRole = [];
                            for (var i in roles) {
                                var role = roles[i];
                                dataRole[i] = item[role.column];
                            }
                            user.data('roles',dataRole);

                            groups['users'].elem.append(user);

                            if (item.sales) {
                                $('select[name="projectSalesperson"]').append('<option value="' + item.id + '">' + item.title + '</option>');
                            }
                        }
                    });

                    $(window).trigger('resize');

                    var roleItem = filterGroup.find('li[data-label="role"]');
                    var userItem = filterGroup.find('li[data-label="user"]');
                    var activeRole = null;

                    function uncheckAll() {
                        roleItem.removeClass('active');
                        userItem.removeClass('active');
                    }

                    filterGroup.on('click.fx', 'li', function () {
                        var $this = $(this);

                        switch($this.data('label')) {
                            case 'role':
                                activeRole = $this;

                                if (activeRole.hasClass('active')) {
                                    uncheckAll();
                                } else {
                                    uncheckAll();
                                    activeRole.addClass('active');
                                    var role = activeRole.data('id');
                                    userItem.each(function() {
                                        var $this = $(this);
                                        if ($this.data('roles')[role] == true) {
                                            $this.addClass('active');
                                        }
                                    });
                                }
                                break;
                            case 'user':
                                if ($this.hasClass('active')) {
                                    $this.removeClass('active');
                                    if (activeRole) {
                                        activeRole.removeClass('active');
                                    }
                                } else {
                                    $this.addClass('active');
                                    if (activeRole) {
                                        activeRole.removeClass('active');
                                    }
                                }
                                break;
                        }
                    });

                    // var calendarFilterCookie = document.getCookie('calendar_filter');
                    // if (calendarFilterCookie !== '') {
                    //     try {
                    //         calendarFilterCookie = JSON.parse(calendarFilterCookie);
                    //
                    //         if (Array.isArray(calendarFilterCookie)){
                    //             for (i = 0; i < calendarFilterCookie.length; i++) {
                    //                 var cookieID = calendarFilterCookie[i];
                    //                 var newItem = resource_filter.find('li[data-id="'+cookieID+'"]');
                    //                 newItem.addClass('active');
                    //                 if (newItem.hasClass('role-item')) {
                    //                     activeRole = newItem;
                    //                 }
                    //             }
                    //         }
                    //
                    //         if (resource_filter.find('li').hasClass('active')) {
                    //             showFilter.addClass('active');
                    //         }
                    //     }
                    //     catch(err) {}
                    // }

                    function getFilterSelected() {
                        var filterCalendar = [];
                        resource_filter.find('li.active').each(function(index){
                            filterCalendar[index] = $(this).data('id');
                        });
                        return filterCalendar;
                    };

                    $('#filter-apply').click(function() {
                        var selectedItems = getFilterSelected();

                        if (selectedItems.length > 0) {
                            showFilter.addClass('active');
                        } else {
                            showFilter.removeClass('active');
                        }

                        //document.setCookie('calendar_filter', JSON.stringify(selectedItems));
                        var view = $('#calendar').fullCalendar('getView');
                        var sortableList = $('#sortable');

                        if (sortableList.hasClass('changed')) {
                            elem.loader.show();
                            $.ajax({
                                url: window.fx_url.API + 'calendar/resources/order',
                                dataType: "json",
                                type: "POST",
                                contentType: "application/x-www-form-urlencoded",
                                data: {
                                    sort: getUserSortOrder()
                                },
                                success: function (response) {
                                    if (response.status === 1) {
                                        sortableList.removeClass('changed');
                                        filterCalendar(view.name);
                                    }
                                },
                                error: function (jqXHR, textStatus, errorThrown) {
                                    console.log(textStatus, errorThrown);
                                    console.log(jqXHR.responseText);
                                }
                            });
                        } else {
                            filterCalendar(view.name);
                        }

                    });

                    $('#filter-clear').click(function() {
                        uncheckAll();
                        showFilter.removeClass('active');

                        //document.setCookie('calendar_filter', JSON.stringify(getFilterSelected()));

                        var view = $('#calendar').fullCalendar('getView');
                        filterCalendar(view.name);
                    });

                    if ($('.newCustomerDisplay').length > 0) {
                        addNewCustomer();
                    } else {
                        var views = {
                            D: {
                                button: '#dailyView',
                                view: 'timelineDay'
                            },
                            M: {
                                button: '#monthlyView',
                                view: 'month'
                            },
                            R: {
                                button: '#resourceWeekView',
                                view: 'resourceWeek'
                            },
                            LD: {
                                button: '#listDay',
                                view: 'listDay'
                            },
                            LW: {
                                button: '#listWeek',
                                view: 'listWeek'
                            },
                            LM: {
                                button: '#listMonth',
                                view: 'listMonth'
                            }
                        };

                        // var cookie_view = document.getCookie('calendar_view');
                        var cookie_view = '';
                        if (typeof views[cookie_view] === 'undefined') {
                            cookie_view = $(window).width() < 640 ? 'LD' : 'D';
                        } else if ($(window).width() < 640 && $.inArray(cookie_view, ['LD', 'LW', 'LM']) < 0) {
                            cookie_view = 'LD';
                        }

                        var view = views[cookie_view];
                        for (var i in views) {
                            if (i === cookie_view) {
                                continue;
                            }
                            $(views[i].button).removeClass('active');
                        }
                        $(view.button).addClass('active');
                        filterCalendar(view.view);
                    }

                    $('#sortable').sortable({
                        delay: 200,
                        handle: '.handle',
                        stop: function( event, ui ) {
                            $(this).addClass('changed');
                        }
                    });
                    $('#sortable').disableSelection();
                } else {
                    $('#calendar').empty();
                    $('.dashboard-filter-bar').empty();
                    $('#noResourcesModal').foundation('open');
                }

            }, error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }
        });

        //Cancel Confirm No
        $('#cancelConfirmNo').click(function () {
            $('#cancelEvent').foundation('close');
        });

        //Cancel Confirm Yes
        $('#cancelConfirmYes').click(function () {

            var idToCancel =  $('#cancelEvent').find('[name="tempID"]').val();

            if (idToCancel != '') {
                $.ajax({
                    url: window.fx_url.API + 'project/event/cancel',
                    dataType: "json",
                    type: "PUT",
                    contentType: "application/x-www-form-urlencoded",
                    data: {
                        projectScheduleID: idToCancel,
                        projectID: state.id
                    },
                    success: function (response) {
                        if (response.success) {
                            //Remove Calendar and Display Map
                            $('#scheduleEvaluationButton').empty();
                            $('#evaluationSchedule').empty();
                            $('#scheduleInstallationButton').empty();
                            $('#installationSchedule').empty();
                            $('#generalEvaluationSchedule').empty();
                            $('#generalInstallationSchedule').empty();

                            $('#scheduleList').show();
                            $('#scheduleCalendar').hide();
                            $('#cancelScheduleButton').remove();
                            $('#calendar').empty();

                            //Get New Project Schedule
                            projectSchedule();

                            $('#cancelEvent').foundation('close');
                        }
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                        console.log(jqXHR.responseText);
                        alert('Unable to cancel event, please contact support');
                    }
                });
            }
        });

        //stops the iPad / Android Keybaord from popping up on the date picker
        $('input[name="scheduledStartTime"]').prop('readonly', 'readonly');
        $('input[name="scheduledEndTime"]').prop('readonly', 'readonly');

        if(is_idevice) {
            //change the input types for time to
            //html 5 "time" so that we don't have
            //interface issues
            $('#scheduledStartTime').removeClass('timepicker');
            $('#scheduledEndTime').removeClass('timepicker');

            $('#scheduledStartTime').attr('type','time');
            $('#scheduledEndTime').attr('type', 'time');
        }

        $('#scheduledStartTime, #scheduledEndTime, #scheduledStartDate, #scheduledEndDate, #scheduleSalesperson').focus(function(){
            if(is_idevice){
                //without this, the time controls lost focus and made data entry impossible.
                $(window).scrollTop(0);
            }
        });

        //show warning if scheduling date in past
        $('#scheduledStartDate').on('change', function(){
            console.log($('#scheduledStartDate'));
            $('.callout.small.alert').remove();
            if (new Date($('#scheduledStartDate').val()) < today || new Date($('#scheduledEndDate').val()) < today) {
                if ($('.callout.small.alert').length < 1){
                    $('#addEvent').append('<div class="callout small alert"><p>Please note that one of the selected dates is in the past.</p></div>');
                    $('#scheduledEndDate').trigger("change");
                }
            }
        });

        $('#scheduledEndDate').on('change', function(){
            $('.callout.small.alert').remove();
            if (new Date($('#scheduledStartDate').val()) < today|| new Date($('#scheduledEndDate').val()) < today) {
                if ($('.callout.small.alert').length < 1){
                    $('#addEvent').append('<div class="callout small alert"><p>Please note that one of the selected dates is in the past.</p></div>');
                }
            }
        });

        $(".datepickerFrom").datepicker({
            defaultDate: "+1w",
            changeMonth: true,
            numberOfMonths: 1,
            onClose: function (selectedDate) {
                $(".datepickerTo").datepicker("option", "minDate", selectedDate);
                $('#scheduledEndDate').trigger('change');
            }
        });
        $(".datepickerTo").datepicker({
            defaultDate: "+1w",
            changeMonth: true,
            numberOfMonths: 1,
            onClose: function (selectedDate) {
                $(".datepickerFrom").datepicker("option", "maxDate", selectedDate);
            }
        });

        all_day_flag.click(function () {
            setAllDayFlag($(this).is(':checked'), false);
        });
    }
    module.exports.setup = setup;

    function setAllDayFlag(active, set_elem = true) {
        var checked = all_day_flag.is(':checked');
        if (set_elem && checked === active) {
            return;
        }
        if (set_elem) {
            all_day_flag.prop('checked', active);
        }

        var oldStartTime, oldEndTime;

        if (active) {
            scheduledStart.attr('disabled', 'disabled').css('background-color', '#bdbdbd');
            scheduledEnd.attr('disabled', 'disabled').css('background-color' , '#bdbdbd');

            oldStartTime = scheduledStart.val();
            oldEndTime = scheduledEnd.val();

            scheduledStartHidden.text(oldStartTime);
            scheduledEndHidden.text(oldEndTime);

            scheduledStart.val(is_idevice ? '00:00' : '12 : 00 AM');
            scheduledEnd.val(is_idevice ? '23:59' : '11 : 59 PM');
        } else {
            scheduledStart.removeAttr('disabled').css('background-color', '#ffffff');
            scheduledEnd.removeAttr('disabled').css('background-color', '#ffffff');

            oldStartTime = scheduledStartHidden.text();
            oldEndTime = scheduledEndHidden.text();
            if (oldStartTime === '') {
                oldStartTime = is_idevice ? '08:00' : '08 : 00 AM';
            }
            if (oldEndTime === '') {
                oldEndTime = is_idevice ? '10:00' : '10 : 00 AM';
            }

            scheduledStart.val(oldStartTime);
            scheduledEnd.val(oldEndTime);
        }
    }

    function initializeMap() {
        //try {
        if ($('.fc-toolbar').length > 0) {
            var locations = $('#calendar').fullCalendar('clientEvents');

            var sD = state.current_date.split('-');
            var start = new Date(sD[0], sD[1] - 1, sD[2]);
            //var end = new Date(sD[0], sD[1] - 1, sD[2], 23, 59, 59, 999);
            var end = new Date(start.getTime() + 86400000);

            //console.log(locations);

            var newEvents = [];
            $.each(locations, function(i, item) {
                if (item.latitude != "0.00000000" && item.longitude != "0.00000000"){
                    newEvents.push(item);
                }
            });
            setMapLocations(newEvents, start, end);
        }
        //}
        // catch (e) {
        //     console.log(e.name);
        //     console.log(e.message);
        // }
    }

    function setMapLocations(locations, start, end) {
        //try {
        if (evalMap == null) {
            evalMap = new MapSet(document.getElementById('calendarMap'), window.project_data.company.latitude, window.project_data.company.longitude);
        }
        if (typeof (start) == "string")
            start = moment(start, "YYYY MM DD");
        if (typeof (end) == "string")
            end = moment(end, "YYYY MM DD");
        evalMap.SetMapLocations(locations, start, end);

        var lat = state.latitude;
        var long = state.longitude;

        if (lat !== '0.00000000' || long !== '0.00000000') {
            evalMap.MapLocation(lat, long, `${state.address}, ${state.city}, ${state.state}, ${state.zip}`);
        }
        //}
        // catch (e) {
        //     console.log(e.name);
        //     console.log(e.message);
        // }
    }

    function htmlEncode(value){
        return $('<div/>').text(value).html();
    }

    var validateDates = function() {
        var elems = {
            start_date: {
                input: $('input[name="scheduledStartDate"]'),
                error: $('#startDateErr')
            },
            start_time: {
                input: $('input[name="scheduledStartTime"]'),
                error: $('#startTimeErr')
            },
            end_date: {
                input: $('input[name="scheduledEndDate"]'),
                error: $('#endDateErr')
            },
            end_time: {
                input: $('input[name="scheduledEndTime"]'),
                error: $('#endTimeErr')
            }
        };
        var start_date = elems.start_date.input.val().trim(),
            start_time = !is_idevice ? elems.start_time.input.wickedpicker('time') : elems.start_time.input.val(),
            start_datetime = null,
            end_date = elems.end_date.input.val().trim(),
            end_time = !is_idevice ? elems.end_time.input.wickedpicker('time') : elems.end_time.input.val(),
            time_format = !is_idevice ? 'h:mmA' : 'HH:mm',
            errors = {};

        if (start_date === '') {
            errors.start_date = 'Start date is required';
            start_date = null;
        }
        if (start_time === '') {
            errors.start_time = 'Start time is required';
            start_datetime = null;
        } else if (start_date !== null) {
            start_datetime = moment(start_date + ' ' + start_time.replace(/ /g, ''), 'MM/DD/YYYY ' + time_format);
        }

        if (end_date === '') {
            errors.end_date = 'End date is required';
            end_date = null;
        }
        if (end_time === '') {
            errors.end_time = 'End time is required';
        } else if (start_datetime !== null && end_date !== null) {
            var end_datetime = moment(end_date + ' ' + end_time.replace(/ /g, ''), 'MM/DD/YYYY ' + time_format);
            if (end_datetime.isSameOrBefore(start_datetime, 'minute')) {
                errors.end_time = 'End time must be after start time';
            }
        }

        var error_count = 0;
        for (var name in elems) {
            var elem = elems[name];
            if (errors[name]) {
                elem.error.text(errors[name]).show();
                elem.input.addClass('required').addClass('is-invalid-input');
                error_count++;
                continue;
            }
            elem.error.hide();
            elem.input.removeClass('required').removeClass('is-invalid-input');
        }
        return error_count === 0;
    };

    var confirmAppointment = function(){
        //Make sure there are no empty input fields for date / time
        var eventModal = $('#addEvent');
        var calendar = $('#calendar');

        if (validateDates() ==true){

            if ($('input[name="scheduledStartTimeAllDay"]').is(':checked')) {
                var startTimeAllDay = 1;
            } else {
                var startTimeAllDay = 0;
            }

            elem.loader.show();

            if(is_idevice){
                var userStartTime = $('input[name="scheduledStartTime"]').val();
                var userEndTime = $('input[name="scheduledEndTime"]').val();

            } else{
                var userStartTime = $('input[name="scheduledStartTime"]').wickedpicker('time').toString();
                var userEndTime = $('input[name="scheduledEndTime"]').wickedpicker('time').toString();

                var startHours = userStartTime.split(':')[0];
                var startMinutes = userStartTime.split(':')[1];
                startMinutes = startMinutes.slice(0, -2)
                var startSuffix = userStartTime.split(' ')[3];

                startHours = startHours.trim();
                startMinutes = startMinutes.trim();
                startSuffix = startSuffix.trim();

                if (startSuffix == 'pm' || startSuffix == 'PM') {
                    startHours = startHours != '12' ? parseInt(startHours) + parseInt(12) : startHours;
                }

                userStartTime = startHours + ":" + startMinutes + ":" + "00";

                var endHours = userEndTime.split(':')[0];
                var endMinutes = userEndTime.split(':')[1];
                endMinutes = endMinutes.slice(0, -2)
                var endSuffix = userEndTime.split(' ')[3];

                endHours = endHours.trim();
                endMinutes = endMinutes.trim();
                endSuffix = endSuffix.trim();

                if (endSuffix == 'pm' || endSuffix == 'PM') {
                    endHours = endHours != '12' ? parseInt(endHours) + parseInt(12) : endHours;
                }

                userEndTime = endHours + ":" + endMinutes + ":" +"00";

            }

            var notifyCustomer = '';
            if (eventModal.find('input[name="notifyCustomer"]:checked').length >= 1){
                notifyCustomer = 1;
            } else {
                notifyCustomer = 0;
            }


            if ($('input[name="projectScheduleID"]').val() == '') {
                //Add New Event to DB
                $.ajax({
                    url: window.fx_url.API + 'project/events',
                    dataType: "json",
                    type: "POST",
                    contentType: "application/x-www-form-urlencoded",
                    data: {
                        projectID: state.id,
                        startDate: $('input[name="scheduledStartDate"]').val(),
                        startTime: userStartTime,
                        startTimeAllDay: startTimeAllDay,
                        endDate: $('input[name="scheduledEndDate"]').val(),
                        endTime: userEndTime,
                        scheduleType: $('#eventType').val(),
                        scheduledUserID: $('select[name="salesperson"]').val(),
                        description: $('textarea[name="scheduleDescription"]').val(),
                        notifyCustomer: notifyCustomer
                    },
                    success: function (response) {
                        if (response.success) {
                            if (eventModal.find('.callout.small.alert.response').length >= 1){
                                eventModal.find('.callout.small.alert.response').remove();
                            }

                            calendar.fullCalendar('unselect');

                            eventModal.foundation('close');

                            //Remove Schedule Modal Inputs
                            $('select[name="salesperson"]').val('');
                            $('input[name="scheduledStartDate"]').val('');
                            $('input[name="scheduledStartTime"]').val('');
                            $('input[name="scheduledStartTimeAllDay"]').removeAttr('checked');
                            $('input[name="scheduledStartTime"], input[name="scheduledEndTime"]').removeAttr('disabled');
                            $('div#scheduledStartTimeHidden, div#scheduledEndTimeHidden').empty();
                            $('input[name="scheduledEndDate"]').val('');
                            $('input[name="scheduledEndTime"]').val('');
                            $('input[name="projectScheduleID"]').val('');
                            $('textarea[name="scheduleDescription"]').val('');

                            //Remove Calendar and Display Map
                            $('#scheduleEvaluationButton').empty();
                            $('#evaluationSchedule').empty();
                            $('#scheduleInstallationButton').empty();
                            $('#installationSchedule').empty();
                            $('#scheduleList').show();
                            $('#scheduleCalendar').hide();
                            $('#cancelScheduleButton').remove();
                            calendar.empty();

                            //Get New Project Schedule
                            projectSchedule();
                            elem.loader.hide();

                            $('#confirmAppointmentNoDrag').off('click.reschedule_modal');
                        } else {
                            elem.loader.hide();
                            if (eventModal.find('.callout.small.alert.response').length < 1){
                                eventModal.append('<div class="callout small alert response"><p>'+response.error+'</p></div>');
                            }
                        }
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                        console.log(jqXHR.responseText);
                        alert('Unable to add appointment, please contact support');
                        elem.loader.hide();
                    }
                });
            } else {
                //Update Existing Event
                $.ajax({
                    url: window.fx_url.API + 'project/events',
                    dataType: "json",
                    type: "PUT",
                    contentType: "application/x-www-form-urlencoded",
                    data: {
                        projectScheduleID: $('input[name="projectScheduleID"]').val(),
                        projectID: state.id,
                        startDate: $('input[name="scheduledStartDate"]').val(),
                        startTime: userStartTime,
                        startTimeAllDay: startTimeAllDay,
                        endDate: $('input[name="scheduledEndDate"]').val(),
                        endTime: userEndTime,
                        scheduleType: $('#eventType').val(),
                        scheduledUserID: $('select[name="salesperson"]').val(),
                        description: $('textarea[name="scheduleDescription"]').val(),
                        notifyCustomer: notifyCustomer
                    },
                    success: function (response) {
                        if (response.status === 1) {
                            if (eventModal.find('.callout.small.alert.response').length >= 1){
                                eventModal.find('.callout.small.alert.response').remove();
                            }

                            eventModal.foundation('close');

                            //Remove Schedule Modal Inputs
                            $('select[name="salesperson"]').val('');
                            $('input[name="scheduledStartDate"]').val('');
                            $('input[name="scheduledStartTime"]').val('');
                            $('input[name="scheduledStartTimeAllDay"]').removeAttr('checked');
                            $('input[name="scheduledStartTime"], input[name="scheduledEndTime"]').removeAttr('disabled');
                            $('div#scheduledStartTimeHidden, div#scheduledEndTimeHidden').empty();
                            $('input[name="scheduledEndDate"]').val('');
                            $('input[name="scheduledEndTime"]').val('');
                            $('input[name="projectScheduleID"]').val('');
                            $('textarea[name="scheduleDescription"]').val('');

                            //Remove Calendar and Display Map
                            $('#scheduleEvaluationButton').empty();
                            $('#evaluationSchedule').empty();
                            $('#scheduleInstallationButton').empty();
                            $('#installationSchedule').empty();
                            $('#scheduleList').show();
                            $('#scheduleCalendar').hide();
                            $('#cancelScheduleButton').remove();
                            calendar.empty();

                            //Get New Project Schedule
                            projectSchedule();
                            elem.loader.hide();

                            $('#confirmAppointmentNoDrag').off('click.reschedule_modal');
                        } else {
                            elem.loader.hide();
                            if (eventModal.find('.callout.small.alert.response').length < 1){
                                eventModal.append('<div class="callout small alert response"><p>'+response.error.message+'</p></div>');
                            }
                        }
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                        console.log(jqXHR.responseText);
                        alert('Unable to reschedule event, please contact support');
                        elem.loader.hide();
                    }
                });
            }
        } else{
            // alert("issues");
            return false;
        }
    };


    function editEvent(event) {
        var eventModal = $('#editEvent');

        var nameDisplay;

        if (event.businessName != null) {
            nameDisplay = event.businessName;
        } else {
            nameDisplay = event.firstName + ' ' + event.lastName;
        }

        eventModal.find('input[name="projectScheduleID"]').val(event.projectScheduleID);
        eventModal.find('textarea[name="scheduleDescription"]').val(event.description);

        eventModal.find('#modalTitle').text(event.scheduleType + ' Appointment');
        eventModal.find('p.lead.name').text('Edit Appointment Description For ' + nameDisplay);

        eventModal.foundation('open');
    }

    function moveEvent(event, delta, revertFunc, jsEvent, ui, view) {

        var eventModal = $('#addEvent');
        var calendar = $('#calendar');

        if (delta){
            eventModal.find('.button.drag').show();
            eventModal.find('.button.select').hide();

            var confirm_appt_no_drag = $('#confirmAppointmentNoDrag');
            confirm_appt_no_drag.on('click.reschedule_modal', function(){
                eventModal.foundation('close');
                //event.calendarBgColor = originalResource.calendarBgColor;
                revertFunc();
                confirm_appt_no_drag.off('click.reschedule_modal');
            });
        }

        var newResource = $('#calendar').fullCalendar('getResourceById', event.resourceId);

        if (newResource !== undefined) {
            event.calendarBgColor = newResource.calendarBgColor;
        }

        var selectSalesperson = eventModal.find($('select[name="salesperson"]')).empty();

        for (var i in users) {
            var user = users[i];

            for (var i2 in roles) {
                var role = roles[i2];

                if (user[i2] == 1 && $.inArray(event.scheduleType, role.eventTypes) !== -1){
                    selectSalesperson.append('<option value="' + user.id + '">' + user.title + '</option>');
                }
            }
        }

        var nameDisplay;

        if (event.businessName != null) {
            nameDisplay = event.businessName;
        } else {
            nameDisplay = event.firstName + ' ' + event.lastName;
        }

        if (eventModal.find('input[name="projectScheduleID"]').val() != '') {
            var name = 'Update';
            var type = 'Reschedule';
        } else {
            var name = 'Add';
            var type = 'Schedule';
        }

        eventModal.find('textarea[name="scheduleDescription"]').val(event.description);

        eventModal.find('#modalTitle').text(type + ' ' + event.scheduleType);

        if (event.scheduleType == 'Evaluation') {
            name = name + ' Appointment For ' + nameDisplay;
            eventModal.find('p.lead.name').text(name);
            eventModal.find('.scheduledTitle').text('Salesperson');
        } else {
            name = name + ' Installation For ' + nameDisplay;
            eventModal.find('p.lead.name').text(name);
            eventModal.find('.scheduledTitle').text('Installer');
        }

        //set Start Date & Time
        var startDate = moment(event.start).format("MM/DD/YYYY");
        var startTime = moment(event.start).format("HH:mm");
        var startTimeAMPM = moment(event.start).format("h : mm A");
        var startTimeString = startTime.toString();

        var StartTimeOptions ={
            now: startTimeString,
            twentyFour: false,  //Display 24 hour format, defaults to false
            upArrow: 'wickedpicker__controls__control-up',  //The up arrow class selector to use, for custom CSS
            downArrow: 'wickedpicker__controls__control-down', //The down arrow class selector to use, for custom CSS
            close: 'wickedpicker__close', //The close class selector to use, for custom CSS
            hoverState: 'hover-state', //The hover state class to use, for custom CSS
            title: "Select a time", //The Wickedpicker's title,
            showSeconds: false, //Whether or not to show seconds,
            secondsInterval: 1, //Change interval for seconds, defaults to 1,
            minutesInterval: 5, //Change interval for minutes, defaults to 1
            beforeShow: null, //A function to be called before the Wickedpicker is shown
            show: function(){
                var modal = $('input[name="scheduledStartTime"]').closest('#addEvent');
                var datePicker = $('body').find('.wickedpicker');
                if(!modal.length) {
                    $(datePicker).css('z-index', 'auto');
                    return;
                }
                var zIndexModal = $(modal).css('z-index');
                $(datePicker).css('z-index', zIndexModal + 1);
            }, //A function to be called when the Wickedpicker is shown
            clearable: false //Make the picker's input clearable (has clickable "x")
        };

        //set End Date & Time
        var event_end = moment(event.end);
        if (event.isAllDay === 1) {
            event_end.subtract(1, 'day').endOf('day');
        }
        var endDate = event_end.format("MM/DD/YYYY");
        var endTime = event_end.format("HH:mm");
        var endTimeAMPM = event_end.format("h : mm A");
        var endTimeString = endTime.toString();
        var EndTimeOptions ={
            now: endTimeString,
            twentyFour: false,  //Display 24 hour format, defaults to false
            upArrow: 'wickedpicker__controls__control-up',  //The up arrow class selector to use, for custom CSS
            downArrow: 'wickedpicker__controls__control-down', //The down arrow class selector to use, for custom CSS
            close: 'wickedpicker__close', //The close class selector to use, for custom CSS
            hoverState: 'hover-state', //The hover state class to use, for custom CSS
            showSeconds: false, //Whether or not to show seconds,
            secondsInterval: 1, //Change interval for seconds, defaults to 1,
            minutesInterval: 5, //Change interval for minutes, defaults to 1
            beforeShow: null, //A function to be called before the Wickedpicker is shown
            show: function(){
                var modal = $('input[name="scheduledEndTime"]').closest('#addEvent');
                var datePicker = $('body').find('.wickedpicker');
                if(!modal.length) {
                    $(datePicker).css('z-index', 'auto');
                    return;
                }
                var zIndexModal = $(modal).css('z-index');
                $(datePicker).css('z-index', zIndexModal + 1);
            }, //A function to be called when the Wickedpicker is shown
            clearable: false, //Make the picker's input clearable (has clickable "x")
        };

        if (is_idevice) {
            $('input[name="scheduledStartTime"]').val(startTime);
            $('input[name="scheduledEndTime"]').val(endTime);
        } else {

            $('#scheduledStartTime').wickedpicker(StartTimeOptions);
            $('#scheduledStartTime').val(startTimeAMPM);
            $('#scheduledEndTime').wickedpicker(EndTimeOptions);
            $('input[name="scheduledEndTime"]').val(endTimeAMPM);
        }

        $('input[name="scheduledStartDate"]').val(startDate);
        $('input[name="scheduledEndDate"]').val(endDate);

        setAllDayFlag(event.isAllDay === 1);

        //salesperson info
        selectSalesperson.find("option[value='" + event.resourceId + "']").prop('selected', true);

        eventModal.find('input[name="notifyCustomer"]').prop('checked', event.sendNotifications === 1);

        if (new Date($('input[name="scheduledStartDate"]').val()) < today) {
            if (eventModal.find('.callout.small.alert').length < 1){
                eventModal.append('<div class="callout small alert"><p>Please note that one of the selected dates is in the past.</p></div>');
            } else {
                eventModal.find('.callout.small.alert p').text('Please note that one of the selected dates is in the past.');
            }
        } else {
            eventModal.find('.callout.small.alert').remove();
        }

        eventModal.foundation('open');
        eventModal.find('textarea[name="scheduleDescription"]').val(state.reschedule_description);
    }

    //Display Calendar
    var filterCalendar = function (defaultView) {

        var calendar = $('#calendar');
        var calendarDelay = null;

        //$('#scheduleModalDD select > option').not(':first').remove();
        var date = state.current_date;

        calendar.fullCalendar('destroy');
        calendar.fullCalendar('render');

        calendar.fullCalendar({
            header: {
                left: '',
                center: '',
                right: ''
            },
            views: {
                resourceWeek: {
                    type: 'timelineDay',
                    duration: { weeks: 1 },
                    slotDuration: '24:00:00',
                    slotLabelFormat: 'ddd M/D'
                },
                timelineDay: {
                    slotDuration: '00:15:00',
                    slotLabelFormat: 'hA'
                }
            },
            slotDuration: '00:15:00',
            slotLabelFormat: 'hA',
            scrollTime: '08:00:00',
            defaultView: defaultView,
            aspectRatio: 1.8,
            defaultDate: date,
            fixedWeekCount: false,
            selectable: true,
            selectHelper: false,
            unselectAuto: false,
            editable: true,
            eventLimit: true,
            eventOverlap: true,
            eventColor: '#f6f7fb',
            businessHours: window.project_data.company.business_hours,
            resourceLabelText: ' ',
            resources: (callback) => {
                $.ajax({
                    url: window.fx_url.API + 'calendar/resources',
                    dataType: 'json',
                    data: {
                        filter: getFilterUsers()
                    },
                    success: function (response) {
                        callback(response.result);
                        $('#scheduleModalDD select').empty();
                        $.each(response.result, function (i, item) {
                            if (item.id != '0') {
                                users[item.id] = item;
                            }
                        });
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                        console.log(jqXHR.responseText);
                    }
                });
            },
            dayClick: function (date, jsEvent, view) {
                var date = date.format();
                var coordinates = jsEvent.pageX + ',' + jsEvent.pageY;
                var view = view.name;

                if (view != 'timelineDay') {
                    $('#resourceWeekView, #monthlyView').removeClass('active');
                    $('#dailyView').addClass('active');
                    calendar.fullCalendar('changeView', 'timelineDay');
                    calendar.fullCalendar('gotoDate', date);
                }
            },
            selectAllow: function(selectInfo) {
                if (selectInfo.resourceId) {

                    if (selectInfo.resourceId == 0) {
                        return false;
                    } else {
                        var newResource = $('#calendar').fullCalendar('getResourceById', selectInfo.resourceId);
                        var allowDrop = false;
                        var eventType = $('#eventType').val();

                        for (var i in roles) {
                            var role = roles[i];

                            if (newResource[i] == 1 && $.inArray(eventType, role.eventTypes) !== -1) {
                                allowDrop = true;
                                break;
                            }
                        }

                        return allowDrop;
                    }
                }
            },
            eventAllow: function(dropLocation, draggedEvent) {
                if (dropLocation.resourceId == null){
                    dropLocation.resourceId = draggedEvent.resourceId;
                }
                if (dropLocation.resourceId == 0){
                    return false;
                } else {
                    var newResource = $('#calendar').fullCalendar('getResourceById', dropLocation.resourceId);
                    var allowDrop = false;
                    var eventType = $('#eventType').val();

                    for (var i in roles) {
                        var role = roles[i];

                        if (newResource[i] == 1 && $.inArray(eventType, role.eventTypes) !== -1){
                            allowDrop = true;
                            break;
                        }
                    }

                    return allowDrop;
                }
            },
            eventDrop: moveEvent,
            eventResize: moveEvent,
            select: function (start, end, jsEvent, view, resource) {
                var view = view.name;
                var eventModal = $('#addEvent');

                if (view == 'timelineDay') {

                    //Render Event
                    var eventData;
                    var firstName = $('#firstName').text();
                    var lastName = $('#lastName').text();
                    var businessName = $('#businessName').text();
                    var eventType = $('#eventType').val();
                    var titleDisplay = firstName + ' ' + lastName + ' Appointment';
                    if (businessName != ''){
                        titleDisplay = businessName + ' Appointment';
                    } else {
                        businessName = null;
                    }
                    var newResource = $('#calendar').fullCalendar('getResourceById', resource.id);
                    var calendarBgColor = newResource.calendarBgColor;
                    var description = null;

                    var projectScheduleID = eventModal.find('input[name="projectScheduleID"]').val();

                    if (projectScheduleID != '') {
                        description = $('#schedule #Schedule'+projectScheduleID+' .description').text();
                        description = description.substring(13);

                    }

                    eventData = {
                        title: titleDisplay,
                        firstName: firstName,
                        lastName: lastName,
                        businessName: businessName,
                        start: start,
                        end: end,
                        resourceId: resource.id,
                        scheduledUserID: resource.id,
                        address: $('#address').text(),
                        city: $('#city').text(),
                        state: $('#state').text(),
                        zip: $('#zip').text(),
                        calendarBgColor: calendarBgColor,
                        scheduleType: eventType,
                        editable: true,
                        description: description,
                        isAllDay: 0,
                        sendNotifications: 1
                    };

                    calendar.fullCalendar('renderEvent', eventData, true);

                    calendar.fullCalendar('refetchEvents');
                    calendar.fullCalendar('rerenderEvents');

                    //Get Temporary Event ID
                    var clientEvents = calendar.fullCalendar('clientEvents');
                    var lastEvent = clientEvents[clientEvents.length - 1];

                    var lastId = lastEvent._id;
                    eventModal.find('input[name="tempID"]').val(lastId);

                    eventModal.find('.button.drag').hide();
                    eventModal.find('.button.select').show();

                    moveEvent(lastEvent);

                }
            },
            events: function (start, end, timezone, callback) {
                var start = moment(start).format("YYYY/MM/DD");
                var end = moment(end).format("YYYY/MM/DD");

                if (calendarDelay !== null) {
                    clearTimeout(calendarDelay);
                }

                calendarDelay = setTimeout(function(){
                    calendarDelay = null;
                    getEvents(start, end, timezone, callback);
                }, 800);
            },
            eventRender: function (event, element) {

                var rescheduleEventID = $('input[name="projectScheduleID"]').val();
                if (event.id != rescheduleEventID){
                    event.editable = false;
                }

                if (event.eventType == 3) {
                    $('select#filterResources').find('option[value="0"]').show();
                    $('tr[data-resource-id="0"] .fc-widget-content').show();
                }

                var fullUserColor = $('#fullUserColor').val();
                var eventToReschedule = $('input[name="projectScheduleID"]').val();

                //Decode customer name on calendar
                var name = (element.find('.fc-title').text());
                if (name != '') { element.find('.fc-title').text(name); }

                if (event.rendering == 'background') {
                    element.append(event.title);
                }

                if (event.start == null || event.end == null) return;
                event.end.local();
                event.start.local();

                var view = calendar.fullCalendar('getView');
                var eventAddressDisplay2 = '';
                if (event.address2 != null && event.address2 != ""){
                    eventAddressDisplay2 = ' ' + event.address2;
                }

                if (view.name === 'timelineDay') {
                    if (fullUserColor == '1') {
                        element.find('.fc-content').parent().css({"background-color": event.calendarBgColor, "border-top-color": event.calendarBgColor, "border-bottom-color": '#ffffff'});
                        element.find('.fc-title, span.fc-time').css('color', event.calendarTextColor);
                    } else {
                        element.find('.fc-title').prepend('<div style="width: .5rem;background-color:' + event.calendarBgColor + ';height: 4.2rem;float: left;margin: .2rem 0 0 3px;"></div>');
                    }
                    if (event.id == eventToReschedule) {
                        element.find('.fc-title').parents("a").css("background-color","#CDE9FA");
                        element.find('.fc-title, span.fc-time').css('color', '#000000');
                    }

                    if (event.eventType == 1) {
                        if (parseFloat(event.latitude) === 0 && parseFloat(event.longitude) === 0){
                            if (event.scheduleType == 'Installation'){
                                element.find('.fc-title').append('<br/><span class="fc-title-type">'+ event.projectDescription +'</span><br/><span class="fc-title-type">Installation (Location Not Mapped): ' + $.datepicker.formatDate("MM dd, yy", event.start._d) + ' - ' + (event.end.format('hh:mm:ss a') == '12:00:00 am' ? $.datepicker.formatDate("MM dd, yy", event.end.add(-1, 'second')._d) : $.datepicker.formatDate("MM dd, yy", event.end._d)) + '</span>');
                            }

                            if (event.scheduleType == 'Evaluation'){
                                element.find('.fc-title').append('<br/><span class="fc-title-type">'+ event.projectDescription +'</span><br/><span class="fc-title-type">Evaluation (Location Not Mapped)</span>');
                            }
                            element.find('.fc-title').append('<br/><span class="fc-title-address">' + event.address + eventAddressDisplay2 + ", " + event.city + ", " + event.state + ' ' + event.zip + '</span>');
                        }
                        else{
                            if (event.scheduleType == 'Installation') { element.find('.fc-title').append('<br/><span class="fc-title-type">'+ event.projectDescription +'</span><br/><span class="fc-title-type">Installation: ' + $.datepicker.formatDate("MM dd, yy", event.start._d) + ' - ' + (event.end.format('hh:mm:ss a') == '12:00:00 am' ? $.datepicker.formatDate("MM dd, yy", event.end.add(-1, 'second')._d) : $.datepicker.formatDate("MM dd, yy", event.end._d)) + '</span>'); }
                            if (event.scheduleType == 'Evaluation') { element.find('.fc-title').append('<br/><span class="fc-title-type">'+ event.projectDescription +'</span><br/><span class="fc-title-type">Evaluation</span>'); }

                            element.find('.fc-title').append('<br/><span class="fc-title-address">' + event.address + eventAddressDisplay2 + ", " + event.city + ", " + event.state + ' ' + event.zip + '</span>');
                        }
                    } else if (event.eventType == 2 || event.eventType == 3) {
                        element.find('.fc-title').append('<br/><span class="fc-title-type">'+ event.scheduleType +'</span>');
                    }
                }


                else if (view.name === 'month') {
                    element.find('span.fc-time').prependTo(element.find('.fc-content .fc-title'));

                    if (fullUserColor == '1') {
                        element.find('.fc-content').parent().css({"background-color": event.calendarBgColor, "border-top-color": event.calendarBgColor, "border-bottom-color": '#ffffff'});
                        element.find('.fc-title, span.fc-time').css('color', event.calendarTextColor);
                    } else {
                        element.find('.fc-title').prepend('<div style="width: .5rem;background-color:' + event.calendarBgColor + ';height: 1.9rem;float: left;margin: .2rem .3rem 0 3px;"></div>');
                    }
                    if (event.id == eventToReschedule) {
                        element.find('.fc-title').parents("a").css("background-color","#CDE9FA");
                        element.find('.fc-title, span.fc-time').css('color', '#000000');
                    }

                    if (event.eventType == 1) {
                        if (event.allDay == '1') {
                            element.find('.fc-title').append('<span class="fc-title-address"> ' + event.address + ' ' + eventAddressDisplay2 + ", " + event.city + ", " + event.state + ' ' + event.zip + '</span>');
                        }
                        if (event.scheduleType == 'Installation') { element.find('.fc-title').append('<br/><span class="fc-title-type">I:' + ' ' + event.projectDescription +'</span>'); }
                        if (event.scheduleType == 'Evaluation') { element.find('.fc-title').append('<br/><span class="fc-title-type">E:' + ' ' + event.projectDescription +'</span>'); }

                    } else if (event.eventType == 2 || event.eventType == 3) {
                        element.find('.fc-title').append('<br/><span class="fc-title-type">'+ event.scheduleType +'</span>');
                    }
                }

                else if (view.name === 'resourceWeek') {

                    element.find('.fc-title').empty();

                    if (element.find('.fc-content span.fc-time').length != 1){
                        element.find('.fc-content .fc-title').prepend('<span class="fc-time"></span>');
                    } else {
                        element.find('.fc-time').remove();
                        element.find('.fc-content .fc-title').prepend('<span class="fc-time"></span>');
                    }

                    if (fullUserColor == '1') {
                        element.find('.fc-content').parent().css({"background-color": event.calendarBgColor, "border-top-color": event.calendarBgColor, "border-bottom-color": '#ffffff'});
                        element.find('.fc-title, span.fc-time').css('color', event.calendarTextColor);
                    } else {
                        element.find('.fc-title').prepend('<div style="width: .5rem;background-color:' + event.calendarBgColor + ';height: 1.9rem;float: left;margin: .2rem 0 0 3px;"></div>');
                    }
                    if (event.id == eventToReschedule) {
                        element.find('.fc-title').parents("a").css("background-color","#CDE9FA");
                        element.find('.fc-title, span.fc-time').css('color', '#000000');
                    }
                    element.find('.fc-content').parent().addClass('resource-week');

                    if (event.eventType == 1) {
                        element.find('.fc-title').append('<span class="fc-title-address">' + event.city + ", " + event.state + '</span>');

                        if (event.scheduleType == 'Installation') {

                            element.find('.fc-title').append('<br/><span class="fc-title-type">I: '+ event.projectDescription +'</span>');

                            if (moment(event.start._d).format('h:mm') == '12:00' && (moment(event.end._d).format('h:mm') == '12:00' || moment(event.end._d).format('h:mm') == '11:59')) {
                                element.find('span.fc-time').html('All Day');
                            } else {
                                element.find('span.fc-time').html(moment(event.start._d).format('h:mm') + ' - ' + moment(event.end._d).format('h:mm'));
                            }
                        } else if  (event.scheduleType == 'Evaluation') {
                            element.find('span.fc-time').html(moment(event.start._d).format('h:mm') + ' - ' + moment(event.end._d).format('h:mm'));
                            element.find('.fc-title').append('<br/><span class="fc-title-type">E: '+ event.projectDescription +'</span>');
                        }

                    } else if (event.eventType == 2 || event.eventType == 3) {
                        element.find('span.fc-time').html(moment(event.start._d).format('h:mm') + ' - ' + moment(event.end._d).format('h:mm'));
                        element.find('.fc-title').append('<br/><span class="fc-title-type">'+ event.scheduleType +'</span>');
                    }
                }

            },
            eventAfterAllRender: function (view) {
                if ($('.fc-toolbar').length > 0) {
                    if (view.name === 'timelineDay') {
                        if (evalMap === null) {
                            $('.calendarWrapper').before('<div id="calendarMap"></div>');
                        }
                        $('#calendarMap').show();
                        initializeMap();
                    }
                    else {
                        $('#calendarMap').hide();
                    }
                }

                var containerHeight = $(window).height() - $('.footer').height() - $('.top-bar').height();
                var dashboardHeight = $('#calendarMap').height() + $('.first-row').height() + $('.progress-background').height();

                var calendarGroupHeight = $('#calendar').outerHeight() + $('.dashboard-filter-bar').outerHeight();
                var filterGroup = $('#filterGroup');
                var filterGroupHeader = $('#filterGroup .header');
                var filterGroupList = $('#filterGroup .list');

                if ((containerHeight - dashboardHeight) < calendarGroupHeight){
                    var height = calendarGroupHeight - 3;
                    filterGroup.height(height);
                    filterGroupList.height(height - filterGroupHeader.height());
                } else {
                    var height = containerHeight - dashboardHeight;
                    filterGroup.height(height);
                    filterGroupList.height(height - filterGroupHeader.height());
                }
            },
            viewRender: function (view, element) {
                var moment = calendar.fullCalendar('getDate');
                var momentFormat = moment.format();

                state.current_date = momentFormat;


                if (view.name === 'timelineDay') {
                    var momentFormat = moment.format('dddd MMMM D, YYYY');
                    $('#calendarTitle').text(momentFormat);

                    $('.fc-expander-space').remove();

                    //$(element).height(auto);
                    calendar.fullCalendar('option', 'contentHeight', 'auto');


                } else if (view.name === 'agendaWeek' || view.name === 'resourceWeek') {
                    //var momentFormat = moment.format('MMMM D');
                    var viewStart = view.intervalStart.format('MMMM D');
                    var viewEnd = view.intervalEnd.format('MMMM D YYYY');

                    var newdate = new Date(viewEnd);
                    newdate.setDate(newdate.getDate() - 1); // minus the date
                    var viewEndNew = new Date(newdate);
                    var viewEndNewFormat = $.datepicker.formatDate("MM d", viewEndNew);

                    $('#calendarTitle').text(viewStart + ' - ' + viewEndNewFormat);

                    calendar.fullCalendar('option', 'contentHeight', '');
                    $('#calendarMap').hide();
                } else if (view.name === 'month') {
                    var momentFormat = moment.format('MMMM YYYY');
                    $('#calendarTitle').text(momentFormat);

                    calendar.fullCalendar('option', 'contentHeight', '');
                    $('#calendarMap').hide();
                }
            },
            resourceRender: function (resourceObj, labelTds, bodyTds) {
                var role = resourceObj.installation == "1" ? "Installation" : "";
                if (resourceObj.sales == "1") {
                    if (role.length > 0) role += ", ";
                    role += "Sales";
                }
                labelTds.find('.fc-cell-content').append($('<span class="fc-cell-role"></span>').text(role));

                calendar.find('tr[data-resource-id="' + resourceObj.id + '"] .fc-cell-content').before('<div style="width: .5rem;background-color:' + resourceObj.calendarBgColor + ';height: 4.2rem;float: left;margin-left: 3px;margin-top: 0.4rem;"></div>');

                //Decode name on calendar
                var title = calendar.find('tr[data-resource-id="' + resourceObj.id + '"] .fc-cell-content .fc-cell-text').text();
                calendar.find('tr[data-resource-id="' + resourceObj.id + '"] .fc-cell-content .fc-cell-text').text(title);

                if (resourceObj.id == 0) {
                    labelTds.find('.fc-cell-content .fc-cell-role').text('Company Calendar');
                    //labelTds.hide();
                    //bodyTds.hide();
                }
            }
        });

    };

//Get Events in Current View
    var getEvents = (start, end, timezone, callback) => {
        elem.loader.show();
        $.ajax({
            url: window.fx_url.BASE + 'getEvents.php',
            dataType: 'json',
            data: {
                filter: getFilterUsers(),
                start: start, //moment(start).format("YYYY/MM/DD"),
                end: end//moment(end).format("YYYY/MM/DD")
            },
            success: function (doc) {
                elem.loader.hide();
                if ($('.fc-toolbar').length > 0) {
                    var events = eval(doc);
                    callback(events);
                }
                else {
                    var newEvents = [];
                    $.each(doc, function(i, item) {
                        if (item.latitude != "0.00000000" && item.longitude != "0.00000000"){
                            newEvents.push(item);
                        }
                    });
                    setMapLocations(newEvents, start, end);
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }
        });
    };

    var completeInstallation = function (projectScheduleID, completed) {
        elem.loader.show();
        var endpoint = null;
        var type = null;
        if (completed) {
            endpoint = window.fx_url.API + 'project/event/complete';
            type = 'PUT';
        } else {
            endpoint = window.fx_url.API + 'project/event';
            type = 'DELETE';
        }
        $.ajax({
            url: endpoint,
            dataType: "json",
            type: type,
            contentType: "application/x-www-form-urlencoded",
            data: {
                projectScheduleID: projectScheduleID
            },
            success: function (response) {
                elem.loader.hide();

                //Update Schedule
                $('#scheduleEvaluationButton').empty();
                $('#evaluationSchedule').empty();
                $('#scheduleInstallationButton').empty();
                $('#installationSchedule').empty();
                $('input[name="projectScheduleID"]').val('');
                projectSchedule();
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }
        });
    };

    //Display Project Schedule
    function projectSchedule(config) {
        Object.assign(state, config);

        $('#evaluationSchedule').empty();
        $('#installationSchedule').empty();
        $('#scheduleEvaluationButton').empty();
        $('#scheduleInstallationButton').empty();

        $.ajax({
            url: window.fx_url.BASE + 'getProjectEvents.php',
            dataType: "json",
            type: "GET",
            contentType: "application/x-www-form-urlencoded",
            data: {
                projectID: state.id
            },
            success: function (response) {
                //Response is NOT Null
                if (!response == '') {

                    var isEvaluationDate = false;
                    var isInstallationDate = false;

                    $.each(response, function (i, item) {
                        var description = '';
                        var raw_description = '';
                        if (item.description != null) {
                            description = htmlEncode(item.description);
                            description = '<br/><span class="description" style="font-size:.7rem;">Description: ' + description + '</span><br/><br/>';
                            raw_description = item.description !== null ? item.description : '';
                        }
                        var notified = '';
                        if (item.sendNotifications === 1) {
                            notified = '<span style="font-size:.7rem;font-style: normal;">(Customer Notified of Appointment)</span><br/>';
                        }

                        var status = '';
                        if (item.isPending == 1) {
                            var publishedTime = moment(item.pendingAt).add(window.project_data.pending_delay, 'minutes').format('MMMM Do, YYYY [at] h:mm a');
                            status = 'Pending <span data-tooltip aria-haspopup="true" class="has-tip" data-disable-hover="false" tabindex="6" title="Appointment will be published ' + publishedTime + ' if no other changes are made."><img src="' + window.fx_url.assets.IMAGE + 'icons/info.png" /></span>';
                        }
                        var statusText = '';
                        if (item.isCalendarPushInProgress == 1) {
                            statusText = 'Calendar Push in Progress';
                        } else if (item.isSyncInProgress == 1) {
                            statusText = 'Google Sync in Progress';
                        }

                        if (item.scheduleType == 'Evaluation') {
                            var evalRescheduleDisplay = '';
                            var evalCancelDisplay = '';
                            var syncGoogleDisplay = '';
                            var syncedWithGoogle = '';

                            if (window.project_data.user.primary == 1 || window.project_data.user.project_management == 1 || window.project_data.user.sales == 1) {
                                evalRescheduleDisplay = '<button class="button rescheduleEvaluation" style="float:right;margin-left:1rem;" href="' + item.projectScheduleID + '" data-description="' + raw_description + '">Reschedule</button>';
                                evalCancelDisplay = '<button class="button secondary cancelEventButton" style="float:right;" href="' + item.projectScheduleID + '">Cancel</button>';
                            }

                            if (window.project_data.google_calendar.project_event_sync_allowed && item.googleID == null && item.isPending != 1) {
                                var disabled = item.isCalendarPushInProgress == 1 || item.isSyncInProgress == 1 ? 'disabled' : '';
                                syncGoogleDisplay = '<button ' + disabled + ' class="button googleSyncEvent" style="float:right;margin-right:1rem;" href="' + item.projectScheduleID + '">Sync To Google</button>';
                            } else if (window.project_data.google_calendar.project_event_sync_allowed && item.googleID != null) {
                                syncedWithGoogle = '<img alt="Event is Synced with Google" title="Event is Synced with Google" src="' + window.fx_url.assets.IMAGE + 'icons/google-sync.png" style="height: 21px;padding-left: .5rem;">';
                            }

                            if (item.scheduleType == 'Evaluation' && (item.cancelledDate != null || item.replacedAt != null)) {
                                var statusUpdate = '';
                                if (item.cancelledDate != null) {
                                    statusUpdate = 'Cancelled by: ' + item.cancelledFirstName + ' ' + item.cancelledLastName + ' on ' + item.cancelledDate;
                                    isEvaluationDate = false;
                                } else if (item.replacedAt != null) {
                                    statusUpdate = 'Replaced by: ' + item.replacedByFirstName + ' ' + item.replacedByLastName + ' on ' + item.replacedAt;
                                }
                                $('#evaluationSchedule').append('<div class="small-12 medium-12 columns no-pad"><div class="callout primary" id="Schedule' + this.projectScheduleID + '" data-id="' + this.projectScheduleID + '"><div class="row expanded"><div class="small-12 medium-12"><p class="no-margin">' + item.scheduledStart + ' - ' + item.scheduledEnd + ' with ' + item.scheduledFirstName + ' ' + item.scheduledLastName + '</p></div></div><div class="row expanded"><div class="small-12"><p class="no-margin" style="line-height:1;">' + notified + '<br><span style="font-size:.7rem;">Scheduled by: ' + (item.scheduledByFirstName === null && item.scheduledByLastName === null ? 'System' : item.scheduledByFirstName + ' ' + item.scheduledByLastName) + ' on ' + item.scheduledOn + '</span><br/><span style="font-size:.7rem;">' + statusUpdate + '</span>' + description + '</p></div></div></div></div>');
                            } else if (item.scheduleType == 'Evaluation' && item.cancelledDate == null) {
                                $('#evaluationSchedule').append('<div class="small-12 medium-12 columns no-pad"><div class="callout primary" id="Schedule' + this.projectScheduleID + '" data-id="' + this.projectScheduleID + '"><div class="row expanded"><div class="small-12 medium-10"><p class="no-margin">' + item.scheduledStart + ' - ' + item.scheduledEnd + ' with ' + item.scheduledFirstName + ' ' + item.scheduledLastName + ' <a class="editEvent"><small><i>Edit</i></small></a>' + syncedWithGoogle + '</p></div><div class="small-12 medium-2"><p class="no-margin text-right"><span>' + status + statusText + '</span></p></div></div><div class="row expanded"><div class="small-12"><p style="line-height:1;">' + notified + '<br><span style="font-size:.7rem;">Scheduled by: ' + (item.scheduledByFirstName === null && item.scheduledByLastName === null ? 'System' : item.scheduledByFirstName + ' ' + item.scheduledByLastName) + ' on ' + item.scheduledOn + '</span>' + description + '</p><div>' + evalRescheduleDisplay + '' + evalCancelDisplay + '' + syncGoogleDisplay + '</div></div></div></div></div>');
                                isEvaluationDate = true;
                                $('#scheduleEvaluationButton').html('');
                            }
                        }

                        if (item.scheduleType == 'Installation') {

                            var installRescheduleDisplay = '';
                            var installCancelDisplay = '';
                            var installCompleteDisplay = '';
                            var syncGoogleDisplay = '';
                            var syncedWithGoogle = '';

                            if (window.project_data.user.primary == 1 || window.project_data.user.project_management == 1 || window.project_data.user.installation == 1) {
                                installRescheduleDisplay = '<button class="button rescheduleInstallation" style="float:right;margin-left:1rem;" href="' + item.projectScheduleID + '" data-description="' + raw_description + '">Reschedule</button>';
                                installCancelDisplay = '<button class="button secondary cancelEventButton" style="float:right;" href="' + item.projectScheduleID + '">Cancel</button>';
                                installCompleteDisplay = '<button class="button installationComplete" style="float:right;margin-left:1rem;" href="' + item.projectScheduleID + '">Complete</button>';
                            }

                            if (window.project_data.google_calendar.project_event_sync_allowed && item.googleID == null && item.isPending != 1) {
                                var disabled = item.isCalendarPushInProgress == 1 || item.isSyncInProgress == 1 ? 'disabled' : '';
                                syncGoogleDisplay = '<button ' + disabled + ' class="button googleSyncEvent" style="float:right;margin-right:1rem;" href="' + item.projectScheduleID + '">Sync To Google</button>';
                            } else if (window.project_data.google_calendar.project_event_sync_allowed && item.googleID != null) {
                                syncedWithGoogle = '<img alt="Event is Synced with Google" title="Event is Synced with Google" src="' + window.fx_url.assets.IMAGE + 'icons/google-sync.png" style="height: 21px;padding-left: .5rem;">';
                            }

                            if (item.scheduleType == 'Installation' && (item.cancelledDate != null || item.replacedAt != null)) {
                                var statusUpdate = '';
                                if (item.cancelledDate != null) {
                                    statusUpdate = 'Cancelled by: ' + item.cancelledFirstName + ' ' + item.cancelledLastName + ' on ' + item.cancelledDate;
                                    isInstallationDate = false;
                                } else if (item.replacedAt != null) {
                                    statusUpdate = 'Replaced by: ' + item.replacedByFirstName + ' ' + item.replacedByLastName + ' on ' + item.replacedAt;
                                }
                                $('#installationSchedule').append('<div class="small-12 medium-12 columns no-pad"><div class="callout primary" id="Schedule' + this.projectScheduleID + '" data-id="' + this.projectScheduleID + '"><div class="row expanded"><div class="small-12 medium-12"><p class="no-margin">' + item.scheduledStart + ' - ' + item.scheduledEnd + ' with ' + item.scheduledFirstName + ' ' + item.scheduledLastName + '</p></div></div><div class="row expanded"><div class="small-12"><p class="no-margin" style="line-height:1;">' + notified +
                                    '<br><span style="font-size:.7rem;">Scheduled by: ' + (item.scheduledByFirstName === null && item.scheduledByLastName === null ? 'System' : item.scheduledByFirstName + ' ' + item.scheduledByLastName) + ' on ' + item.scheduledOn + '</span><br/><span style="font-size:.7rem;">' + statusUpdate + '</span>' + description + '</p></div></div></div></div>');
                            } else if (item.scheduleType == 'Installation' && item.cancelledDate == null) {
                                if (item.installationComplete == null) {
                                    $('#installationSchedule').append('<div class="small-12 medium-12 columns no-pad"><div class="callout primary" id="Schedule' + this.projectScheduleID + '" data-id="' + this.projectScheduleID + '"><div class="row expanded"><div class="small-12 medium-10"><p class="no-margin">' + item.scheduledStart + ' - ' + item.scheduledEnd + ' with ' + item.scheduledFirstName + ' ' + item.scheduledLastName + ' <a class="editEvent"><small><i>Edit</i></small></a>' + syncedWithGoogle + '</p></div><div class="small-12 medium-2"><p class="no-margin text-right"><span>' + status + statusText + '</span></p></div></div><div class="row expanded"><div class="small-12"><p style="line-height:1;">' + notified + '<br><span style="font-size:.7rem;">Scheduled by: ' + (item.scheduledByFirstName === null && item.scheduledByLastName === null ? 'System' : item.scheduledByFirstName + ' ' + item.scheduledByLastName) + ' on ' + item.scheduledOn + '</span>' + description + '</p><div>' + installRescheduleDisplay + '' + installCompleteDisplay + '' + installCancelDisplay + '' + syncGoogleDisplay + '</div></div></div></div>');
                                } else {
                                    installCompleteDisplay = '<button class="button undoInstallationComplete" style="float:right;margin-left:1rem;" href="' + item.projectScheduleID + '">Undo Complete</button>';

                                    $('#installationSchedule').append('<div class="small-12 medium-12 columns no-pad"><div class="callout primary" id="Schedule' + this.projectScheduleID + '" data-id="' + this.projectScheduleID + '"><div class="row expanded"><div class="small-12 medium-12"><p class="no-margin">' + item.scheduledStart + ' - ' + item.scheduledEnd + ' with ' + item.scheduledFirstName + ' ' + item.scheduledLastName + '' + syncedWithGoogle + '</p></div></div><div class="row expanded"><div class="small-12"><p class="no-margin" style="line-height:1;">' + notified + '<br><span style="font-size:.7rem;">Scheduled by: ' + (item.scheduledByFirstName === null && item.scheduledByLastName === null ? 'System' : item.scheduledByFirstName + ' ' + item.scheduledByLastName) + ' on ' + item.scheduledOn + '</span></p><p style="line-height:1;"><span style="font-size:.7rem;">Completed by: ' + item.completedFirstName + ' ' + item.completedLastName + ' on ' + item.installationCompleteRecordedDT + '</span>' + description + '</p><div>' + installCompleteDisplay + '' + syncGoogleDisplay + '</div></div></div></div>');
                                }
                                isInstallationDate = true;
                            }
                        }

                        if (item.isPending == 1) {
                            $('#Schedule' + this.projectScheduleID + '.callout.primary').find('[data-tooltip]').foundation();
                        }
                    });

                    if (window.project_data.user.primary == 1 || window.project_data.user.project_management == 1 || window.project_data.user.sales == 1 || window.project_data.user.installation == 1) {
                        $('#scheduleEvaluationButton').append('<button class="button scheduleEvaluation" style="float:right;margin-bottom:.5rem;">Schedule</button>');
                        $('#scheduleInstallationButton').append('<button class="button scheduleInstallation" style="float:right;margin-top:.5rem;">Schedule</button>');
                    }

                    //Response is Null
                } else {

                    if (window.project_data.user.primary == 1 || window.project_data.user.project_management == 1 || window.project_data.user.sales == 1) {
                        $('#scheduleEvaluationButton').append('<button class="button scheduleEvaluation" style="float:right;margin-bottom:.5rem;">Schedule</button>');
                    }

                    if (window.project_data.user.primary == 1 || window.project_data.user.project_management == 1) {
                        $('#scheduleInstallationButton').append('<button class="button scheduleInstallation" style="float:right;margin-top:.5rem;">Schedule</button>');
                    }

                }

                //Go to Schedule Tab
                $("#getSchedule").click(function () {
                    $('[data-tabs]').eq(0).foundation('selectTab', $('#schedule'));

                });

                //go to schedule tab from installation
                $('#installGetSchedule').click(function () {

                    elem.loader.show();

                    $('[data-tabs]').eq(0).foundation('selectTab', $('#schedule'));

                    elem.loader.hide();

                });

                //Select Evaluation from Filter and Load Calendar
                $('.scheduleEvaluation').click(function () {
                    $('#resourceWeekView, #dailyView').removeClass('active');
                    $('#monthlyView').addClass('active');
                    $('#scheduleList').hide();
                    $('#scheduleCalendar').show();
                    $('#cancelSchedule').append('<button id="cancelScheduleButton" class="button" style="float: right;">Back to Schedule</button>');
                    $('#eventType').val('Evaluation');

                    //Cancel Adding New Appointment/Installation
                    $('#cancelScheduleButton').click(function () {
                        $('input[name="projectScheduleID"]').val('');
                        $('textarea[name="scheduleDescription"]').val('');
                        $('#scheduleList').show();
                        $('#scheduleCalendar').hide();
                        $('#cancelScheduleButton').remove();
                        $('#calendar').empty();
                        state.reschedule_description = '';
                    });

                });
                $('.scheduleEvaluation').click(function () {
                    filterCalendar('month');
                });


                //Select Installation from Filter and Load Calendar
                $('.scheduleInstallation').click(function () {
                    $('#resourceWeekView, #dailyView').removeClass('active');
                    $('#monthlyView').addClass('active');
                    $('#scheduleList').hide();
                    $('#scheduleCalendar').show();
                    $('#cancelSchedule').append('<button id="cancelScheduleButton" class="button" style="float: right;">Back to Schedule</button>');
                    $('#eventType').val('Installation');

                    //Cancel Adding New Appointment/Installation
                    $('#cancelScheduleButton').click(function () {
                        $('input[name="projectScheduleID"]').val('');
                        $('textarea[name="scheduleDescription"]').val('');
                        $('#scheduleList').show();
                        $('#scheduleCalendar').hide();
                        $('#cancelScheduleButton').remove();
                        $('#calendar').empty();
                    });
                });
                $('.scheduleInstallation').click(function () {
                    filterCalendar('month');
                });

                $('.cancelEventButton').click(function () {
                    var idToCancel = $(this).attr('href');

                    $('#cancelEvent').find('[name="tempID"]').val(idToCancel);

                    //Open Cancel Event Modal
                    $('#cancelEvent').foundation('open');
                });


                //Reschedule Evaluation
                $('.rescheduleEvaluation').click(function () {
                    $('#resourceWeekView, #dailyView').removeClass('active');
                    $('#monthlyView').addClass('active');
                    var idToEdit = $(this).attr('href');
                    var description = $(this).attr('data-description');
                    state.reschedule_description = description;
                    $('input[name="projectScheduleID"]').val(idToEdit);

                    $('#scheduleList').hide();
                    $('#scheduleCalendar').show();
                    $('#cancelSchedule').append('<button id="cancelScheduleButton" class="button" style="float: right;">Back to Schedule</button>');
                    $('#eventType').val('Evaluation');

                    //Cancel Adding New Appointment/Installation
                    $('#cancelScheduleButton').click(function () {
                        $('input[name="projectScheduleID"]').val('');
                        $('textarea[name="scheduleDescription"]').val('');

                        $('#scheduleList').show();
                        $('#scheduleCalendar').hide();
                        $('#cancelScheduleButton').remove();
                        $('#calendar').empty();
                        state.reschedule_description = '';
                    });

                });
                $('.rescheduleEvaluation').click(function () {
                    filterCalendar('month');
                });

                //Reschedule Installation
                $('.rescheduleInstallation').click(function () {
                    $('#resourceWeekView, #dailyView').removeClass('active');
                    $('#montly').removeClass('active');

                    var idToEdit = $(this).attr('href');
                    var description = $(this).attr('data-description');
                    state.reschedule_description = description;
                    $('input[name="projectScheduleID"]').val(idToEdit);

                    $('#scheduleList').hide();
                    $('#scheduleCalendar').show();
                    $('#cancelSchedule').append('<button id="cancelScheduleButton" class="button" style="float: right;">Back to Schedule</button>');
                    $('#eventType').val('Installation');

                    //Cancel Adding New Appointment/Installation
                    $('#cancelScheduleButton').click(function () {
                        $('input[name="projectScheduleID"]').val('');
                        $('textarea[name="scheduleDescription"]').val('');

                        $('#scheduleList').css('display', '');
                        $('#scheduleCalendar').css('display', 'none');
                        $('#cancelScheduleButton').remove();
                        $('#calendar').empty();
                        state.reschedule_description = '';
                    });
                });
                $('.rescheduleInstallation').click(function () {
                    filterCalendar('month');
                });

                //Complete Installation
                $('.installationComplete').click(function () {
                    var idToEdit = $(this).attr('href');
                    completeInstallation(idToEdit, 1);
                });

                //Installation Not Complete
                $('.undoInstallationComplete').click(function () {
                    var idToEdit = $(this).attr('href');
                    completeInstallation(idToEdit, 0);
                });


                //Sync To Google
                $('.googleSyncEvent').click(function () {
                    var idToSync = $(this).attr('href');

                    $.ajax({
                        url: window.fx_url.API + 'project/event/calendar-push',
                        dataType: "json",
                        type: "PUT",
                        contentType: "application/x-www-form-urlencoded",
                        data: {
                            projectScheduleID: idToSync,
                            projectID: state.id
                        },
                        success: function (response) {
                            if (response.success) {
                                //Remove Calendar and Display Map
                                $('#scheduleEvaluationButton').empty();
                                $('#evaluationSchedule').empty();
                                $('#scheduleInstallationButton').empty();
                                $('#installationSchedule').empty();

                                //Get New Project Schedule
                                projectSchedule();

                            }
                        },
                        error: function (jqXHR, textStatus, errorThrown) {
                            console.log(textStatus, errorThrown);
                            console.log(jqXHR.responseText);
                        }
                    });

                });

                $('.editEvent').on('click.fx', function (e) {
                    var eventModal = $('#addEvent');
                    eventModal.find('.button.drag').hide();
                    eventModal.find('.button.select').show();
                    elem.loader.show();
                    $.ajax({
                        url: window.fx_url.BASE + 'getEventDetail.php',
                        dataType: "json",
                        type: "GET",
                        contentType: "application/x-www-form-urlencoded",
                        data: {
                            eventID: $(e.target).parents('.callout.primary').data('id'),
                            eventType: 1
                        },
                        success: function (response) {
                            editEvent(response);
                            elem.loader.hide();
                        }, error: function (jqXHR, textStatus, errorThrown) {
                            console.log(textStatus, errorThrown);
                            console.log(jqXHR.responseText);
                        }

                    });
                });

            }, error: function (jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }

        });
    }
    module.exports.projectSchedule = projectSchedule;

    function openFilter() {
        var width = '20%';
        if ($(window).width() < 640) {
            width = '50%';
        }
        $('#filterGroup').animate({ width: width }, { duration: 300, easing: "swing" });
        $('#filterGroup').addClass('show');
    }

    function closeFilter() {
        $('#filterGroup').animate({
            width: '0%'
        }, {
            duration: 300,
            easing: "swing",
            complete: function() {
                $(window).trigger('resize');
            }
        });
        $('#filterGroup').removeClass('show');
    }

    function getFilterUsers(){
        var filterUsers = [];

        var activeUsers = $("#sortable li[data-label='user'].active");

        var userSelection = '';
        if (activeUsers.length > 0) {
            userSelection = activeUsers;
        } else {
            userSelection = $("#sortable li[data-label='user']");
        }

        userSelection.each(function(index){
            filterUsers[index] = $(this).data('id');
        });
        return filterUsers;
    };

    function getUserSortOrder(){
        var allUsers = [];

        $("#sortable li[data-label='user']").each(function(index){
            allUsers[index] = $(this).data('id');
        });
        return allUsers;
    };
})(e$);
