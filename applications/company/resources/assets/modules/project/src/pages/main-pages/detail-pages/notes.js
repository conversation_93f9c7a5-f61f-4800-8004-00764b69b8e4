'use strict';

const Page = require('@ca-package/router/src/page');

const LegacyNotes = require('./legacy/notes');

const notes_tpl = require('@cam-project-tpl/pages/main-pages/detail-pages/notes.hbs');

class Notes extends Page {
    /**
     * Get child routes
     *
     * @readonly
     *
     * @returns {object}
     */
    static get routes() {
        return {
            add: {
                path: '/add',
                page: require('./notes-pages/add')
            }
        };
    };

    /**
     * Fetch data from server using legacy code
     *
     * @param {string} project_id
     */
    fetchData(project_id) {
        LegacyNotes.projectNotes({
            id: project_id
        });
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        this.elem.loader.show();

        await super.load(request, next);
        this.fetchData(request.params.project_id);
        this.elem.loader.hide();
    };

    /**
     * Refresh page
     *
     * @param {object} request
     */
    refresh(request) {
        this.fetchData(request.params.project_id);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.loader = this.elem.root.fxFind('loader');
        LegacyNotes.setup({
            loader: this.parent.elem.loader
        });
        e$(this.elem.root[0]).find('[data-reveal]').foundation();
        e$(this.elem.root[0]).find('[data-tooltip]').foundation();
    };

    /**
     * Render page
     */
    render() {
        return notes_tpl({
            info: window.fx_url.assets.IMAGE+'icons/info.png'
        });
    };
}

module.exports = Notes;
