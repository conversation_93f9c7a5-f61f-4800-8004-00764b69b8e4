'use strict';

const Page = require('@ca-package/router/src/page');

const main_tpl = require('@cam-project-tpl/pages/main.hbs');

class Main extends Page {
    /**
     * Get child routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            manager: {
                default: true,
                page: require('./main-pages/manager')
            },
            detail: {
                path: '/{project_id}',
                page: require('./main-pages/detail'),
                bindings: {
                    project_id: 'int'
                }
            }
        };
    };

    /**
     * Set layout
     *
     * @param {module:Layout.Controller} instance
     */
    set layout(instance) {
        this.state.layout = instance;
    };

    /**
     * Get layout
     *
     * @readonly
     *
     * @returns {module:Layout.Controller}
     */
    get layout() {
        return this.state.layout;
    };

    /**
     * Get container for child pages to be rendered
     *
     * @returns {jQuery}
     */
    getPageContainer() {
        return this.elem.pages;
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.pages = root.fxFind('pages');
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return main_tpl();
    };
}

module.exports = Main;
