<div class="c-sbcs-step">
    <div class="c-sbcss-header t-general">
        <div class="c-sbcssh-error" data-js="error"></div>
        <h4 class="c-sbcssh-title">General Info</h4>
        <p class="c-sbcssh-instruction">
            We can customize the software for your company by pulling in your logo and colors. Simply add your website,
            and click lookup.</p>
    </div>
    <form class="m-form t-general" data-js="form">
        <div class="c-f-row t-section t-ai-flex-end" data-js="url-wrapper">
            <div class="c-fr-field t-full t-url f-field">
                <label for="company-url" class="f-f-label">Company's Website Domain
                    <span class="f-fl-optional">(example: companysite.com)</span>
                </label>
                <input id="company-url" class="f-f-input" type="text" data-js="url" placeholder="-- Your Website URL Here --">
            </div>
            <button class="c-fr-field t-auto t-button-icon-text-primary" data-js="search-url">
                <svg data-icon><use xlink:href="#remix-icon--system--search-line"></use></svg>
            </button>
        </div>
        <div class="c-f-row t-logo-colors">
            <div class="c-fr-field t-half t-logo">
                <label class="c-frf-label" data-js="logo-title">Upload Your Company Logo</label>
                <p class="c-frf-instructions" data-js="logo-instructions">Please upload your company's Logo. The Logo
                    will appear in email communications as well as on the bid.
                </p>
                <div class="c-frf-logo-container">
                    <div class="c-frflc-placeholder" data-js="logo-placeholder"></div>
                    <div class="c-frflc-result-buttons" data-js="result-buttons">
                        <button class="c-frflcrb-clear" data-js="clear-results">Clear Results</button>
                        <button class="c-frflcrb-more" data-js="more-results">Load More</button>
                    </div>
                    <div class="c-frflc-options-placeholder" data-js="logo-options-placeholder"></div>
                    <button class="c-frflc-button" data-js="upload-logo">
                        <svg data-icon><use xlink:href="#remix-icon--system--upload-cloud-2-line"></use></svg>
                        <div data-text>Upload Logo</div>
                    </button>
                    <p class="c-frflc-details" data-js="logo-details"></p>
                    <div class="c-frflc-error" data-js="logo-error">Choose a logo or click <strong>Clear Results</strong></div>
                </div>
                <p class="c-frf-no-results" data-js="no-image-results">No image options found</p>
            </div>
            <div class="c-fr-field t-half t-colors">
                <label class="c-frf-label" data-js="color-title">Select Your Company Brand Colors</label>
                <p class="c-frf-instructions" data-js="color-instructions">To update the buttons and colors on
                    customer-facing communications, click inside the circle to edit your brand colors.
                </p>
                <div class="m-color-container">
                    <div class="c-cc-item" data-js="primary"></div>
                    <div class="c-cc-item" data-js="secondary"></div>
                </div>
                <p class="c-frf-no-results" data-js="no-color-results">No color options found</p>
            </div>
        </div>
        <div class="c-f-row">
            <div class="c-fr-field t-full f-field">
                <label for="email_from" class="f-f-label">From Email Address
                    <span data-tooltip data-type="info">You can send appointment reminders, bids, or other emails via
                        {{brand_name}}. While we facilitate the send, the emails come from your company. What address
                        should the emails come from? (example: <EMAIL>)
                    </span>
                </label>
                <input id="email_from" class="f-f-input" type="text" data-js="email_from">
            </div>
            <div class="c-fr-field t-full f-field">
                <label for="email_reply" class="f-f-label">Reply-To Email Address
                    <span data-tooltip data-type="info">Where would you like us to deliver customer email responses? (example: <EMAIL>)</span>
                </label>
                <input id="email_reply" class="f-f-input" type="text" data-js="email_reply">
            </div>
        </div>
        <div class="c-f-row">
            <div class="c-fr-field t-half f-field">
                <label for="website" class="f-f-label">Website Domain</label>
                <input id="website" class="f-f-input" type="text" data-js="website">
            </div>
        </div>
    </form>
</div>
