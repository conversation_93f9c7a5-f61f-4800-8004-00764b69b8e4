/**
 * @module SetupWizard
 */

'use strict';

import Router from '@ca-package/router';

import {MainPage} from './pages/main';

/**
 * Main controller for setup wizard module
 *
 * @memberof module:SetupWizard
 */
export class Controller {
    /**
     * Setup Wizard constructor
     *
     * @param {module:Layout.Controller} layout
     */
    constructor(layout) {
        this.elem = {};
        this.state = {layout};
        this.boot();
    };

    /**
     * Boot module
     */
    boot() {
        this.state.layout.setModeWindow();
        this.state.layout.setTitle('Setup Wizard');
        this.state.router = new Router(MainPage, {
            base_path: '/setup-wizard',
            main_route_callback: (instance) => {
                instance.layout = this.state.layout;
                return instance;
            }
        });
        this.state.router.boot(this.state.layout.elem.content);
    };
}
