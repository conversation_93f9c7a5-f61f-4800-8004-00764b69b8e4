'use strict';

import Page from '@ca-package/router/src/page';
import {findChild, jsSelector, onClickWatcher} from "@ca-package/dom";

import '@cac-js/handlebars-helpers/math';

import {General} from './step-pages/general';
import {Products} from './step-pages/products';
import {Users} from './step-pages/users';
import {TermsConditions} from './step-pages/terms_conditions';
import {QuickbooksOnline} from './step-pages/quickbooks_online';
import {GoogleCalendar} from './step-pages/google_calendar';
import {Media} from './step-pages/media';
import {Emails} from './step-pages/emails';
import {WarrantyPacket} from './step-pages/warranty_packet';
import {BidCustomization} from './step-pages/bid_customization';
import {AdditionalServices} from './step-pages/additional_services';

import step_tpl from '@cam-setup-wizard-tpl/pages/main-pages/step.hbs';

/**
 * @memberof module:SetupWizard/Pages/MainPages
 */
export class Step extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            current_step: null,
            back_route: '',
            next_route: '',
            skip_route: null,
            parent,
            steps: {
                general: {
                    label: 'General Info',
                    mobile_label: 'Step 1/11',
                    step_id: parent.steps.GENERAL
                },
                users: {
                    label: 'Users',
                    mobile_label: 'Step 2/11',
                    step_id: parent.steps.USERS
                },
                bid_customization: {
                    label: 'Bid Intro',
                    mobile_label: 'Step 3/11',
                    step_id: parent.steps.BID_CUSTOMIZATION
                },
                emails: {
                    label: 'Emails',
                    mobile_label: 'Step 4/11',
                    step_id: parent.steps.EMAILS
                },
                terms_conditions: {
                    label: 'Terms & Conditions',
                    mobile_label: 'Step 5/11',
                    step_id: parent.steps.TERMS_CONDITIONS
                },
                products: {
                    label: 'Products',
                    mobile_label: 'Step 6/11',
                    step_id: parent.steps.PRODUCTS
                },
                media: {
                    label: 'Media',
                    mobile_label: 'Step 7/11',
                    step_id: parent.steps.MEDIA
                },
                warranty_packet: {
                    label: 'Warranties',
                    mobile_label: 'Step 8/11',
                    step_id: parent.steps.WARRANTY_PACKET
                },
                quickbooks_online: {
                    label: 'QuickBooks Online',
                    mobile_label: 'Step 9/11',
                    step_id: parent.steps.QUICKBOOKS_ONLINE
                },
                google_calendar: {
                    label: 'Google Calendar',
                    mobile_label: 'Step 10/11',
                    step_id: parent.steps.GOOGLE_CALENDAR
                },
                additional_services: {
                    label: 'Additional Services',
                    mobile_label: 'Step 11/11',
                    step_id: parent.steps.ADDITIONAL_SERVICES
                }
            },
            outer_routes: {
                instruction: {
                    step_id: parent.steps.INSTRUCTION
                },
                review: {
                    step_id: parent.steps.REVIEW
                }
            },
            menu_order: ['general', 'users', 'bid_customization', 'emails', 'terms_conditions', 'products', 'media',
                'warranty_packet', 'quickbooks_online', 'google_calendar', 'additional_services']
        });
    };

    /**
     * Get steps in setup wizard
     *
     * @returns {object}
     */
    get steps() {
        return this.state.parent.steps;
    };

    /**
     * Get child routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            general: {
                path: '/general',
                page: General
            },
            products: {
                path: '/products',
                page: Products
            },
            users: {
                path: '/users',
                page: Users
            },
            terms_conditions: {
                path: '/terms-conditions',
                page: TermsConditions
            },
            quickbooks_online: {
                path: '/quickbooks-online',
                page: QuickbooksOnline
            },
            google_calendar: {
                path: '/google-calendar',
                page: GoogleCalendar
            },
            media: {
                path: '/media',
                page: Media
            },
            emails: {
                path: '/emails',
                page: Emails
            },
            warranty_packet: {
                path: '/warranties',
                page: WarrantyPacket
            },
            bid_customization: {
                path: '/bid-customization',
                page: BidCustomization
            },
            additional_services: {
                path: '/additional-services',
                page: AdditionalServices
            }
        };
    };

    /**
     * Update setup data
     *
     * @param {object} data
     */
    updateSetupData(data) {
        this.state.data = data;
    };

    /**
     * Set back route
     *
     * @param {string} route
     */
    setBackRoute(route) {
        this.state.back_route = route;
    };

    /**
     * Set next route
     *
     * @param {string|null} route
     */
    setNextRoute(route) {
        this.state.next_route = route;
    };

    /**
     * Set skip route
     *
     * @param {string|null} route
     */
    setSkipRoute(route) {
        this.state.skip_route = route;
    };

    /**
     * Set top navigation state and update side nav or mobile nav
     *
     * @param {boolean} unload
     */
    setNavState(unload = false) {
        if (!unload) {
            this.state.parent.setStepNavState({
                back: this.state.back_route,
                next: this.state.next_route,
                skip: this.state.skip_route
            });
        }
        // check off any completed steps
        for (let step in this.state.steps) {
            let this_step = this.state.steps[step],
                value = this.state.data[step];
            this_step.status = value;
            switch (value) {
                case null:
                    break;
                case true:
                    this_step.elem.removeClass('t-skipped');
                    this_step.elem.addClass('t-complete');
                    if (this.elem[`line_${step}`] !== undefined) {
                        this.elem[`line_${step}`].addClass('t-complete');
                    }
                    break;
                case false:
                    this_step.elem.addClass('t-skipped');
                    break;
            }
        }

        if (this.state.data.additional_services !== null) {
            this.elem.item_review.removeClass('t-hidden');
        }

        // stop code execution if route name matches one of the following
        let ignore_routes = [
            'step.google_calendar.add', 'step.google_calendar.remove', 'step.products.add'
        ];
        if (ignore_routes.includes(this.router.current_route.name)) {
            return;
        }

        // remove active step
        if (this.state.current_step !== null) {
            this.state.steps[this.state.current_step].elem.removeClass('t-active');
        }

        if (unload) {
            this.state.current_step = null;
            return;
        }

        // set active step
        let current_route = this.router.current_route.name.replace('step.', '');

        this.elem.mobile_title.text(this.state.steps[current_route].mobile_label);
        this.state.steps[current_route].elem.addClass('t-active');
        this.state.current_step = current_route;
    }

    /**
     * Change skip to next
     */
    changeNavState() {
        if (this.state.current_step === null) {
            return;
        }
        this.state.parent.setSkipToNext({
            next: this.state.next_route
        });
    };

    /**
     * Change next to skip in header
     */
    changeNextToSkip() {
        this.state.parent.setNextToSkip({
            skip: this.state.skip_route
        });
    };

    /**
     * Get parent header
     *
     * @returns {jQuery}
     */
    getMainHeader() {
        return this.state.parent.elem.header;
    };

    /**
     * Get container for child pages to be rendered
     *
     * @returns {jQuery}
     */
    getPageContainer() {
        return this.elem.pages;
    };

    /**
     * Handle route changed event
     *
     * Sets the correct navigation state
     */
    onRouteChange() {
        this.setNavState();
    };

    /**
     * Start doing work, show loader image
     */
    startWorking() {
        this.state.parent.startWorking();
    }

    /**
     * Stop doing work, hide loader image
     */
    resetWorking() {
        this.state.parent.resetWorking();
    }

    /**
     * Save current step for company
     *
     * @returns {Promise}
     */
    saveCompanyStep(step) {
        try {
            $.ajax({
                url: window.fx_url.API + `setup-wizard/company-step`,
                dataType: 'json',
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify({
                    step
                })
            });
        } catch (error) {
            alert('Unable to save step, please try again');
        }
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        await super.load(request, next);
        this.state.data = request.data;

        this.setNavState();
        this.routerSubscribe('route-changed', 'onRouteChange');
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        this.setNavState(true);
        await super.unload(request, next);
    };

    /**
     * Scroll root element back to top
     */
    scrollTopParent() {
        this.elem.root.scrollTop(0);
    }

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.pages = findChild(root, jsSelector('pages'));
        this.elem.steps = findChild(root, jsSelector('steps'));
        this.elem.locked = findChild(root, jsSelector('locked'));
        this.elem.item = findChild(root, jsSelector('item'));
        this.elem.lines = findChild(root, jsSelector('lines'));
        this.elem.mobile_title = findChild(root, jsSelector('step-title'));

        for (let i = 0; i < this.elem.item.length; i++) {
            let elem = $(this.elem.item[i]);
            this.elem[`item_${elem.data('id')}`] = elem;
        }

        for (let i = 0; i < this.elem.lines.length; i++) {
            let elem = $(this.elem.lines[i]);
            this.elem[`line_${elem.data('id')}`] = elem;
        }

        for (let item of this.state.menu_order) {
            this.state.steps[item].elem = this.elem[`item_${item}`];
        }

        let that = this;
        onClickWatcher(this.elem.steps, jsSelector('item'), function() {
            let $this = $(this),
                id = $this.data('id');
            let outer_routes = [
                'instruction', 'review'
            ];
            if (outer_routes.includes(id)) {
                that.router.redirect(id);
                that.saveCompanyStep(that.state.outer_routes[id].step_id);
                return;
            }

            if (that.state.current_step === id) {
                return;
            }
            if (that.state.steps[id].status === null) {
                return;
            }
            that.router.redirect(`step.${id}`);
            that.saveCompanyStep(that.state.steps[id].step_id);
        }, true);
    };

    /**
     * Render page
     */
    render() {
        let items = {};
        for (let item of this.state.menu_order) {
            items[item] = this.state.steps[item];
        }
        let link = window.setup_wizard_data.is_training ? window.fx_pages.TRAINING : window.fx_pages.DASHBOARD;
        return step_tpl({
            items,
            save_exit: link
        });
    };
}
