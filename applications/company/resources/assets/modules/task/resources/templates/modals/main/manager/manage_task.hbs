<form class="m-task-form" data-js="form">
    <div class="c-tf-error" data-js="error"></div>
    <div class="c-tf-grid">
        <div class="f-field t-title">
            <label class="f-f-label">Title</label>
            <input class="f-f-input" type="text" value="" data-js="title" />
        </div>
        <div class="f-field">
            <label class="f-f-label">Type</label>
            <select class="f-f-input" data-js="type" placeholder="-- Select One --">
                {{#each types}}
                    <option value="{{value}}">{{label}}</option>
                {{/each}}
            </select>
        </div>
        <div class="f-field">
            <label class="f-f-label">Priority <span class="f-fl-optional">(Optional)</span></label>
            <select class="f-f-input" data-js="priority" placeholder="-- Select One --">
                {{#each priorities}}
                    <option value="{{value}}">{{label}}</option>
                {{/each}}
            </select>
        </div>
        <div class="f-field">
            <label class="f-f-label">Assigned To</label>
            <select class="f-f-input" data-js="assigned_to_user_id" data-fx-form-input="static-dropdown"></select>
        </div>
        <div class="f-field">
            <label class="f-f-label" data-js="due-date-label">
                Due Date
                <span class="f-fl-optional">(Optional)</span>
                <span class="f-fl-details">
                    <button class="f-fld-button t-reset" disabled data-js="due-date-reset">
                        <div data-text>Reset</div>
                        <svg data-icon><use xlink:href="#remix-icon--system--refresh-line"></use></svg>
                    </button>
                </span>
            </label>
            <input class="f-f-input" type="datetime" data-js="due_date" />
        </div>
        <div class="f-field">
            <label class="f-f-label">Association <span class="f-fl-optional">(Optional)</span></label>
            <select class="f-f-input" data-js="association_type" placeholder="-- Select One --">
                {{#each associations}}
                    <option value="{{value}}">{{label}}</option>
                {{/each}}
            </select>
        </div>
        <div class="f-field t-association-item" data-js="association-item">
            <label class="f-f-label t-hidden" data-js="item_title">Item</label>
            <select class="f-f-input" style="display:none;" data-js="customer" data-fx-form-input="dynamic-dropdown"></select>
            <select class="f-f-input" style="display:none;" data-js="property" data-fx-form-input="dynamic-dropdown"></select>
            <select class="f-f-input" style="display:none;" data-js="project" data-fx-form-input="dynamic-dropdown"></select>
            <select class="f-f-input" style="display:none;" data-js="lead" data-fx-form-input="dynamic-dropdown"></select>
        </div>
        <div class="f-field t-notes">
            <label class="f-f-label">Notes <span class="f-fl-optional">(Optional)</span></label>
            <textarea class="f-f-input" data-fx-form-input="wysiwyg" data-js="notes"></textarea>
        </div>
    </div>
</form>
