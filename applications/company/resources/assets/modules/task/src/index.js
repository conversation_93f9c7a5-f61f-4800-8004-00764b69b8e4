/**
 * @module Task
 */

'use strict';

import Router from '@ca-package/router';

import {MainPage} from './pages/main';

/**
 * Main controller for task module
 *
 * @memberof module:Task
 */
export class Controller {
    /**
     * Task constructor
     *
     * @param {module:Layout.Controller} layout
     */
    constructor(layout) {
        this.state = {
            layout
        };
        this.boot();
    };

    /**
     * Boot module
     */
    boot() {
        this.state.layout.setModeWindow();
        this.state.layout.setTitle('Tasks');
        this.state.router = new Router(MainPage, {
            base_path: '/tasks',
            main_route_callback: (instance) => {
                instance.layout = this.state.layout;
                return instance;
            }
        });
        this.state.router.boot(this.state.layout.elem.content);
    };
}
