<div class="c-pb-page" data-js="page">
    <form data-js="form">
        <div class="m-details">
            <div class="c-name">
                <div class="f-field">
                    <label class="f-f-label">First Name</label>
                    <input class="f-f-input" type="text" value="" data-js="first_name" />
                </div>
                <div class="f-field">
                    <label class="f-f-label">Last Name</label>
                    <input class="f-f-input" type="text" value="" data-js="last_name" />
                </div>
            </div>
            <div class="c-email">
                <div class="f-field">
                    <label class="f-f-label">Email Address</label>
                    <input class="f-f-input" type="text" value="" data-js="email" />
                </div>
            </div>
            <div class="c-photo-button">
                <div class="c-pb-button">
                    <div class="c-pbb-file">
                        <input type="file" name="file" data-js="file">
                    </div>
                    <label class="f-f-label">Photo <span class="f-fl-optional">(Optional)</span></label>
                    <a class="c-pbb-upload" data-js="upload">
                        <div data-text>Upload</div>
                        <svg data-icon><use xlink:href="#remix-icon--system--upload-cloud-2-line"></use></svg>
                    </a>
                    <div class="c-pbb-error" data-js="image-error"></div>
                </div>
                <div data-js="current-image"></div>
            </div>
            <div class="c-phone">
                <div class="f-f-label">Phone Numbers <span class="f-fl-optional">(Optional)</span></div>
                <div class="c-p-container">
                    <div class="c-pc-header">
                        <div class="c-pch-icon">
                            <span data-tooltip data-type="info">Set as primary</span>
                        </div>
                        <div class="f-field">
                            <label class="f-f-label">Type</label>
                        </div>
                        <div class="f-field">
                            <label class="f-f-label">Phone #</label>
                        </div>
                        <div>&nbsp;</div>
                    </div>
                    <div class="c-pc-body" data-js="phone-table">
                    </div>
                    <div class="c-pc-footer">
                        <a class="c-pcf-add" data-js="row-add">
                            <div data-text>Add Row</div>
                            <svg data-icon><use xlink:href="#remix-icon--system--add-circle-line"></use></svg>
                        </a>
                    </div>
                </div>
            </div>
            <div class="c-bio">
                <div class="f-field">
                    <label class="f-f-label">Bio <span class="f-fl-optional">(Optional)</span></label>
                    <textarea class="f-f-input" data-js="bio"></textarea>
                </div>
            </div>
        </div>

        <div class="c-p-header">
            <h4>Notifications</h4>
        </div>

       <div class="c-p-roles">
         <div class="c-pr-settings t-edit">
           <div class="c-prs-content">
             <div class="c-prsc-title">
               Task Assigned
               <span data-tooltip data-type="info">
                 Receive a notification when a task has been assigned to you by another user.
               </span>
             </div>
             <div class="c-prsc-content">
               <div class="f-field">
                 <input
                   class="f-f-input"
                   type="checkbox"
                   id="email_task_assigned_{{ns}}"
                   data-fx-form-input="switch"
                   data-js="email-notification-task-assigned"
                   data-parsley-multiple="roles"
                 >
                 <label class="f-f-label t-label" for="email_task_assigned_{{ns}}">Email</label>
               </div>
             </div>
             <div class="c-prsc-content">
               <div class="f-field">
                 <input
                   class="f-f-input"
                   type="checkbox"
                   id="app_task_assigned_{{ns}}"
                   data-fx-form-input="switch"
                   data-js="app-notification-task-assigned"
                   data-parsley-multiple="roles"
                 >
                 <label class="f-f-label t-label" for="app_task_assigned_{{ns}}">In-App</label>
               </div>
             </div>
           </div>

           <div class="c-prs-content">
             <div class="c-prsc-title">
               Lead Assigned
               <span data-tooltip data-type="info">
                 Receive a notification when a lead has been assigned to you by another user.
               </span>
             </div>
             <div class="c-prsc-content">
               <div class="f-field">
                 <input
                   class="f-f-input"
                   type="checkbox"
                   id="lead_assigned_email_{{ns}}"
                   data-fx-form-input="switch"
                   data-js="email-notification-lead-assigned"
                   data-parsley-multiple="roles"
                 >
                 <label class="f-f-label t-label" for="lead_assigned_email_{{ns}}">Email</label>
               </div>
             </div>
             <div class="c-prsc-content">
               <div class="f-field">
                 <input
                   class="f-f-input"
                   type="checkbox"
                   id="lead_assigned_app_{{ns}}"
                   data-fx-form-input="switch"
                   data-js="app-notification-lead-assigned"
                   data-parsley-multiple="roles"
                 >
                 <label class="f-f-label t-label" for="lead_assigned_app_{{ns}}">In -App</label>
               </div>
             </div>
           </div>

           <div class="c-prs-content">
             <div class="c-prsc-title">
               Project Assigned
               <span data-tooltip data-type="info">
                 Receive a notification when a project has been assigned to you by another user.
               </span>
             </div>
             <div class="c-prsc-content">
               <div class="f-field">
                 <input
                   class="f-f-input"
                   type="checkbox"
                   id="project_assigned_email_{{ns}}"
                   data-fx-form-input="switch"
                   data-js="email-notification-project-assigned"
                   data-parsley-multiple="roles"
                 >
                 <label class="f-f-label t-label" for="project_assigned_email_{{ns}}">Email</label>
               </div>
             </div>
             <div class="c-prsc-content">
               <div class="f-field">
                 <input
                   class="f-f-input"
                   type="checkbox"
                   id="project_assigned_app_{{ns}}"
                   data-fx-form-input="switch"
                   data-js="app-notification-project-assigned"
                   data-parsley-multiple="roles"
                 >
                 <label class="f-f-label t-label" for="project_assigned_app_{{ns}}">In-App</label>
               </div>
             </div>
           </div>

           <div class="c-prs-content">
             <div class="c-prsc-title">
               Appointment Assigned
               <span data-tooltip data-type="info">
                 Receive a notification when a Project Appointment has been assigned to you by another user.
               </span>
             </div>
             <div class="c-prsc-content">
               <div class="f-field">
                 <input
                   class="f-f-input"
                   type="checkbox"
                   id="appointment_scheduled_email_{{ns}}"
                   data-fx-form-input="switch"
                   data-js="email-notification-appointment-scheduled"
                   data-parsley-multiple="roles"
                 >
                 <label class="f-f-label t-label" for="appointment_scheduled_email_{{ns}}">Email</label>
               </div>
             </div>
             <div class="c-prsc-content">
               <div class="f-field">
                 <input
                   class="f-f-input"
                   type="checkbox"
                   id="appointment_scheduled_app_{{ns}}"
                   data-fx-form-input="switch"
                   data-js="app-notification-appointment-scheduled"
                   data-parsley-multiple="roles"
                 >
                 <label class="f-f-label t-label" for="appointment_scheduled_app_{{ns}}">In-App</label>
               </div>
             </div>
           </div>

           <div class="c-prs-content">
             <div class="c-prsc-title">
               Bid Viewed
               <span data-tooltip data-type="info">
                 Receive a notification when your project's bid has been viewed by the customer.
               </span>
             </div>
             <div class="c-prsc-content">
               <div class="f-field">
                 <input
                   class="f-f-input"
                   type="checkbox"
                   id="bid_viewed_email_{{ns}}"
                   data-fx-form-input="switch"
                   data-js="email-notification-bid-viewed"
                   data-parsley-multiple="roles"
                 >
                 <label class="f-f-label t-label" for="bid_viewed_email_{{ns}}">Email</label>
               </div>
             </div>
             <div class="c-prsc-content">
               <div class="f-field">
                 <input
                   class="f-f-input"
                   type="checkbox"
                   id="bid_viewed_app_{{ns}}"
                   data-fx-form-input="switch"
                   data-js="app-notification-bid-viewed"
                   data-parsley-multiple="roles"
                 >
                 <label class="f-f-label t-label" for="bid_viewed_app_{{ns}}">In-App</label>
               </div>
             </div>
           </div>

           <div class="c-prs-content">
             <div class="c-prsc-title">
               Bid Accepted
               <span data-tooltip data-type="info">
                 Receive a notification when your project's bid has been accepted by the customer.
               </span>
             </div>
             <div class="c-prsc-content">
               <div class="f-field">
                 <input
                   class="f-f-input"
                   type="checkbox"
                   id="bid_accepted_email_{{ns}}"
                   data-fx-form-input="switch"
                   data-js="email-notification-bid-accepted"
                   data-parsley-multiple="roles"
                 >
                 <label class="f-f-label t-label" for="bid_accepted_email_{{ns}}">Email</label>
               </div>
             </div>
             <div class="c-prsc-content">
               <div class="f-field">
                 <input
                   class="f-f-input"
                   type="checkbox"
                   id="bid_accepted_app_{{ns}}"
                   data-fx-form-input="switch"
                   data-js="app-notification-bid-accepted"
                   data-parsley-multiple="roles"
                 >
                 <label class="f-f-label t-label" for="bid_accepted_app_{{ns}}">In-App</label>
               </div>
             </div>
           </div>

           <div class="c-prs-content">
             <div class="c-prsc-title">
               Bid Rejected
               <span data-tooltip data-type="info">
                 Receive a notification when your project's bid has been rejected by the customer.
               </span>
             </div>
             <div class="c-prsc-content">
               <div class="f-field">
                 <input
                   class="f-f-input"
                   type="checkbox"
                   id="bid_rejected_email_{{ns}}"
                   data-fx-form-input="switch"
                   data-js="email-notification-bid-rejected"
                   data-parsley-multiple="roles"
                 >
                 <label class="f-f-label t-label" for="bid_rejected_email_{{ns}}">Email</label>
               </div>
             </div>
             <div class="c-prsc-content">
               <div class="f-field">
                 <input
                   class="f-f-input"
                   type="checkbox"
                   id="bid_rejected_app_{{ns}}"
                   data-fx-form-input="switch"
                   data-js="app-notification-bid-rejected"
                   data-parsley-multiple="roles"
                 >
                 <label class="f-f-label t-label" for="bid_rejected_app_{{ns}}">In-App</label>
               </div>
             </div>
           </div>
         </div>
       </div>

    </form>
</div>