'use strict';

const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector} = require("@ca-package/dom");

const main_tpl = require('@cam-user-profile-tpl/main.hbs');

class Main extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        this.state.components = {
            info: require('./main-components/info'),
            menu: require('./main-components/menu')
        };
    }

    /**
     * Get child routes
     *
     * @returns {object}
     */
    static get routes() {
        let routes = {
            profile: {
                default: true,
                page: require('./main-pages/profile')
            },
            security: {
                path: '/security',
                page: require('./main-pages/security')
            }
        };
        if (window.profile_data.integrations) {
            Object.assign(routes, {
                integrations: {
                    path: '/integrations',
                    page: require('./main-pages/integrations')
                }
            });
        }
        return routes;
    };

    /**
     * Resizes the user-profile image based on the window height
     *
     * profile_container allows for padding adjustments based on window_height
     *
     */
    resizeImage() {
        let window_height = window.innerHeight,
            info = this.getComponent('info').elem,
            image_wrapper = info.image_wrapper,
            profile_container = this.elem.header_wrapper;

        if (window_height <= 840) {
            image_wrapper.addClass('t-small');
            profile_container.addClass('t-small');
        } else {
            image_wrapper.removeClass('t-small');
            profile_container.removeClass('t-small');
        }

        if (window_height <= 720) {
            image_wrapper.addClass('t-xsmall');
        } else {
            image_wrapper.removeClass('t-xsmall');
        }

        if (window_height <= 600) {
            this.elem.header_wrapper.addClass('t-mobile');
        } else {
            this.elem.header_wrapper.removeClass('t-mobile');
        }
    };

    /**
     * Show bottom border if components height >= section height
     */
    setModuleBorder() {
        // @todo will review this again in the future, for now just adding the border always
        // let section_height = this.elem.sections_wrapper.outerHeight(),
        //     components_height = this.elem.sections.innerHeight();
        //
        // // Reset
        // this.elem.sections_wrapper.removeClass('t-border');
        //
        // if (components_height >= section_height) {
        //     this.elem.sections_wrapper.addClass('t-border');
        // }
    };

    /**
     * Set layout
     *
     * @param {module:Layout.Controller} instance
     */
    set layout(instance) {
        this.state.layout = instance;
    };

    /**
     * Get layout
     *
     * @readonly
     *
     * @returns {module:Layout.Controller}
     */
    get layout() {
        return this.state.layout;
    };

    /**
     * Get container for child pages to be rendered
     *
     * @returns {jQuery}
     */
    getPageContainer() {
        return this.elem.sections;
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        await super.load(request, next);
        this.resizeImage();

        // have to wait for style transitions to finish to get actual height calculations
        setTimeout(() => {
            this.setModuleBorder();
        }, 400);

        $(window).on('resize', (e) => {
            this.resizeImage();
            this.setModuleBorder();
        });
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.header_wrapper = findChild(root, jsSelector('header-wrapper'));
        this.elem.sections_wrapper = findChild(root, jsSelector('sections-wrapper'));
        this.elem.sections = findChild(root, jsSelector('sections'));
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return main_tpl();
    };
}

module.exports = Main;
