/**
 * @module Api/Batch/Request/Base
 */

'use strict';

/**
 * @alias module:Api/Batch/Request/Base
 */
class Base {
    /**
     * Constructor
     */
    constructor() {
        /**
         * @private
         */
        this.state = {
            deferred: null,
            response: null
        };
    };

    /**
     * Set response of request
     *
     * @param response
     */
    response(response) {
        this.state.response = response;
        if (this.state.deferred !== null) {
            let value = {
                response: response,
                request: this
            };
            if (response.isError()) {
                this.state.deferred.reject(value);
            } else {
                this.state.deferred.resolve(value);
            }
        }
    }

    /**
     * Get response
     *
     * @returns {*}
     */
    getResponse() {
        return this.state.response;
    };

    /**
     * Get promise
     *
     * @readonly
     *
     * @returns {*}
     */
    get promise() {
        if (this.state.deferred === null) {
            this.state.deferred = $.Deferred();
        }
        return this.state.deferred.promise();
    };
}

module.exports = Base;
