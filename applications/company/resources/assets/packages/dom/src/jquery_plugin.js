'use strict';

import $ from 'jquery';

function buildSelector(name, data) {
    let selector = `[data-js="${name}"]`;
    for (let key in data) {
        if (!data.hasOwnProperty(key)) {
            continue;
        }
        selector += `[data-${key}="${data[key]}"]`;
    }
    return selector;
}

$.fn.fxFind = function (name, data = {}) {
    return this.find(buildSelector(name, data));
};
$.fn.fxFindFirst = function (name, data = {}) {
    return this.find(`${buildSelector(name, data)}:first`);
};
$.fn.fxChildren = function (name, data = {}) {
    return this.children(buildSelector(name, data));
};
$.fn.fxParents = function (name, data = {}) {
    return this.parents(buildSelector(name, data));
};
$.fn.fxClosest = function (name, data = {}) {
    return this.closest(buildSelector(name, data));
};

// event helper
function namespaceEvents(events, config) {
    if (typeof events === 'string') {
        events = [events];
    }
    let namespace = '.fx';
    if (typeof config.namespace !== 'undefined') {
        namespace += '.' + config.namespace.trim('.');
    }
    for (let key in events) {
        events[key] += namespace;
    }
    return events.join(' ');
}
function preventDefault(closure, enabled) {
    if (!enabled) {
        return closure;
    }
    return function (e) {
        e.preventDefault();
        closure.call(this, e);
        return false;
    };
}

$.fn.fxEvent = function (events, closure, config = {}) {
    return this.on(namespaceEvents(events, config), closure);
};
$.fn.fxEventWatcher = function (events, name, closure, config = {}) {
    return this.on(namespaceEvents(events, config), `[data-js="${name}"]`, closure);
};
$.fn.fxEventDestroy = function (events, config = {}) {
    return this.off(namespaceEvents(events, config));
};

// click helpers
$.fn.fxClick = function (closure, prevent_default = false) {
    return this.fxEvent('click', preventDefault(closure, prevent_default));
};
$.fn.fxClickWatcher = function (name, closure, prevent_default = false) {
    return this.fxEventWatcher('click', name, preventDefault(closure, prevent_default));
};
$.fn.fxClickDestroy = function () {
    return this.fxEventDestroy('click');
};
