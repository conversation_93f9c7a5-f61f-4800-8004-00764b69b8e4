'use strict';

/**
 * Base class for all routes
 *
 * @memberof module:Router
 * @abstract
 */
class Route {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        this.properties = {
            router,
            name,
            parent,
            router_actions: []
        };
        this.state = {};
    };

    /**
     * Get router instance
     *
     * @readonly
     *
     * @returns {module:Router.Controller}
     */
    get router() {
        return this.properties.router;
    };

    /**
     * Get name
     *
     * @readonly
     *
     * @returns {string}
     */
    get name() {
        return this.properties.name;
    };

    /**
     * Get parent component
     *
     * @returns {module:Router.Page}
     */
    get parent() {
        return this.properties.parent;
    };

    /**
     * Get parent instance by name
     *
     * @param {string} name
     * @returns {module:Router.Page|null}
     */
    getParentByName(name) {
        let parent = this.parent;
        if (parent === null || parent.name === name) {
            return parent;
        }
        return parent.getParentByName(name);
    };

    /**
     * Subscribe to router action
     *
     * Any action subscribed via this method will automatically be unsubscribed when route unloads for convenience.
     *
     * @param {string} action - action to subscribe to
     * @param {string} method - method to call of route with action data
     */
    routerSubscribe(action, method) {
        if (this.properties.router_actions.indexOf(action) !== -1) {
            throw new Error(`Already subscribed to router action: ${action}`);
        }
        this.properties.router_actions.push(action);
        this.router.subscribe(action, this, method);
    };

    /**
     * Unsubscribe from router action
     *
     * @param {string} action
     */
    routerUnsubscribe(action) {
        if (this.properties.router_actions.indexOf(action) === -1) {
            throw new Error(`Not subscribed to router action: ${action}`);
        }
        this.properties.router_actions = this.properties.router_actions.filter(router_action => router_action !== action);
        this.router.unsubscribe(action, this);
    };

    /**
     * Unsubscribe from all actions registered with routerSubscribe() method
     */
    routerUnsubscribeAll() {
        if (this.properties.router_actions.length === 0) {
            return;
        }
        this.properties.router_actions.forEach(action => this.router.unsubscribe(action, this));
        this.properties.router_actions = [];
    };

    /**
     * Load route
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        await next(request);
    };

    /**
     * Refresh active route
     *
     * Used when the same route is requested, since no unload and load action will be run.
     *
     * @param {object} request
     */
    refresh(request) {};

    /**
     * Determines if user is allowed to leave page via beforeunload event
     *
     * Checks must be kept simple as you can't reliably do time consuming tasks in the event.
     *
     * @returns {boolean}
     */
    allowUnload() {
        return true;
    };

    /**
     * Handle actions before unload of route
     *
     * Runs synchronously before unload allowing for reliably running code before the load of the next route. This is
     * useful since unload is an asynchronous process.
     */
    beforeUnload() {};

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        await next(request);
        this.routerUnsubscribeAll();
    };

    /**
     * Boot route
     */
    boot() {};
}

module.exports = Route;
