'use strict';

import groupBy from 'lodash/groupBy';

/**
 * @memberof module:RulesEngine
 */
export class RuleSet {
    constructor() {
        /**
         * @protected
         */
        this.state = {
            rules: [],
            sorted: false
        };
    };

    /**
     * Add rule
     *
     * @param {module:RulesEngine.Rule} rule
     * @returns {module:RulesEngine.RuleSet}
     */
    add(rule) {
        this.state.rules.push(rule);
        this.state.sorted = false;
        return this;
    };

    /**
     * Get all rules sorted by priority
     *
     * Rules with the lowest priority are evaluated earlier.
     */
    getRules() {
        if (!this.state.sorted) {
            // convert group object into array to use native sorting
            let temp_priority_groups = groupBy(this.state.rules, 'priority'),
                priority_groups = [];
            for (let priority of Object.keys(temp_priority_groups)) {
                priority_groups.push({
                    priority: parseInt(priority),
                    rules: temp_priority_groups[priority]
                });
            }
            temp_priority_groups = null;
            priority_groups.sort((a, b) => a.priority - b.priority);
            // flatten rule set back to single level array
            let rules = [];
            for (let {rules: group_rules} of priority_groups) {
                rules = rules.concat(group_rules);
            }
            this.state.rules = rules;
            this.state.sorted = true;
        }
        return this.state.rules;
    };
}
