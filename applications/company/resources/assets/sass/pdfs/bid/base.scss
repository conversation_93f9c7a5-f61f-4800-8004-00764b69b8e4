@use '~@cac-sass/base';
@use 'sass:color' as sass-color;
@use 'sass:math';

* {
    box-sizing: border-box;
    text-rendering: optimizeLegibility;
}
html, body {
    margin: 0;
    padding: 0;
    font-size: 11pt;
}
body {
    font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif;
    line-height: 1.2;
}

/* TYPOGRAPHY */
%header-margin {
    margin: 1rem 0 0.75rem;
}
%margin {
    margin: 0 0 1rem;
}

h1 {
    @extend %header-margin;
    font-size: 2.5rem;
}
h2 {
    @extend %header-margin;
    font-size: 2rem;
}
h3 {
    @extend %header-margin;
    font-size: 1.375rem;
}
h4 {
    @extend %header-margin;
    font-size: 1.125rem;
}
h5 {
    @extend %header-margin;
    font-size: 1rem;
}
h6 {
    @extend %header-margin;
    font-size: 0.875rem;
}
p {
    @extend %margin;
    font-size: 12pt;
    font-weight: 200;
    line-height: 1.4;
}
ul {
    @extend %margin;
    li {
        margin-bottom: 0.5rem;
        &:last-child {
            margin-bottom: 0;
        }
    }
}
ol {
    @extend %margin;
    li {
        margin-bottom: 0.5rem;
        &:last-child {
            margin-bottom: 0;
        }
    }
}
a {
    color: base.$color-primary-default !important;
    &:hover {
        color: base.$color-primary-light-1;
        background-color: #fff;
    }
}

.font-light {
    font-weight: 300;
}

.font-regular {
    font-weight: 400;
}

.font-heavy {
    font-weight: 700;
}

/* POSITIONING */
.text-left {
    text-align: left;
}
.text-right {
    text-align: right;
}
.text-center {
    text-align: center;
    margin-left: auto;
    margin-right: auto;
}
.justify {
    text-align: justify;
}
.no-margin {
    margin: 0;
}
.no-margin-top {
    margin-top: 0;
}
.no-margin-bottom {
    margin-bottom: 0;
}

/* PAGE */
.page {
    position: relative;
    page-break-after: always;
    margin: 0;
    padding: 0;
    width: 7in;
    height: 10in;
    &.static {
        overflow: hidden;
    }
    &.expandable {
        height: auto;
        page-break-after: auto;
    }
    &.break-before {
        page-break-before: always;
    }
}

/* GRID */
.m-grid {
    width: 100%;
    .c-g-row {
        position: relative;
        width: 100%;
        &::after {
            content: "";
            display: table;
            clear: both;
        }
        [class*="c-gr-column"] {
            padding-right: 20px;
            &:last-of-type {
                padding-right: 0;
            }
        }
    }
        .c-gr-column {
            float: left;
            margin: 0 0 1rem;
            $grid-columns: 12;
            @for $i from 1 through $grid-columns {
                &.t-size-#{$i} {
                    width: percentage(math.div($i, $grid-columns));
                }
            }
        }
}

/* TABLE */
.m-table {
    width: 100%;
    margin: 0 0 1rem;
    border-collapse: collapse;
    thead {
        display: table-header-group;
    }
    tfoot {
        display: table-row-group;
    }
    tr {
        page-break-inside: avoid;
    }
    %padding {
        padding: 5px;
    }
    th {
        @extend %padding;
        background-color: lightgrey;
    }
    td {
        @extend %padding;
        border-top: 1px solid #ccc;
    }
    $grid-columns: 12;
    @for $i from 1 through $grid-columns {
        .size-#{$i} {
            width: percentage(math.div($i, $grid-columns));
        }
    }

}
