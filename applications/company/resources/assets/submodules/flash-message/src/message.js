'use strict';

require('remixicon/icons/System/close-line.svg');
require('remixicon/icons/Device/restart-line.svg');

const message_tpl = require('@cas-flash-message-tpl/message.hbs');

const Types = {
    ERROR: 1,
    INFO: 2,
    SUCCESS: 3
};

/**
 * @memberof module:FlashMessage
 */
class Message {
    /**
     * Constructor
     *
     * @param {string} message
     * @param {number} type
     */
    constructor(message, type) {
        Message.__idx++;
        this.elem = {};
        /**
         * @private
         */
        this.state = {
            id: Message.__idx,
            message: message,
            type: type,
            action_idx: 0,
            actions: new Map,
            delete_time: null,
            delete_timer: null
        };
    };

    /**
     * Short hand method to create message instance
     *
     * @param {string} message
     * @param {number} type
     * @returns {module:FlashMessage.Message}
     */
    static make(message, type) {
        return new Message(message, type);
    };

    /**
     * Short hand method to create info message instance
     *
     * @param {string} message
     * @returns {module:FlashMessage.Message}
     */
    static info(message) {
        return this.make(message, Types.INFO);
    };

    /**
     * Short hand method to create error message instance
     *
     * @param {string} message
     * @returns {module:FlashMessage.Message}
     */
    static error(message) {
        return this.make(message, Types.ERROR);
    };

    /**
     * Short hand method to create success message instance
     *
     * @param {string} message
     * @returns {module:FlashMessage.Message}
     */
    static success(message) {
        return this.make(message, Types.SUCCESS);
    };

    /**
     * Types
     *
     * @readonly
     *
     * @returns {object}
     */
    static get Type() {
        return Types;
    };

    /**
     * Get id
     *
     * @readonly
     *
     * @returns {number}
     */
    get id() {
        return this.state.id;
    };

    /**
     * Add preconfigured action for clear
     */
    actionClear() {
        this.action('remix-icon--system--close-line', () => {
            this.delete();
        });
    };

    /**
     * Add preconfigured action for retry
     *
     * @param {function} handler
     */
    actionRetry(handler) {
        this.action('remix-icon--device--restart-line', handler);
    };

    /**
     * Add action
     *
     * @param {string} icon - Icon to display for action
     * @param {function} handler
     */
    action(icon, handler) {
        this.state.actions.set(this.state.action_idx, {
            idx: this.state.action_idx,
            icon: icon,
            handler: handler
        });
        this.state.action_idx++;
    };

    /**
     * Set number of seconds to delete message after
     *
     * @param {?number} seconds
     */
    deleteAfter(seconds) {
        this.state.delete_time = seconds;
    };

    /**
     * Handle action
     *
     * @param {number} idx
     */
    handleAction(idx) {
        let action = this.state.actions.get(idx);
        if (action === undefined) {
            return;
        }
        action.handler();
    };

    /**
     * Delete message
     */
    delete() {
        this.elem.root.remove();
        this.state.controller.clearMessage(this.state.id);
        if (this.state.delete_timer !== null) {
            clearTimeout(this.state.delete_timer);
            this.state.delete_timer = null;
        }
    };

    /**
     * Boot message
     *
     * @param {module:FlashMessage.Controller} controller
     */
    boot(controller) {
        this.state.controller = controller;

        this.elem.root = controller.elem.root.fxChildren('message', {id: this.state.id});

        if (this.state.delete_time !== null) {
            this.state.delete_timer = setTimeout(() => {
                this.delete();
            }, this.state.delete_time * 1000);
        }
    };

    /**
     * Render message
     *
     * @returns {string}
     */
    render() {
        let type_map = {
            [Message.Type.ERROR]: 'error',
            [Message.Type.INFO]: 'info',
            [Message.Type.SUCCESS]: 'success'
        };

        return message_tpl({
            id: this.state.id,
            type: type_map[this.state.type],
            message: this.state.message,
            actions: Array.from(this.state.actions.values())
        });
    };
}

Message.__idx = 0;

module.exports = Message;
