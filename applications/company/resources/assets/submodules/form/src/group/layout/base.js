/**
 * @module Form/Group/Layout
 */

'use strict';

/**
 * @typedef {Object} LayoutData
 * @property {number} type - Layout type
 */

/**
 * @memberof module:Form/Group
 */
export class Layout {
    /**
     * Constructor
     *
     * @param {module:Form.Group} group - Parent group
     * @param {LayoutData} data
     */
    constructor(group, data) {
        /**
         * @protected
         */
        this.state = {
            group: group,
            type: data.type
        };
    };

    /**
     * Get layout type
     *
     * @readonly
     *
     * @returns {number}
     */
    get type() {
        return this.state.type;
    };
}
