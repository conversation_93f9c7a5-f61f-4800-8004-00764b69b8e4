'use strict';

import difference from 'lodash/difference';

/**
 * @memberof module:Form/Rules
 */
export class ConditionParser {
    constructor() {
        this.state = {
            config: {
                // left
                left: {
                    mapping: 'left'
                },
                field: {
                    mapping: 'left',
                    context: value => context => context.get(`field:${value}`)
                },
                context: {
                    mapping: 'left',
                    context: value => context => context.get(value)
                },
                meta: {
                    mapping: 'left',
                    context: value => context => context.get(`meta:${value}`)
                },
                product_item_meta: {
                    mapping: 'left',
                    context: ([id, name]) => context => context.get(`product-item-meta:${id}:${name}`)
                },
                product_item_price: {
                    mapping: 'left',
                    context: ([id, quantity]) => context => context.get(`product-item-price:${id}:${quantity}`)
                },
                product_item_total: {
                    mapping: 'left',
                    context: ([id, quantity]) => context => context.get(`product-item-total:${id}:${quantity}`)
                },
                group_line_item_total: {
                    mapping: 'left',
                    context: group => context => context.get(`group-line-item-total:${group}`)
                },

                // right
                right: {
                    mapping: 'right'
                },
                value: {
                    mapping: 'right'
                },
                field_2: {
                    mapping: 'right',
                    context: value => context => context.get(`field:${value}`)
                },
                context_2: {
                    mapping: 'right',
                    context: value => context => context.get(value)
                },
                meta_2: {
                    mapping: 'right',
                    context: value => context => context.get(`meta:${value}`)
                },
                field_option: {
                    mapping: 'right',
                    context: (value, condition) => context => context.get(`field-option:${condition.field}.${value}`)
                },
                product_item_meta_2: {
                    mapping: 'right',
                    context: ([id, name]) => context => context.get(`product-item-meta:${id}:${name}`)
                },
                product_item_price_2: {
                    mapping: 'right',
                    context: ([id, quantity]) => context => context.get(`product-item-price:${id}:${quantity}`)
                },
                product_item_total_2: {
                    mapping: 'right',
                    context: ([id, quantity]) => context => context.get(`product-item-total:${id}:${quantity}`)
                },
                group_line_item_total_2: {
                    mapping: 'right',
                    context: group => context => context.get(`group-line-item-total:${group}`)
                }
            },
            keys: null
        };
    };

    /**
     * Add new configuration for condition
     *
     * @param {string} key - Key in condition to look for
     * @param {string} side - Side which the value should be mapped to
     * @param {function|undefined} context - Context function to allow controlling of output value
     */
    addConfig(key, side, context = undefined) {
        this.state.config[key] = {mapping: side, context};
    };

    /**
     * Get and cache list of keys to search for
     */
    getKeys() {
        if (this.state.keys === null) {
            this.state.keys = Object.keys(this.state.config);
        }
        return this.state.keys;
    };

    /**
     * Parse condition data into rules engine rule instance
     *
     * @param {module:RulesEngine.Rule} rule - rules engine rule
     * @param {object|array} condition
     */
    parse(rule, condition) {
        if (Array.isArray(condition)) {
            for (let cond of condition) {
                this.parse(rule, cond);
            }
            return;
        }
        let group_types = ['any', 'all'];
        for (let group_type of group_types) {
            if (condition[group_type] === undefined) {
                continue;
            }
            rule[group_type](() => this.parse(rule, condition[group_type]));
            return;
        }
        if (condition.operator === undefined) {
            throw new Error('Operator is require in condition');
        }
        let expression_parts = {};
        for (let key of this.getKeys()) {
            if (condition[key] === undefined) {
                continue;
            }
            let config = this.state.config[key],
                value = condition[key];
            if (typeof config.context === 'function') {
                value = config.context(value, condition);
            }
            expression_parts[config.mapping] = value;
        }
        let missing_parts = difference(['left', 'right'], Object.keys(expression_parts));
        if (missing_parts.length > 0) {
            throw new Error('Condition missing the following expression parts: ' + missing_parts.join(', '));
        }
        rule.condition(expression_parts.left, condition.operator, expression_parts.right);
    };
}
