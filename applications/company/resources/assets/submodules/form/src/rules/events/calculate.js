'use strict';

import expression from '../expression_parser';

/**
 * Run calculation using context values
 *
 * @param {object} config
 * @param {string} config.expression
 * @param {number|undefined} config.scale
 * @param {number|undefined} config.round
 * @param {number|undefined} config.round_up
 * @param {number|undefined} config.round_down
 * @param {string|undefined} config.result_field_id
 * @param {string|undefined} config.result_context_key
 */
export async function calculate(config) {
    try {
        let value = expression(await this.context.parse(config.expression)),
            scale = config.scale || 0,
            round = config.round || null,
            round_up = config.round_up || null,
            round_down = config.round_down || null;
        value = typeof value === 'number' ? value.toFixed(scale) : null;
        if (round) {
            value = Math.round(value / round) * round;
        } else if (round_up) {
            value = Math.ceil(value / round_up) * round_up;
        } else if (round_down) {
            value = Math.floor(value / round_down) * round_down;
        }
        if (config.result_field_id !== undefined) {
            let result_field = this.group.field(config.result_field_id, true);
            if (result_field !== undefined) {
                result_field.setValue(value, true, false);
            }
        } else if (config.result_context_key !== undefined) {
            this.context.set(config.result_context_key, value);
        }
    } catch (e) {
        console.error(e);
    }
}
