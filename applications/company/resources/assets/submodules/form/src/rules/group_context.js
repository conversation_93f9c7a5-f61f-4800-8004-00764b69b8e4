'use strict';

import get from 'lodash/get';

import {Context} from '@ca-package/rules-engine/src/context';

/**
 * @memberof module:Form/Rules
 */
export class GroupContext extends Context {
    /**
     * Constructor
     *
     * Setup form related subcontexts.
     *
     * @param {module:Form.Group} group
     * @param {object} [data={}]
     */
    constructor(group, data = {}) {
        super(data);
        this.setSubcontext('dependency', alias => group.form.dependency(alias) ?? null);
        this.setSubcontext('meta', name => group.form.meta(name) ?? null);
        this.setSubcontext('field', id => {
            let path = ['value'];
            id = id.replace(/\.\.\//g, '~P~');
            if (id.indexOf('.') !== -1) {
                [id, ...path] = id.split('.');
            }
            id = id.replace(/~P~/g, '../');
            let field = group.field(id, true);
            return field !== undefined ? get(field, path, null) : null;
        });
        this.setSubcontext('field-option', id => {
            let [field_id, option_id] = id.split('.'),
                field = group.field(field_id, true);
            if (field === undefined) {
                return null;
            }
            return field.option(option_id)?.id ?? null;
        });
        const product_item_helper = group.form.product_item_helper;
        this.setSubcontext('product-item-price', async id => {
            id = await this.parse(id);
            let [product_item_id, quantity] = id.split(':');
            try {
                let product_item = await product_item_helper.get(product_item_id);
                return product_item_helper.getPrice(product_item, quantity)?.price.toFixed(2) ?? null;
            } catch (e) {
                console.log(e);
                return null;
            }
        });
        this.setSubcontext('product-item-total', async id => {
            id = await this.parse(id);
            let [product_item_id, quantity] = id.split(':');
            try {
                let product_item = await product_item_helper.get(product_item_id);
                return product_item_helper.getTotal(product_item, quantity)?.toFixed(2) ?? null;
            } catch (e) {
                console.log(e);
                return null;
            }
        });
        this.setSubcontext('product-item-meta', async id => {
            id = await this.parse(id);
            let [product_item_id, meta_name] = id.split(':');
            try {
                let product_item = await product_item_helper.get(product_item_id);
                return product_item_helper.getMeta(product_item, meta_name) ?? null;
            } catch (e) {
                console.log(e);
                return null;
            }
        });
        const line_item_helper = group.form.line_item_helper;
        this.setSubcontext('group-line-item-total', id => {
            let grp = group.child(id, true);
            if (grp === undefined) {
                return null;
            }
            return line_item_helper.getTotalByGroup(grp).toFixed(2);
        });
    };
}
