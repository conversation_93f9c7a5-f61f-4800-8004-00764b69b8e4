'use strict';

import Handlebars from 'handlebars/runtime';

import {OptionTypes} from '../../group/field/option/select/constants';

Handlebars.registerHelper('selectOptions', function (context) {
    let str = '<option value="">-- Select One --</option>';
    let handle_item = (item) => {
        switch (item.type) {
            case OptionTypes.OPTION:
                str += '<option value="' +
                    Handlebars.escapeExpression(item.option.id) + '">' +
                    Handlebars.escapeExpression(item.option.label) + '</option>';
                break;
            case OptionTypes.GROUP:
                str += '<optgroup label="' + Handlebars.escapeExpression(item.group.label) + '">';
                item.group.options.forEach(handle_item);
                str += '</optgroup>';
                break;
        }
    };
    context.forEach(handle_item);
    return new Handlebars.SafeString(str);
});
