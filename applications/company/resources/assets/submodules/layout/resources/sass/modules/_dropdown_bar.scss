@use '~@cac-sass/base';
@use '../config';

.m-layout-dropdown-bar {
    position: relative;
    width: 100%;
    .c-ldb-dropdown {
        position: absolute;
        top: 0;
        width: 100%;
        height: 0;
        overflow: hidden;
        transition: height 0.3s;
        z-index: 100;
        &.t-top {
            position: relative;
            z-index: 101;
        }
        &.t-show {
            height: config.$title-bar-height;
        }
    }
        .c-ldbd-inner {
            display: flex;
            position: absolute;
            bottom: 0;
            width: 100%;
            height: config.$title-bar-height;
            background-color: base.$color-white-default;
        }
            .c-ldbdi-link {
                display: block;
                padding: 0 base.unit-rem-calc(20px);
                @include base.typo-nav($size: 14px, $line-height: config.$title-bar-height);
                color: base.$color-grey-dark-2;
                text-transform: uppercase;
                transition: all 0.2s ease-out;
                &.t-active {
                    color: base.$color-primary-default;
                }
                &:hover {
                    color: base.$color-primary-light-1;
                }
                &:first-child {
                    padding-left: base.unit-rem-calc(8px);
                }
            }
}
