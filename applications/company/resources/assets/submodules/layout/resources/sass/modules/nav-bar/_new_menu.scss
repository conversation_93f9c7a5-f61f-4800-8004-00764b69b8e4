@use '~@cac-sass/base';

.m-layout-new-menu {
    display: none;
    padding: base.unit-rem-calc(4px);
    border-radius: 0 0 base.unit-rem-calc(8px) base.unit-rem-calc(8px);
    border-width: 0 base.unit-rem-calc(1px) base.unit-rem-calc(1px) base.unit-rem-calc(1px);
    border-style: solid;
    border-color: base.$color-grey-light-4;
    background-color: base.$color-white-default;
    box-shadow: base.$elevation-level-4;
    margin-top: base.unit-rem-calc(-2px) !important;
    z-index: 500;
    &.t-show {
        display: block;
    }
    .c-lnm-item {
        display: flex;
        align-items: center;
        height: base.unit-rem-calc(32px);
        width: base.unit-rem-calc(160px);
        color: base.$color-grey-dark-4;
        border-radius: base.unit-rem-calc(4px);
        transition: all 0.2s ease-out;
        &:first-child {
            margin-top: 0;
        }
        &:hover {
            color: base.$color-primary-default;
            background-color: base.$color-primary-light-4;
            .c-lnmi-icon {
                color: base.$color-primary-default;
            }
        }
    }
        .c-lnmi-icon {
            @include base.svg-icon('default-18');
            color: base.$color-grey-light-1;
            margin: base.unit-rem-calc(7px);
            transition: all 0.2s ease-out;
        }
        .c-lnmi-text {
            font-size: base.unit-rem-calc(14px);
        }
}
