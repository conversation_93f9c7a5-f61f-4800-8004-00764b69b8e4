<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title><?=$this->shared('title')?></title>
    <link rel="stylesheet" media="screen" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,600" />
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.7.2/css/all.css" integrity="sha384-fnmOCqbTlWIlj8LyTjo7mOUStjsKC4pOpQbqyi7RrhN7udi9RwhKkMHpvLbHG9Sr" crossorigin="anonymous">
    <link rel="stylesheet" media="screen" href="<?=$this->asset->uri('style', 'errors/base.css')?>" />
    <link rel="shortcut icon" href="<?=$this->asset->uri('image', 'favicon.ico')?>" type="image/x-icon">
</head>
<body class="background error-page-wrapper background-color background-image">
<center>
    <div class="content-container shadow">
        <div class="head-line secondary-text-color"><?=$this->shared('code')?></div>
        <div class="subheader primary-text-color"><?=$this->shared('subheader')?></div>
        <div class="hr"></div>
        <div class="context secondary-text-color">
<?php if (($content = $this->getRaw('content')) !== ''): echo $content; else: ?>
            <p>You may want to head back to the homepage. If you think something is broken, report a problem.</p>
<?php endif; ?>
        </div>
        <div class="buttons-container">
<?php if (($button = $this->sharedRaw('button', null)) !== null): echo $button; else: ?>
            <a class="button" href="<?=$this->uri->route('page.app.home')?>" target="_blank"><span class="fas fa-home"></span> Go to homepage</a>
<?php endif; ?>
            <a class="button" href="mailto:<EMAIL>" target="_blank"><span class="fas fa-exclamation-triangle"></span> Report a problem</a>
        </div>
    </div>
</center>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.3/jquery.min.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.11.4/jquery-ui.min.js"></script>
<script>
    function ErrorPage(container, pageType, templateName) {
        this.$container = $(container);
        this.$contentContainer = this.$container.find(templateName == 'sign' ? '.sign-container' : '.content-container');
        this.pageType = pageType;
        this.templateName = templateName;
    }

    ErrorPage.prototype.centerContent = function () {
        var containerHeight = this.$container.outerHeight()
            , contentContainerHeight = this.$contentContainer.outerHeight()
            , top = (containerHeight - contentContainerHeight) / 2
            , offset = this.templateName == 'sign' ? -100 : 0;

        this.$contentContainer.css('top', top + offset);
    };

    ErrorPage.prototype.initialize = function () {
        var self = this;

        this.centerContent();
        this.$container.on('resize', function (e) {
            e.preventDefault();
            e.stopPropagation();
            self.centerContent();
        });
    };

    // hack to make sure content stays centered >_<
    $(window).on('resize', function() {
        $('body').trigger('resize')
    });

    var ep = new ErrorPage('body', "404", "background");
    ep.initialize();
</script>
</body>
</html>
