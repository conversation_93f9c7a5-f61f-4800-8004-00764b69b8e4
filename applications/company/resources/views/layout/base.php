<?php

use Core\StaticAccessors\{App, Config};

$asset = $this->asset;

if ($this->shared('legacy_assets', true)) {
    $asset->style('foundation');
    $asset->style('custom');
    $asset->style('motion-ui');
    $asset->script('jquery')->name('jquery')->defer(false);
    $asset->script('what-input')->name('what-input')->defer(false);
    $asset->script('foundation.min')->name('foundation')->defer(false);
    $foundation_js =<<<JS
$(document).foundation();
JS;
    $asset->rawScript($foundation_js)->name('foundation-boot');
}

$fx_url = [
    'BASE' => $this->uri->base() . '/',
    'assets' => [
        'BASE' => $this->asset->uri('base') . '/',
        'IMAGE' => $this->asset->uri('image') . '/',
        'SCRIPT' => $this->asset->uri('script') . '/',
        'STYLE' => $this->asset->uri('style') . '/',
        'VENDOR' => $this->asset->uri('vendor') . '/',
        'ANIMATION' => $this->asset->uri('animation') . '/',
    ],
    'API' => $this->uri->createRaw('api')->build() . '/',
    'API_V1' => $this->uri->createRaw('api/v1')->build() . '/'
];
$fx_url = json_encode($fx_url);
$asset->rawScript("window.fx_url = {$fx_url};")->name('fx-vars');

$debug_enabled = App::debugEnabled();
$gtm_id = Config::get('google.tag_manager_id');

$sentry_enabled = Config::get('log.sentry.enabled', false);
$sentry_dsn = Config::get('log.sentry.dsn', '');

if ($sentry_enabled && $sentry_dsn) {
    $env = SERVER_ROLE ?? 'unknown';
    $tracesRate = $env === 'PROD' ? 0.3 : ($env === 'STAGING' ? 0.1 : 0.0);
    $sessionRate = $env === 'PROD' ? 0.1 : 0.0;
    $errorReplayRate = in_array($env, ['PROD', 'STAGING']) ? 1.0 : 0.0;
}

?>
<!DOCTYPE html>
<html class="no-js" lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, user-scalable=1.0">
    <?php if ($this->hasShared('format-detection')): ?>
    <meta name="format-detection" content="<?=$this->shared('format-detection')?>">
    <?php endif; ?>
    <title><?=$this->get('page_title', ($title ?? 'Application'))?></title>

    <?php if (!$debug_enabled && $gtm_id !== null): ?>
        <!-- Google Tag Manager -->
        <script>
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','<?=$gtm_id?>');
        </script>
        <!-- End Google Tag Manager -->
    <?php endif; ?>

    <?php if ($sentry_enabled && $sentry_dsn): ?>
        <!-- Sentry JS SDK -->
        <script
                src="https://browser.sentry-cdn.com/7.114.0/bundle.tracing.replay.min.js"
                crossorigin="anonymous"
                onload="
                        Sentry.init({
                            dsn: '<?= $sentry_dsn ?>',
                            environment: '<?= $env ?>-frontend',
                            integrations: [new Sentry.BrowserTracing(), new Sentry.Replay()],
                            tracesSampleRate: <?= $tracesRate ?>,
                            replaysSessionSampleRate: <?= $sessionRate ?>,
                            replaysOnErrorSampleRate: <?= $errorReplayRate ?>
                        });
                        Sentry.setTag('platform', 'frontend');
                        "
        ></script>
        <!-- End Sentry -->
    <?php endif; ?>


<?=$this->asset->output('head_end')?>
</head>
<body>
<?php if (!$debug_enabled && $gtm_id !== null): ?>
<!-- Google Tag Manager (noscript) -->
<noscript>
    <iframe src="https://www.googletagmanager.com/ns.html?id=<?=$gtm_id?>" height="0" width="0"
            style="display:none;visibility:hidden"></iframe>
</noscript>
<!-- End Google Tag Manager (noscript) -->
<?php endif; ?>
<?=$this->asset->output('body_begin')?>
<?=$this->getRaw('content')?>
<?=$this->asset->output('body_end')?>
</body>
</html>