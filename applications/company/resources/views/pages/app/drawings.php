<?php

$this->extend('layout.app');

$asset = $this->asset;
$manifest = $asset->manifest('module:drawing');

$manifest->style("css/{$this->domain->brand()->slug}");
$asset->scriptData('drawing_data', [
    'auth_url' => $this->uri->routeRaw('page.auth.login')->build(),
    'project_management_url' => $this->uri->route('page.app.projects', ['path' => '']) . '/{project_id}'
]);
$manifest->script('vendor');
$manifest->script('module');
$manifest->svg('sprite');

