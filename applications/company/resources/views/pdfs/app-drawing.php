<!DOCTYPE html>
<html>
<head>
    <title>Drawing</title>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: DejaVu Serif;
            /*font-size: 13px;*/
        }

        .mainTable {
            border-collapse: collapse;
        }

        .drawingTable {
            border-collapse: collapse;
            border: 1px solid black;
            width: 112%;
        }

        .drawingImage {
            width: 720px;
            height: 540px;
        }

        .drawingImageRow {
            text-align: center;
            width: 80%;
        }

        .legendTableRow {
            width: 20%;
            border-left: 1px solid;
            vertical-align: top;
        }

        .legendTitleHeader {
            vertical-align: middle;
            font-size: 16px;
            padding-bottom: 10px;
            padding-left: 20px;
        }

        .legendTitleCell {
            padding-left: 10px;
            font-size: 16px;
        }

        .legendIconCell {
            text-align: right;
        }

        .legendIcon {
            background-repeat: no-repeat !important;
            width: 15px;
            height: 15px;
        }

        .customerTable, .authorTable, .companyTable {
            border-collapse: collapse;
            width: 100%;
        }

        .customerTable td, .authorTable td, .companyInfo td {
            /*border-bottom: 1px solid black;*/
        }

        .blankCellAboveLogo {
            border-left: solid 1px black;
            border-right: solid 1px black;
        }

        .companyLogoImage {
            position: relative;
            margin-top: -25px;
            max-width: 130px;
            max-height: 130px;
        }

        .drawingInfo {
            width: 112%;
            border-collapse: collapse;
            border-right: 1px solid black;
            border-left: 1px solid black;
            border-bottom: 1px solid black;
        }

        .drawingTitle {
            text-align: center;
            font-size: 15px;
            font-weight: normal;
        }

        .authorInfo, .customerInfo, .companyInfo {
            border-right: 1px solid black;
            border-top: 1px solid black;
            width: 26.66%;
        }

        .authorTable, .customerTable {
            font-size: 14px;
        }

        .author, .customer, .company {
            padding-left: 10px;
        }

        .companyTable {
            font-size: 14px;
        }

        .companyLogo {
            text-align: center;
            width: 20%;
        }

    </style>
</head>
<body>
<table class="mainTable" cellpadding="0" cellspacing="0">
    <tr>
        <td>
            <table class="drawingTable">
                <tr>
                    <td class="drawingImageRow">
                        <img class="drawingImage" src="<?= $path ?>" alt=""/>
                    </td>
                    <td class="legendTableRow">
                        <table class="legendTable">
                            <tr>
                                <th class="legendTitleHeader" colspan="2">LEGEND</th>
                            </tr>
                            <?php foreach ($legendItems as $legendItem): ?>
                                <tr>
                                    <td class="legendTitleCell"><?= $legendItem['title'] ?></td>
                                    <td class="legendIconCell">
                                        <img class="<?= $legendItem['class'] ?> legendIcon"
                                             src="<?= $legendItem['src'] ?>"/>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </table>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
    <tr>
        <td>
            <table class="drawingInfo">
                <tr>
                    <th class="drawingTitle" colspan="3"><?= $drawing['drawingName'] ?></th>
                    <td class="blankCellAboveLogo"></td>
                </tr>
                <tr>
                    <td class="authorInfo">
                        <table class="authorTable">
                            <tr>
                                <td class="author">
                                    <strong>Created By:</strong><br>
                                    <?= $author['name'] ?><br>
                                    <?= $author['phone'] ?><br>
                                    <br>
                                </td>
                            </tr>
                        </table>
                    </td>
                    <td class="customerInfo">
                        <table class="customerTable">
                            <tr>
                                <td class="customer">
                                    <strong>Project Address</strong><br>
                                    <?= $customer['name'] ?><br>
                                    <?= $customer['address'] ?><br>
                                    <?= $customer['city'] ?>, <?= $customer['state'] ?> <?= $customer['zip'] ?>
                                </td>
                            </tr>
                        </table>
                    </td>
                    <td class="companyInfo">
                        <table class="companyTable">
                            <tr>
                                <td class="company" colspan="2">
                                    <strong><?= $company['name'] ?></strong><br>
                                    <?= $company['address'] ?><br>
                                    <?= $company['city'] ?>, <?= $company['state'] ?> <?= $company['zip'] ?><br>
                                    <?= $company['website'] ?>
                                </td>
                            </tr>
                        </table>
                    </td>
                    <td class="companyLogo">
                        <img class="companyLogoImage" src="<?= $company['logo'] ?>"/>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>
</body>
</html>