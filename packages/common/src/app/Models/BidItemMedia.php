<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class BidItemMedia extends HistoryEntityModel implements Interfaces\BidItemMediaInterface
{
    use Common\BidItemMediaCommon;

    protected $table = 'bidItemMedia';
    protected $primaryKey = 'bidItemMediaID';
    protected $fillable = [
        'bidItemMediaID', 'bidItemID', 'type', 'itemID', 'order', 'createdByUserID', 'updatedByUserID', 'deletedAt',
        'deletedByUserID'
    ];
}
