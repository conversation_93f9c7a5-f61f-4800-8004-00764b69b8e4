<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class BidItemSection extends HistoryEntityModel implements Interfaces\BidItemSectionInterface
{
    use Common\BidItemSectionCommon;

    protected $table = 'bidItemSections';
    protected $primaryKey = 'bidItemSectionID';
    protected $fillable = [
        'bidItemSectionID', 'bidItemID', 'name', 'order', 'createdByUserID', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
}
