<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Models\CompanyPaymentMethod;

trait CompanyAchPaymentMethodCommon
{
    protected $castMap = [
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function paymentMethod()
    {
        return $this->morphOne(CompanyPaymentMethod::class, 'paymentMethod', 'itemType', 'itemID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        $query->join('companyPaymentMethods', function ($join) {
            $join->on('companyPaymentMethods.itemID', '=', "{$this->table}.companyCreditCardPaymentMethodID")
                ->where('companyPaymentMethods.type', CompanyPaymentMethod::TYPE_ACH);
        });
        return $query->where('companyPaymentMethods.companyID', $company);
    }
}
