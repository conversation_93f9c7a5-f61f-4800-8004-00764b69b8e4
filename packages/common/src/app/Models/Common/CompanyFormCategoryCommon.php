<?php

namespace Common\Models\Common;

use Common\Models\CompanyFormItem;
use Common\Models\Company;
use Common\Models\FormType;
use Common\Models\Interfaces\CompanyFormCategoryInterface;
use Common\Models\Pivots\CompanyFormCategoryItem;
use Common\Traits\DB\ScopeSearchTrait;
use Common\Traits\DB\UuidTrait;

trait CompanyFormCategoryCommon
{
    use ScopeSearchTrait;
    use UuidTrait;

    protected $castMap = [
        'companyID' => 'int',
        'type' => 'int',
        'status' => 'int',
        'order' => 'int',
        'archivedByUserID' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];
    protected $searchColumns = ['name'];

    public function children()
    {
        return $this->hasMany(static::class, 'parentCompanyFormCategoryID');
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'companyID', 'companyID');
    }

    public function formType()
    {
        return $this->belongsTo(FormType::class, 'type', 'formTypeID');
    }

    public function items()
    {
        return $this->belongsToMany(
            CompanyFormItem::class, 'companyFormCategoriesItems', 'companyFormCategoryID',
            'companyFormItemID'
        )
            ->using(CompanyFormCategoryItem::class)
            ->withPivot('companyFormCategoryItemID', 'createdByUserID', 'updatedByUserID', 'deletedByUserID')
            ->withTimestamps()
            ->whereNull('companyFormCategoriesItems.deletedAt');
    }

    public function parent()
    {
        return $this->belongsTo(static::class, 'parentCompanyFormCategoryID', 'companyFormCategoryID');
    }

    public function scopeActive($query)
    {
        return $query->where("{$this->table}.status", CompanyFormCategoryInterface::STATUS_ACTIVE);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy("{$this->table}.order", 'asc')->orderBy("{$this->table}.name", 'asc');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->where('companyID', $company);
    }
}
