<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Traits\DB\UuidTrait;

trait ContentPartialCommon
{
    use UuidTrait;

    protected $castMap = [
        'companyID' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function company()
    {
        return $this->belongsTo(Company::class, 'companyID', 'companyID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->where('companyID', $company);
    }
}
