<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Models\FormItemEntry;
use Common\Models\FormItemEntryGroup;
use Common\Models\FormItemEntryGroupFieldFile;
use Common\Models\FormItemEntryGroupFieldOption;
use Common\Models\FormItemEntryGroupFieldProduct;
use Common\Models\FormItemEntryGroupFieldValue;
use Common\Models\FormItemGroup;
use Common\Traits\DB\UuidTrait;

trait FormItemEntryGroupCommon
{
    use UuidTrait;

    protected $castMap = [
        'index' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function children()
    {
        return $this->hasMany(FormItemEntryGroup::class, 'parentFormItemEntryGroupID');
    }

    public function entry()
    {
        return $this->belongsTo(FormItemEntry::class, 'formItemEntryID', 'formItemEntryID');
    }

    public function fieldFiles()
    {
        return $this->hasMany(FormItemEntryGroupFieldFile::class, 'formItemEntryGroupID');
    }

    public function fieldOptions()
    {
        return $this->hasMany(FormItemEntryGroupFieldOption::class, 'formItemEntryGroupID');
    }

    public function fieldProducts()
    {
        return $this->hasMany(FormItemEntryGroupFieldProduct::class, 'formItemEntryGroupID');
    }

    public function fieldValues()
    {
        return $this->hasMany(FormItemEntryGroupFieldValue::class, 'formItemEntryGroupID');
    }

    public function group()
    {
        return $this->belongsTo(FormItemGroup::class, 'formItemGroupID', 'formItemGroupID');
    }

    public function parent()
    {
        return $this->belongsTo(FormItemEntryGroup::class, 'parentFormItemEntryGroupID', 'formItemEntryGroupID');
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy("{$this->table}.index", 'asc')->orderBy("{$this->table}.createdAt", 'asc');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->join('formItemEntries', 'formItemEntries.formItemEntryID', "{$this->table}.formItemEntryID")
            ->join('companyFormItems', function ($join) {
                $join->where('formItemEntries.formType', FormItemEntry::FORM_TYPE_COMPANY)
                    ->on('companyFormItems.companyFormItemID', '=', 'formItemEntries.formID');
            })
            ->where('companyFormItems.companyID', $company);
    }
}
