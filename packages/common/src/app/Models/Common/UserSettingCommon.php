<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Models\User;

trait UserSettingCommon
{
    protected $castMap = [
        'userID' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'userID', 'userID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        $query->join('user', 'user.userID', '=', "{$this->table}.userID");
        return $query->where('user.companyID', $company);
    }
}
