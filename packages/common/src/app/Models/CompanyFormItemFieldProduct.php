<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyFormItemFieldProduct extends Model implements Interfaces\FormItemGroupFieldProductInterface
{
    use SoftDeletes;
    use UuidTrait;

    protected $table = 'companyFormItemFieldProducts';
    protected $primaryKey = 'companyFormItemFieldProductID';
    protected $fillable = [
        'companyFormItemFieldProductID', 'companyFormItemFieldID', 'itemType', 'itemID', 'action', 'order', 'createdByUserID',
        'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
    protected $casts = [
        'itemType' => 'int',
        'action' => 'int',
        'order' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public static function getActions()
    {
        return [
            static::ACTION_ADD => 'Add',
            static::ACTION_REMOVE => 'Remove'
        ];
    }

    public function item()
    {
        return $this->morphTo('item', 'itemType', 'itemID');
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy("{$this->table}.order", 'asc')->orderBy("{$this->table}.createdAt", 'asc');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        $query->join('companyFormItemFields', 'companyFormItemFields.companyFormItemFieldID', '=', "{$this->table}.companyFormItemFieldID")
            ->join('companyFormItems', 'companyFormItems.companyFormItemID', 'companyFormItemFields.companyFormItemID');
        return $query->where('companyFormItems.companyID', $company);
    }
}
