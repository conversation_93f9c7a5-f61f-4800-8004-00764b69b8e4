<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Models\Pivots\CompanyIntakeFeature;

class CompanyIntake extends Model
{
    protected $table = 'companyIntake';
    protected $primaryKey = 'companyIntakeID';
    protected $fillable = [
        'companyIntakeID', 'companyID', 'intakeUserCountID', 'intakeIndustryID', 'intakeMarketingSourceID'
    ];
    protected $casts = [
        'intakeUserCountID' => 'int',
        'intakeIndustryID' => 'int',
        'intakeMarketingSourceID' => 'int'
    ];

    public function company()
    {
        return $this->belongsTo(Company::class, 'companyID', 'companyID');
    }

    public function features()
    {
        return $this->belongsToMany(IntakeFeature::class, 'companyIntakeFeatures', 'companyIntakeID', 'intakeFeatureID')
            ->using(CompanyIntakeFeature::class)
            ->withTimestamps();
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->where("{$this->table}.companyID", $company);
    }
}
