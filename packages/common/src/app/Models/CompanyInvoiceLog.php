<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyInvoiceLog extends Model
{
    use SoftDeletes;

    protected $table = 'companyInvoiceLog';
    protected $primaryKey = 'companyInvoiceLogID';
    protected $casts = [
        'companyInvoiceID' => 'int'
    ];
    protected $fillable = [
        'companyInvoiceID', 'message', 'createdAt', 'updatedAt'
    ];

    public $timestamps = false;

    public function invoice()
    {
        return $this->belongsTo(CompanyInvoice::class, 'companyInvoiceID', 'companyInvoiceID');
    }
}
