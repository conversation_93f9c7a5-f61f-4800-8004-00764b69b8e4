<?php

namespace Common\Models;

use Common\Classes\DB\Model;

class CompanySetup extends Model
{
    protected $table = 'companySetup';
    protected $primaryKey = 'companySetupID';

    protected $fillable = [
        'companySetupID', 'companyID', 'industryProductItemsImport', 'industryProductItemsImportCompletedAt',
        'instruction', 'instructionCompletedAt', 'general', 'generalCompletedAt', 'users', 'usersCompletedAt',
        'bidCustomization', 'bidCustomizationCompletedAt', 'emails', 'emailsCompletedAt', 'termsConditions',
        'termsConditionsCompletedAt', 'products', 'productsCompletedAt', 'media', 'mediaCompletedAt', 'warrantyPacket',
        'warrantyPacketCompletedAt', 'quickbooksOnline', 'quickbooksOnlineCompletedAt', 'googleCalendar',
        'googleCalendarCompletedAt',   'additionalServices', 'isProductsServices', 'isTeamTraining',
        'isAdditionalSupport', 'isPreviousCustomerData', 'additionalServicesCompletedAt', 'setupCompleteCompletedAt',
        'setupComplete', 'createdAt', 'createdByUserID', 'updatedAt', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
    protected $casts = [
        'companySetupID' => 'int',
        'companyID' => 'int',
        'industryProductItemsImport' => 'bool',
        'instruction' => 'bool',
        'general' => 'bool',
        'users' => 'bool',
        'bidCustomization' => 'bool',
        'emails' => 'bool',
        'termsConditions' => 'bool',
        'products' => 'bool',
        'media' => 'bool',
        'warrantyPacket' => 'bool',
        'quickbooksOnline' => 'bool',
        'googleCalendar' => 'bool',
        'additionalServices' => 'bool',
        'isProductsServices' => 'bool',
        'isTeamTraining' => 'bool',
        'isAdditionalSupport' => 'bool',
        'isPreviousCustomerData' => 'bool',
        'setupComplete' => 'bool',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];
    protected $dates = [
        'industryProductItemsImportCompletedAt', 'instructionCompletedAt', 'generalCompletedAt', 'usersCompletedAt',
        'bidCustomizationCompletedAt', 'emailsCompletedAt', 'termsConditionsCompletedAt', 'productsCompletedAt',
        'mediaCompletedAt', 'warrantyPacketCompletedAt', 'quickbooksOnlineCompletedAt', 'googleCalendarCompletedAt',
        'additionalServicesCompletedAt', 'setupCompleteCompletedAt'
    ];

    public function company()
    {
        return $this->belongsTo(Company::class, 'companyID', 'companyID');
    }

    public function productItems()
    {
        return $this->hasMany(CompanySetupProductItem::class, 'companySetupID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->where("{$this->table}.companyID", $company);
    }
}
