<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyTrainingSectionModule extends Model
{
    use SoftDeletes;

    protected $table = 'companiesTrainingSectionModules';
    protected $primaryKey = 'companyTrainingSectionModuleID';
    protected $fillable = [
        'companyID', 'trainingSectionModuleID', 'isCompleted', 'completedAt'
    ];
    protected $casts = [
        'companyID' => 'int',
        'trainingSectionModuleID' => 'int',
        'isCompleted' => 'bool'
    ];
    protected $dates = ['completedAt'];

    public function actions()
    {
        return $this->hasMany(CompanyTrainingSectionModuleAction::class, 'companyTrainingSectionModuleID');
    }
}
