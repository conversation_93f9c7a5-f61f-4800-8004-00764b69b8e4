<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;
use Common\Interfaces\DB\ScopeSearchInterface;

class EmailTemplate extends HistoryEntityModel implements Interfaces\EmailTemplateInterface, ScopeSearchInterface
{
    use Common\EmailTemplateCommon;

    protected $table = 'emailTemplates';
    protected $primaryKey = 'emailTemplateID';
    protected $fillable = [
        'emailTemplateID', 'ownerType', 'ownerID', 'source', 'type', 'name', 'subject', 'content',
        'isSendFromSalesperson', 'status', 'archivedAt', 'archivedByUserID', 'createdByUserID', 'updatedByUserID',
        'deletedAt', 'deletedByUserID'
    ];
}
