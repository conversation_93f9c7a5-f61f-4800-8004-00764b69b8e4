<?php

declare(strict_types=1);

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class GoogleCalendar extends Model
{
    use SoftDeletes;
    use UuidTrait;

    public const OWNER_TYPE_USER = 28;
    public const OWNER_TYPE_COMPANY = 5;

    public const STATUS_ADDED = 1;
    public const STATUS_REMOVING = 2;
    public const STATUS_REMOVED = 3;
    public const STATUS_REMOVE_FAILED = 4;

    public const PULL_STATUS_NEVER = 1;
    public const PULL_STATUS_PULLING = 2;
    public const PULL_STATUS_PULLED = 3;
    public const PULL_STATUS_FAILED = 4;

    public const PUSH_STATUS_NEVER = 1;
    public const PUSH_STATUS_PUSHING = 2;
    public const PUSH_STATUS_PUSHED = 3;
    public const PUSH_STATUS_FAILED = 4;

    protected $table = 'googleCalendars';
    protected $primaryKey = 'googleCalendarID';
    protected $fillable = [
        'googleCalendarID', 'googleServiceID', 'ownerType', 'ownerID', 'status', 'calendarID', 'name',
        'pullWindowDaysBefore', 'pullWindowDaysAfter', 'pullWindowExpandWaitDays', 'pullAfterMinutes',
        'pullWindowStartAt', 'pullWindowEndAt', 'pullStatus', 'pullSyncToken', 'lastPullAt',
        'pushWindowDaysBefore', 'pushWindowDaysAfter', 'pushWindowExpandWaitDays', 'pushWindowStartAt',
        'pushWindowEndAt', 'pushStatus', 'lastPushAt', 'removedAt'
    ];
    protected $casts = [
        'ownerType' => 'int',
        'ownerID' => 'int',
        'status' => 'int',
        'pullWindowDaysBefore' => 'int',
        'pullWindowDaysAfter' => 'int',
        'pullWindowExpandWaitDays' => 'int',
        'pullAfterMinutes' => 'int',
        'pullStatus' => 'int',
        'pushWindowDaysBefore' => 'int',
        'pushWindowDaysAfter' => 'int',
        'pushWindowExpandWaitDays' => 'int',
        'pushStatus' => 'int'
    ];
    protected $dates = [
        'pullWindowStartAt', 'pullWindowEndAt', 'lastPullAt', 'pushWindowStartAt', 'pushWindowEndAt', 'lastPushAt',
        'removedAt'
    ];

    protected static function boot()
    {
        parent::boot();
        static::deleting(function (self $calendar) {
            $calendar->notificationChannels()->delete();
        });
    }

    public function notificationChannels()
    {
        return $this->hasMany(GoogleCalendarNotificationChannel::class, 'googleCalendarID');
    }

    public function service()
    {
        return $this->belongsTo(GoogleService::class, 'googleServiceID', 'googleServiceID');
    }

    public function oAuthUser()
    {
        return $this->belongsTo(User::class, 'OAuthUserID', 'userID');
    }

    public function scopeWithOAuthUser(object $query): object
    {
        return $query->join('googleServices', 'googleServices.googleServiceID', '=', "{$this->table}.googleServiceID")
            ->join('googleOAuth', 'googleOAuth.googleOAuthID', '=', 'googleServices.googleOAuthID')
            ->select(["{$this->table}.*", 'googleOAuth.userID as OAuthUserID'])
            ->with(['oAuthUser']);
    }
}
