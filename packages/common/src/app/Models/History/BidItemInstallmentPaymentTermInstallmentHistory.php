<?php

namespace Common\Models\History;

use Common\Classes\DB\HistoryModel;
use Common\Models\Common\BidItemInstallmentPaymentTermInstallmentCommon;
use Common\Models\Interfaces\BidItemInstallmentPaymentTermInstallmentInterface;

class BidItemInstallmentPaymentTermInstallmentHistory extends HistoryModel implements BidItemInstallmentPaymentTermInstallmentInterface
{
    use BidItemInstallmentPaymentTermInstallmentCommon;

    protected $table = 'bidItemInstallmentPaymentTermInstallmentsHistory';
    protected $primaryKey = 'bidItemInstallmentPaymentTermInstallmentsHistoryID';
    protected $entityPrimaryKey = 'bidItemInstallmentPaymentTermInstallmentID';
}
