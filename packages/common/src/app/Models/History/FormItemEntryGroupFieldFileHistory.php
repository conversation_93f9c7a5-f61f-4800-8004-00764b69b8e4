<?php

namespace Common\Models\History;

use Common\Classes\DB\HistoryModel;
use Common\Models\Common\FormItemEntryGroupFieldFileCommon;
use Common\Models\Interfaces\FormItemEntryGroupFieldFileInterface;

class FormItemEntryGroupFieldFileHistory extends HistoryModel implements FormItemEntryGroupFieldFileInterface
{
    use FormItemEntryGroupFieldFileCommon;

    protected $table = 'formItemEntryGroupFieldFilesHistory';
    protected $primaryKey = 'formItemEntryGroupFieldFilesHistoryID';
    protected $entityPrimaryKey = 'formItemEntryGroupFieldFileID';
}
