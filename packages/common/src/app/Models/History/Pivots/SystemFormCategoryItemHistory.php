<?php

namespace Common\Models\History\Pivots;

use Common\Classes\DB\HistoryPivot;
use Common\Models\Common\SystemFormCategoryItemCommon;
use Common\Models\Interfaces\SystemFormCategoryItemInterface;

class SystemFormCategoryItemHistory extends HistoryPivot implements SystemFormCategoryItemInterface
{
    use SystemFormCategoryItemCommon;

    protected $table = 'systemFormCategoriesItemsHistory';
    protected $primaryKey = 'systemFormCategoriesItemsHistoryID';
    protected $entityPrimaryKey = 'systemFormCategoryItemID';
}
