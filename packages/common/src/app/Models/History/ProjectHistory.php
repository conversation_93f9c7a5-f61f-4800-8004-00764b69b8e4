<?php

namespace Common\Models\History;

use Common\Classes\DB\HistoryModel;
use Common\Interfaces\DB\ScopeSearchInterface;
use Common\Models\Common\ProjectCommon;
use Common\Models\Interfaces\ProjectInterface;

class ProjectHistory extends HistoryModel implements ProjectInterface, ScopeSearchInterface
{
    use ProjectCommon;

    protected $table = 'projectHistory';
    protected $primaryKey = 'projectHistoryID';
    protected $entityPrimaryKey = 'projectID';
}
