<?php

namespace Common\Models\Interfaces;

/**
 * Interface BidItemInterface
 *
 * @package Common\Models\Interfaces
 */
interface BidItemInterface
{
    const TYPE_GUIDED = 1;
    const TYPE_LEGACY = 2;

    const FOLLOW_UP_NOTIFICATION_STATUS_DISABLED = 1;
    const FOLLOW_UP_NOTIFICATION_STATUS_ENABLED = 2;
    const FOLLOW_UP_NOTIFICATION_STATUS_COMPLETED = 3;

    // note: do not rely on the ordering of these constants to determine where a bid is in a process
    const STATUS_INCOMPLETE = 1;
    const STATUS_SUBMITTED = 5;
    const STATUS_FINALIZED = 2;
    const STATUS_ACCEPTED = 3;
    const STATUS_CANCELLED = 4;
    const STATUS_EXPIRED = 6;

    const IP_ADDRESS_TYPE_V4 = 'IPv4';
    const IP_ADDRESS_TYPE_V6 = 'IPv6';

    public function content();

    public function createdByUser();

    public function customDrawings();

    public function drawings();

    public function file();

    public function lineItems();

    public function media();

    public function paymentTerms();

    public function project();

    public function scopeOfWorkFile();

    public function sections();

    public function submittedByUser();

    public function scopeWithProperty($query);

    public function scopeWithCustomer($query);

    public function scopeOfCompany($query, $company);

    public function scopeWithLegacyEvaluation($query);
}
