<?php

namespace Common\Models\Interfaces;

/**
 * Interface ProductItemAdditionalCostInterface
 *
 * @package Common\Models\Interfaces
 */
interface ProductItemAdditionalCostInterface
{
    const STATUS_ACTIVE = 1;
    const STATUS_ARCHIVED = 2;

    public function additionalCost();

    public function productItem();

    public function scopeOrdered($query);

    public function scopeOfCompany($query, $company);
}
