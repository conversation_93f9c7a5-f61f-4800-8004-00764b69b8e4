<?php

namespace Common\Models\Pivots;

use Common\Classes\DB\Pivot;
use Common\Models\CompanyTrainingSectionModuleAction;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyTrainingSectionModule extends Pivot
{
    use SoftDeletes;

    protected $table = 'companiesTrainingSectionModules';
    protected $primaryKey = 'companyTrainingSectionModuleID';
    protected $fillable = [
        'companyID', 'trainingSectionModuleID', 'isCompleted', 'completedAt'
    ];
    protected $casts = [
        'companyID' => 'int',
        'trainingSectionModuleID' => 'int',
        'isCompleted' => 'bool'
    ];
    protected $dates = ['completedAt'];

    public function actions()
    {
        return $this->hasMany(CompanyTrainingSectionModuleAction::class, 'companyTrainingSectionModuleID');
    }
}
