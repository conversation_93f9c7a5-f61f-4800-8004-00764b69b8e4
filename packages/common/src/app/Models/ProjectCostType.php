<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProjectCostType extends Model
{
    use SoftDeletes;

    const TYPE_GENERAL = 6;
    const TYPE_COMMISSION = 7;

    protected $table = 'projectCostTypes';
    protected $primaryKey = 'projectCostTypeID';
    protected $dates = [
        'createdAt',
        'updatedAt'
    ];
    protected $casts = [
        'companyID' => 'int',
        'canDelete' => 'bool',
        'projectCostCategoryID' => 'int'
    ];

    protected $fillable = [
        'companyID', 'type', 'projectCostCategoryID', 'title', 'canDelete', 'createdAt', 'createdByUserID', 'updatedAt', 'updatedByUserID'
    ];

    public $timestamps = false;

    public function company()
    {
        return $this->belongsTo(Company::class, 'companyID', 'companyID');
    }
}
