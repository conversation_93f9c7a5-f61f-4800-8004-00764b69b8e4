<?php

namespace Common\Models;

use Common\Classes\DB\Model;

class ResellerPhone extends Model
{
    protected $table = 'resellerPhones';
    protected $primaryKey = 'resellerPhoneID';
    protected $fillable = [
        'resellerID', 'phoneNumber', 'isPrimary', 'description'
    ];
    protected $casts = [
        'resellerPhoneID' => 'int',
        'isPrimary' => 'bool'
    ];

    public function reseller()
    {
        return $this->belongsTo(Reseller::class, 'resellerID', 'resellerID');
    }
}
