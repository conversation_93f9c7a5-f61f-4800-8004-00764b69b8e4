<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;
use Common\Interfaces\DB\ScopeSearchInterface;

class SystemFormCategory extends HistoryEntityModel implements Interfaces\SystemFormCategoryInterface, ScopeSearchInterface
{
    use Common\SystemFormCategoryCommon;

    protected $table = 'systemFormCategories';
    protected $primaryKey = 'systemFormCategoryID';
    protected $fillable = [
        'systemFormCategoryID', 'parentSystemFormCategoryID', 'type', 'status', 'name', 'order', 'archivedAt',
        'archivedByUserID', 'createdByUserID', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
}
