<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class TextMessage extends Model
{
    use SoftDeletes;
    use UuidTrait;

    const MESSAGE_TYPE_OUTBOUND = 1;
    const MESSAGE_TYPE_INBOUND = 2;

    protected $table = 'textMessages';
    protected $primaryKey = 'textMessageID';
    protected $fillable = [
        'textMessageID', 'messageType', 'type', 'itemID', 'body'
    ];
    protected $casts = [
        'messageType' => 'int',
        'type' => 'int'
    ];

    public function numbers()
    {
        return $this->hasMany(TextMessageNumber::class, 'textMessageID');
    }

    public function recipients()
    {
        return $this->hasMany(TextMessageRecipient::class, 'textMessageID');
    }
}
