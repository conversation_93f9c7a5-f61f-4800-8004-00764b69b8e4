<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TrainingSection extends Model
{
    use SoftDeletes;

    protected $table = 'trainingSections';
    protected $primaryKey = 'trainingSectionID';
    protected $fillable = [
        'title', 'content', 'order'
    ];
    protected $casts = [
        'order' => 'int'
    ];

    public function modules()
    {
        return $this->hasMany(TrainingSectionModule::class, 'trainingSectionID');
    }
}
