<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Models\Pivots\CompanyTrainingSectionModule;
use Common\Models\Pivots\TrainingActionSectionModule;
use Common\Models\Pivots\TrainingSectionModuleUser;
use Illuminate\Database\Eloquent\SoftDeletes;

class TrainingSectionModule extends Model
{
    use SoftDeletes;

    public const ACCESS_LEVEL_COMPANY = 1;
    public const ACCESS_LEVEL_ALL_USERS = 2;
    public const ACCESS_LEVEL_PRIMARY_USER = 3;
    public const ACCESS_LEVEL_NON_PRIMARY_USER = 4;

    public const STATUS_ACTIVE = 1;
    public const STATUS_INACTIVE = 2;

    protected $table = 'trainingSectionModules';
    protected $primaryKey = 'trainingSectionModuleID';
    protected $fillable = [
        'trainingSectionID', 'title', 'cardTitle', 'cardIcon', 'alias', 'intro', 'youtubeVideoID',
        'youtubeVideoStartTime', 'imageUrl', 'content', 'tryUrl', 'helpCenterUrl', 'status', 'accessLevel',
        'isRequired', 'isHidden', 'requiredActionCount', 'order'
    ];
    protected $casts = [
        'youtubeVideoStartTime' => 'int',
        'status' => 'int',
        'accessLevel' => 'int',
        'isRequired' => 'bool',
        'isHidden' => 'bool',
        'requiredActionCount' => 'int',
        'order' => 'int'
    ];

    public function actions()
    {
        return $this->belongsToMany(TrainingAction::class, 'trainingActionsSectionModules', 'trainingSectionModuleID', 'trainingActionID')
            ->using(TrainingActionSectionModule::class)
            ->withTimestamps();
    }

    public function companies()
    {
        return $this->belongsToMany(Company::class, 'companiesTrainingSectionModules', 'trainingSectionModuleID', 'companyID')
            ->using(CompanyTrainingSectionModule::class)
            ->withPivot('isCompleted')
            ->withTimestamps();
    }

    public function section()
    {
        return $this->belongsTo(TrainingSection::class, 'trainingSectionID', 'trainingSectionID');
    }

    public function users()
    {
        return $this->belongsToMany(User::class, 'trainingSectionModulesUsers', 'trainingSectionModuleID', 'userID')
            ->using(TrainingSectionModuleUser::class)
            ->withPivot('isCompleted')
            ->withTimestamps();
    }
}
