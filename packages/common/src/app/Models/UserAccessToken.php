<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserAccessToken extends Model
{
    use SoftDeletes;
    use UuidTrait;

    protected $table = 'userAccessTokens';
    protected $primaryKey = 'userAccessTokenID';
    protected $casts = [
        'userID' => 'int',
        'isActive' => 'bool'
    ];
    protected $fillable = [
        'userAccessTokenID', 'userID', 'token', 'expiresAt', 'lastLoginAt', 'isActive'
    ];
    protected $dates = ['expiresAt', 'lastLoginAt'];

    public function user()
    {
        return $this->belongsTo(User::class, 'userID', 'userID');
    }

    public function scopeActive(object $query, $active = true)
    {
        return $query->where('isActive', $active);
    }
}
