<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;

class UserWebApiToken extends Model
{
    use UuidTrait;

    protected $table = 'userWebApiTokens';
    protected $primaryKey = 'userWebApiTokenID';
    protected $fillable = [
        'userWebApiTokenID', 'deviceID', 'userAgent', 'userAgentHash'
    ];

    public function token()
    {
        return $this->morphOne(UserApiToken::class, 'token', 'tokenType', 'tokenTypeID');
    }
}
