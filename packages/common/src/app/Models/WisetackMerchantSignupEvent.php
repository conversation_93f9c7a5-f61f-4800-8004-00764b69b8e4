<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Ramsey\Uuid\Uuid;

class WisetackMerchantSignupEvent extends Model
{
    use UuidTrait;

    protected $table = 'wisetackMerchantSignupEvents';
    protected $primaryKey = 'wisetackMerchantSignupEventID';
    protected $fillable = ['wisetackMerchantSignupEventID', 'wisetackMerchantID', 'event', 'eventTimestamp',
                           'externalID', 'payload'];

    public static function boot()
    {
        parent::boot();

        self::creating(function ($model) {
            $uuid = Uuid::uuid4();
            $model->wisetackMerchantSignupEventID = $uuid->getBytes();
        });
    }

}