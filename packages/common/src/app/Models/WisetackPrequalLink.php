<?php

namespace Common\Models;

use App\Services\Wisetack\Helpers\WisetackPricingPlan;
use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Ramsey\Uuid\Uuid;

class WisetackPrequalLink extends Model
{
    use Common\WisetackPrequalLinkCommon;

    use UuidTrait;
    use SoftDeletes;

    public const STATUS = [
        'PENDING' => 1,
        'INITIATED' => 2,
        'PREQUALIFIED' => 3,
        'DECLINED' => 4,
        'EXPIRED' => 5,
    ];

    protected $table = 'wisetackPrequalLinks';
    protected $primaryKey = 'wisetackPrequalLinkID';
    protected $fillable = ['wisetackPrequalLinkID', 'wisetackPrequalID', 'wisetackMerchantID', 'customerUUID',
                           'projectID', 'status', 'pricingPlan', 'initiatedAt', 'prequalifiedAt', 'declinedAt', 'expiredAt',
                           'link', 'signupID', 'checksum', 'maxQualifiedAmount', 'expirationDate'];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $uuid = Uuid::uuid4();
            $model->wisetackPrequalLinkID = $uuid->getBytes();
            $model->status = self::STATUS['PENDING'];
        });
    }

    /**
     * Convert the model instance to an array.
     *
     * @return array
     */
    public function toArray()
    {
        $array = parent::toArray();
        $array['wisetackPrequalLinkID'] = strtoupper(bin2hex($this->wisetackPrequalLinkID));
        $array['wisetackMerchantID'] = strtoupper(bin2hex($this->wisetackMerchantID));
        $array['customerUUID'] = strtoupper(bin2hex($this->customerUUID));
        return $array;
    }


    public function getStatusAttribute($value)
    {
        $statuses = array_flip(self::STATUS);
        return $statuses[$value] ?? 'UNKNOWN';
    }

    /**
     *
     *
     * @return bool
     */
    public function isExpired(): bool
    {
        return $this->status === 'EXPIRED' ||
            ($this->expirationDate && Carbon::parse($this->expirationDate)->isPast()) ;
    }

    /**
     * Set the status and timestamp attributes based on the event
     *
     * @param $event
     * @return void
     */
    public function setStatusAttributesBasedOnEvent($event) {
        $status = self::STATUS[$event] ?? null;
        if (!$status) {
            return;
        }

        $this->status = $status;
        $eventToAttributeMap = [
            'INITIATED' => 'initiatedAt',
            'PREQUALIFIED' => 'prequalifiedAt',
            'DECLINED' => 'declinedAt',
            'EXPIRED' => 'expiredAt',
        ];

        if (isset($eventToAttributeMap[$event])) {
            $this->{$eventToAttributeMap[$event]} = Carbon::now();
        }
    }


    /**
     * Check if the transition to the new state is valid
     *
     * @param $new_state
     * @return bool
     */
    public function isValidTransition($new_state): bool {
        $status = $this::STATUS[$this->status];

        // Check if the new state is a direct next state or further along the line
        return $status <= $new_state;
    }
}