<?php

declare(strict_types=1);

namespace Common\Seeders\Project;

use App\Classes\Acl;
use App\Resources\Project\NoteResource;
use Common\Classes\Seeder;
use Common\Traits\Seeder\{CompanyTrait, ProjectTrait};
use Core\Components\Resource\Classes\Entity;

/**
 * Class NoteSeeder
 *
 * @package Common\Seeders\Project
 */
class NoteSeeder extends Seeder
{
    use CompanyTrait;
    use ProjectTrait;

    /**
     * Get nested entity which doesn't come with any identifiers
     *
     * @return array
     */
    public function nestedEntity(): array
    {
        $faker = $this->getFaker();

        return [
            'note' => $faker->paragraphs($faker->biasedNumberBetween(1, 2, 'self::linearLow'), true),
            'is_pinned' => $faker->boolean()
        ];
    }

    /**
     * Run seeder
     *
     * @throws \Core\Exceptions\AppException
     */
    public function run(): void
    {
        $entity = $this->nestedEntity();
        $entity['project_id'] = $this->getProjectID();

        $id = NoteResource::make(Acl::make())->create(Entity::make($entity))->run();

        $this->primaryKey($id);
    }
}
