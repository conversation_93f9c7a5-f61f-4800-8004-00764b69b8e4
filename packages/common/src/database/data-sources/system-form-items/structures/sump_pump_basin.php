<?php

declare(strict_types=1);

use App\Services\Form\Classes\Structure\Group\Rule\Condition;
use App\Services\Form\Components\Fields\FileField;
use App\Services\Form\Components\Fields\ProductListField;
use App\Services\Form\Components\Fields\TextareaField;
use App\Services\Form\Components\Fields\TextField;
use App\Services\Form\Components\Groups\DefaultGroup;
use App\Services\Form\Components\LayoutItems\FieldLayoutItem;
use App\Services\Form\Components\LayoutItems\TemplateLayoutItem;
use App\Services\Form\Components\Layouts\Grid\InputScreenLargeLayout;
use App\Services\Form\Components\Layouts\Grid\OutputBidDocumentLayout;
use App\Services\Form\Components\Layouts\Grid\OutputJobDocumentLayout;
use App\Services\Form\Components\Overrides\FieldOverride;
use App\Services\Form\Components\Overrides\TemplateOverride;
use App\Services\Form\Components\RuleConditionGroups\AllRuleConditionGroup;
use App\Services\Form\Components\RuleConditionSideValues\FieldSideValue;
use App\Services\Form\Components\RuleEvents\LineItemAdd\ProductLineItemAddRuleEvent;
use App\Services\Form\Components\RuleEvents\LineItemRemoveRuleEvent;
use App\Services\Form\Components\Rules\PresentationRule;
use App\Services\Form\Components\Structures\BidStructure;
use App\Services\Form\Components\Templates\ServerTemplate;

return BidStructure::make()->withGroups(function (BidStructure $structure) {
    DefaultGroup::make($structure, 'main')
        ->withFields(function (DefaultGroup $group) {
            ProductListField::make($group, 'pump')->setLabel('Sump Pump')->setIsRequired()
                ->setDefaultProductCategoryName('Sump Pump')
                ->setProductCategoryCreateInstruction('Create the Sump Pump product category needed for the form.')
                ->requireOverride(function (FieldOverride $override) {
                $override->setLabel('Pump Product Category')
                    ->setDescription('Choose or create the pump product category. The category will be used to attach and organize your products within the form dropdown.');
            });
            ProductListField::make($group, 'basin')->setLabel('Basin')->setIsRequired()
                ->setDefaultProductCategoryName('Basin')
                ->setProductCategoryCreateInstruction('Create the Basin product category needed for the form.')
                ->requireOverride(function (FieldOverride $override) {
                $override->setLabel('Basin Product Category')
                    ->setDescription('Choose or create the basin product category. The category will be used to attach and organize your products within the form dropdown.');
            });
            TextField::make($group, 'total-interior-plumbing-length')->setLabel('Total Interior Plumbing Length')
                ->setDisplayMaskDecimal(2)->setDisplayIsInternal();
            TextField::make($group, 'number-of-elbows')->setLabel('Number of Elbows')->setDisplayMaskDecimal(2)->setDisplayIsInternal();
            TextField::make($group, 'linear-feet')->setLabel('Linear Feet (Discharge - Bury)')->setDisplayMaskDecimal(2)->setDisplayIsInternal();
            TextareaField::make($group, 'notes')->setLabel('Notes');
            FileField::make($group, 'photos')->setLabel('Photos')->setAllowedTypesImages()->setMaxSize(10485760);
        })
        ->withTemplates(function (DefaultGroup $group) {
            ServerTemplate::make($group, 'description')->requireOverride(function (TemplateOverride $override) {
                $override->setLabel('Sump Pump Service Description')
                    ->setDescription('Add an explanation of your sump pump installation service to be automatically pulled into the customer-facing Bid Document anytime this form is used.');
            });
        })
        ->withLayouts(function (DefaultGroup $group) {
            InputScreenLargeLayout::make($group)->withItems(function (InputScreenLargeLayout $layout) {
                FieldLayoutItem::make($layout, 'pump')->setSize(6);
                FieldLayoutItem::make($layout, 'basin')->setSize(6);
                FieldLayoutItem::make($layout, 'total-interior-plumbing-length')->setSize(4);
                FieldLayoutItem::make($layout, 'number-of-elbows')->setSize(4);
                FieldLayoutItem::make($layout, 'linear-feet')->setSize(4);
                FieldLayoutItem::make($layout, 'notes')->setSize(8);
                FieldLayoutItem::make($layout, 'photos')->setSize(4);
            });
            OutputBidDocumentLayout::make($group)->withItems(function (OutputBidDocumentLayout $layout) {
                TemplateLayoutItem::make($layout, 'description')->setSize(12);
                FieldLayoutItem::make($layout, 'pump')->setSize(6);
                FieldLayoutItem::make($layout, 'basin')->setSize(6);
                FieldLayoutItem::make($layout, 'notes')->setSize(12);
            });
            OutputJobDocumentLayout::make($group)->withItems(function (OutputJobDocumentLayout $layout) {
                FieldLayoutItem::make($layout, 'pump')->setSize(6);
                FieldLayoutItem::make($layout, 'basin')->setSize(6);
                FieldLayoutItem::make($layout, 'total-interior-plumbing-length')->setSize(4);
                FieldLayoutItem::make($layout, 'number-of-elbows')->setSize(4);
                FieldLayoutItem::make($layout, 'linear-feet')->setSize(4);
                FieldLayoutItem::make($layout, 'notes')->setSize(12);
            });
        })
        ->withRules(function (DefaultGroup $group) {
            PresentationRule::make($group)
                ->withConditions(function (PresentationRule $rule) {
                    AllRuleConditionGroup::make($rule)->with(function (AllRuleConditionGroup $group) {
                        Condition::make($group)->setLeft(FieldSideValue::make('pump'))->notEmpty();
                    });
                })
                ->withPassEvents(function (PresentationRule $rule) {
                    ProductLineItemAddRuleEvent::make($rule, 'add-pump')->setProductField('pump')->setQuantity('1');
                })
                ->withFailEvents(function (PresentationRule $rule) {
                    LineItemRemoveRuleEvent::make($rule)->setEvent('add-pump');
                });
            PresentationRule::make($group)
                ->withConditions(function (PresentationRule $rule) {
                    AllRuleConditionGroup::make($rule)->with(function (AllRuleConditionGroup $group) {
                        Condition::make($group)->setLeft(FieldSideValue::make('basin'))->notEmpty();
                    });
                })
                ->withPassEvents(function (PresentationRule $rule) {
                    ProductLineItemAddRuleEvent::make($rule, 'add-basin')->setProductField('basin')->setQuantity('1');
                })
                ->withFailEvents(function (PresentationRule $rule) {
                    LineItemRemoveRuleEvent::make($rule)->setEvent('add-basin');
                });
        });
});