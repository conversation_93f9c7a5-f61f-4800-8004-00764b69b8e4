<?php

declare(strict_types=1);

use Common\Classes\FormHelpers;

return FormHelpers::repeatableTable([
    'table-alias' => 'product-table',
    'template' => [
        'label' => 'Window Well Drains Service Description',
        'description' => 'Add an explanation of your window well drains installation service to be automatically pulled into the customer-facing Bid Document anytime this form is used.'
    ],
    'product_category' => [
        'label' => 'Window Well Drains Product Category',
        'description' => 'Choose or create the window well drains product category. The category will be used to attach and organize your products within the form dropdown.',
        'default_name' => 'Window Well Drains',
        'create_instruction' => 'Create the Window Well Drains product category needed for the form.'
    ]
]);
