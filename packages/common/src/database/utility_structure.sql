
/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `apiRequestBatchRequests` (
  `apiRequestBatchRequestID` binary(16) NOT NULL,
  `apiRequestBatchID` binary(16) NOT NULL,
  `type` varchar(100) NOT NULL,
  `action` enum('create','poly-create','update','partial-update','poly-update','update-or-create','poly-update-or-create','delete','save','sync') NOT NULL,
  `payload` longtext DEFAULT NULL,
  `scope` longtext DEFAULT NULL,
  `response` longtext DEFAULT NULL,
  `statusCode` smallint(5) unsigned NOT NULL,
  `time` bigint(20) unsigned NOT NULL,
  `createdAt` datetime(6) NOT NULL,
  PRIMARY KEY (`apiRequestBatchRequestID`),
  KEY `api_request_batch_id_index` (`apiRequestBatchID`),
  KEY `status_code_index` (`statusCode`),
  KEY `created_at_index` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `apiRequestBatches` (
  `apiRequestBatchID` binary(16) NOT NULL,
  `apiRequestID` binary(16) NOT NULL,
  `parentApiRequestBatchID` binary(16) DEFAULT NULL,
  `isAtomic` tinyint(3) unsigned NOT NULL,
  `isSequential` tinyint(3) unsigned NOT NULL,
  `requests` longtext DEFAULT NULL,
  `response` longtext DEFAULT NULL,
  `statusCode` smallint(5) unsigned NOT NULL,
  `time` bigint(20) unsigned NOT NULL,
  `createdAt` datetime(6) NOT NULL,
  PRIMARY KEY (`apiRequestBatchID`),
  KEY `api_request_id_index` (`apiRequestID`),
  KEY `parent_id_index` (`parentApiRequestBatchID`),
  KEY `status_code_index` (`statusCode`),
  KEY `created_at_index` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `apiRequests` (
  `apiRequestID` binary(16) NOT NULL,
  `userApiTokenID` binary(16) DEFAULT NULL,
  `ipAddressType` enum('IPv4','IPv6') NOT NULL,
  `ipAddress` binary(16) NOT NULL,
  `method` enum('GET','POST','PUT','PATCH','DELETE','HEAD','OPTIONS') NOT NULL,
  `path` varchar(500) NOT NULL,
  `requestHeaders` text DEFAULT NULL,
  `requestData` longtext DEFAULT NULL,
  `responseHeaders` text DEFAULT NULL,
  `responseData` longtext DEFAULT NULL,
  `statusCode` smallint(5) unsigned NOT NULL,
  `time` bigint(20) unsigned NOT NULL,
  `createdAt` datetime(6) NOT NULL,
  PRIMARY KEY (`apiRequestID`),
  KEY `user_api_token_id_index` (`userApiTokenID`),
  KEY `status_code_index` (`statusCode`),
  KEY `created_at_index` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `googleCalendarPushNotificationLog` (
  `googleCalendarPushNotificationLogID` binary(16) NOT NULL,
  `channelID` varchar(64) DEFAULT NULL,
  `headers` text NOT NULL,
  `message` varchar(500) DEFAULT NULL,
  `googleCalendarNotificationChannelHitID` binary(16) DEFAULT NULL,
  `createdAt` datetime(6) NOT NULL,
  PRIMARY KEY (`googleCalendarPushNotificationLogID`),
  KEY `created_at_index` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `mailgunWebhookLog` (
  `mailgunWebhookLogID` binary(16) NOT NULL,
  `body` longtext NOT NULL,
  `createdAt` datetime(6) NOT NULL,
  PRIMARY KEY (`mailgunWebhookLogID`),
  KEY `created_at_index` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `migrations` (
  `version` bigint(20) NOT NULL,
  `migration_name` varchar(100) DEFAULT NULL,
  `start_time` timestamp NULL DEFAULT NULL,
  `end_time` timestamp NULL DEFAULT NULL,
  `breakpoint` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `queueJobLog` (
  `queueJobLogID` binary(16) NOT NULL,
  `queueJobID` binary(16) NOT NULL,
  `workerID` binary(16) DEFAULT NULL,
  `handleAt` datetime(6) NOT NULL,
  `reservedAt` datetime(6) DEFAULT NULL,
  `try` tinyint(3) unsigned NOT NULL,
  `status` tinyint(3) unsigned NOT NULL,
  `time` bigint(20) unsigned NOT NULL,
  `createdAt` datetime(6) NOT NULL,
  `updatedAt` datetime(6) NOT NULL,
  `deletedAt` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`queueJobLogID`),
  KEY `queuejoblog_queuejobid_index` (`queueJobID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `queueJobs` (
  `queueJobID` binary(16) NOT NULL,
  `channel` tinyint(3) unsigned NOT NULL,
  `type` tinyint(3) unsigned NOT NULL,
  `job` longblob NOT NULL,
  `handleAt` datetime(6) NOT NULL,
  `maxTries` tinyint(3) unsigned DEFAULT NULL,
  `status` tinyint(3) unsigned NOT NULL,
  `tries` smallint(5) unsigned NOT NULL,
  `reservedAt` datetime(6) DEFAULT NULL,
  `handledAt` datetime(6) DEFAULT NULL,
  `failedAt` datetime(6) DEFAULT NULL,
  `createdAt` datetime(6) NOT NULL,
  `updatedAt` datetime(6) NOT NULL,
  `deletedAt` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`queueJobID`),
  KEY `queuejobs_type_index` (`type`),
  KEY `channel_index` (`channel`),
  KEY `channel_status` (`channel`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sessions` (
  `sessionID` varchar(40) COLLATE utf8_unicode_ci NOT NULL,
  `applicationID` tinyint(3) unsigned NOT NULL,
  `data` blob DEFAULT NULL,
  `createdAt` int(10) unsigned NOT NULL,
  `updatedAt` int(10) unsigned NOT NULL,
  PRIMARY KEY (`sessionID`),
  KEY `sessions_applicationid_index` (`applicationID`),
  KEY `sessions_updatedat_index` (`updatedAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `twilioWebhookLog` (
  `twilioWebhookLogID` binary(16) NOT NULL,
  `type` tinyint(3) unsigned NOT NULL,
  `body` longtext NOT NULL,
  `createdAt` datetime(6) NOT NULL,
  PRIMARY KEY (`twilioWebhookLogID`),
  KEY `created_at_index` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

/*!40000 ALTER TABLE `migrations` DISABLE KEYS */;
INSERT INTO `migrations` VALUES (20200723010618,'LogTablesMigration','2020-07-29 23:01:20','2020-07-29 23:01:20',0),(20200830000829,'UpdateGoogleLoggingMigration','2020-09-21 23:01:24','2020-09-21 23:01:25',0),(20210509003225,'UpdateQueueJobsTableMigration','2021-05-26 05:15:46','2021-05-26 05:15:46',0);
/*!40000 ALTER TABLE `migrations` ENABLE KEYS */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

