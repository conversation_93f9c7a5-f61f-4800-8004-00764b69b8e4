<?php

declare(strict_types=1);

namespace Core\Classes;

/**
 * Class Security
 *
 * @package Core\Classes
 */
abstract class Security
{
    /**
     * Hash password
     *
     * @param string $data
     * @return string|bool
     */
    public static function hashPassword(string $data): string|bool
    {
        return \password_hash($data, \PASSWORD_DEFAULT);
    }

    /**
     * Verify password
     *
     * @param string $data
     * @param string $hash
     * @return bool
     */
    public static function verifyPassword(string $data, string $hash): bool
    {
        return \password_verify($data, $hash);
    }

    /**
     * Generate hash using HMAC
     *
     * @param string $data
     * @param string $key
     * @param bool $raw_output
     * @return string
     */
    public static function generateHMAC(string $data, string $key, bool $raw_output = false): string
    {
        return \hash_hmac('sha256', $data, $key, $raw_output);
    }

    /**
     * Compare HMAC hashes
     *
     * @param string $hash
     * @param string $data
     * @param string $key
     * @return bool
     */
    public static function compareHMAC(string $hash, string $data, string $key): bool
    {
        return \hash_equals(self::generateHMAC($data, $key), $hash);
    }
}
