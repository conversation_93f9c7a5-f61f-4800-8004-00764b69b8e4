<?php

namespace Core\Components\Auth\StaticAccessors;

use Core\Components\Auth\Classes\Auth as AuthOriginal;
use Core\Classes\StaticAccessor;

/**
 * Class Auth
 *
 * @package Core\Components\Auth\StaticAccessors
 */
class Auth extends StaticAccessor
{
    /**
     * Get instance of Auth
     *
     * @return \Core\Components\Auth\Classes\Auth
     */
    protected static function getInstance()
    {
        return static::$kernel->get(AuthOriginal::class);
    }
}
