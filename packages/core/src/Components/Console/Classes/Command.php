<?php

declare(strict_types=1);

namespace Core\Components\Console\Classes;

use Core\Components\Console\Exceptions\{CommandException, Console\CommandNotFoundException};
use Core\Components\Console\Interfaces\{CommandInterface, ConsoleInterface, UserCommandInterface};
use Core\Exceptions\AppException;
use Core\Interfaces\KernelInterface;

/**
 * Class Command
 *
 * @package Core\Components\Console\Classes
 */
class Command implements CommandInterface
{
    /**
     * @var array List of available commands
     */
    protected array $commands = [];

    /**
     * Command constructor
     *
     * @param KernelInterface $kernel
     */
    public function __construct(protected KernelInterface $kernel)
    {}

    /**
     * Add command
     *
     * @param string $name
     * @param string $class
     * @param string|null $method
     * @return $this
     */
    public function add(string $name, string $class, ?string $method = null): static
    {
        if ($method === null) {
            $method = '__invoke';
        }
        $this->commands[$name] = [$class, $method];
        return $this;
    }

    /**
     * Checks if command exists
     *
     * @param string $command
     * @return bool
     */
    public function exists(string $command): bool
    {
        return isset($this->commands[$command]);
    }

    /**
     * Run command
     *
     * @param string $command
     * @param ConsoleInterface $console
     * @return UserCommandInterface
     * @throws CommandException
     * @throws AppException
     */
    public function run(string $command, ConsoleInterface $console): UserCommandInterface
    {
        if (!$this->exists($command)) {
            throw new CommandNotFoundException('Command not found: %s', $command);
        }
        [$class, $method] = $this->commands[$command];

        /** @var UserCommandInterface $instance */
        $instance = $this->kernel->create($class);
        if ((!$instance instanceof UserCommandInterface)) {
            throw new CommandException('Command class must implement %s', UserCommandInterface::class);
        }
        $instance->setConsole($console);
        $this->kernel->call([$instance, $method]);

        return $instance;
    }
}
