<?php

namespace Core\Components\Crypt\Classes;

use Core\Components\Crypt\Exceptions\CryptException;
use Core\Classes\Config;
use Defuse\Crypto\Crypto;
use Defuse\Crypto\Exception\WrongKeyOrModifiedCiphertextException;
use Defuse\Crypto\Key;

/**
 * Class Crypt
 *
 * Basic encryption wrapper for php-encryption package, will need to be expanded on in the future
 *
 * @package Core\Components\Crypt\Classes
 */
class Crypt
{
    /**
     * Config instance
     *
     * @var Config
     */
    protected $config;

    /**
     * Key instance
     *
     * @var Key
     */
    protected $key;

    /**
     * Crypt constructor
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        $this->config = $config;
        $this->key = Key::loadFromAsciiSafeString($config->get('app.key'));
    }

    /**
     * Encrypt string using application key
     *
     * @param string $string
     * @param bool $raw_binary
     * @return string
     */
    public function encrypt($string, $raw_binary = false)
    {
        return Crypto::encrypt($string, $this->key, $raw_binary);
    }

    /**
     * Decrypt string using application key
     *
     * @param string $cipher_text
     * @param bool $raw_binary
     * @return string
     *
     * @throws CryptException
     */
    public function decrypt($cipher_text, $raw_binary = false)
    {
        try {
            return Crypto::decrypt($cipher_text, $this->key, $raw_binary);
        } catch (WrongKeyOrModifiedCiphertextException $e) {
            throw new CryptException('Unable to decrypt data - Reason: %s', $e->getMessage());
        }
    }
}
