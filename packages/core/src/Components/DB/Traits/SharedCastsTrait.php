<?php

namespace Core\Components\DB\Traits;

/**
 * Trait SharedCastsTrait
 *
 * @package Core\Components\DB\Traits
 */
trait SharedCastsTrait
{
    /**
     * Pull casts from castMap property and merge with casts of parent class
     *
     * @return array
     */
    public function getCasts()
    {
        $casts = parent::getCasts();
        if (isset($this->castMap)) {
            $casts = array_merge($casts, $this->castMap);
        }
        return $casts;
    }
}
