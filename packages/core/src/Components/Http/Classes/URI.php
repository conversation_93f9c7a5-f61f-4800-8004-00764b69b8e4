<?php

namespace Core\Components\Http\Classes;

use Closure;
use Core\Classes\Config;
use Core\Classes\Str;
use Core\Components\Http\Exceptions\URIException;
use Core\Components\Http\Interfaces\RouterInterface;

/**
 * Class URI
 *
 * @package Core\Components\Http\Classes
 */
class URI
{
    /**
     * Query string key of the application URI
     */
    const QUERY_STRING_KEY = '_uri_';

    /**
     * URI configuration
     *
     * @var array
     */
    protected $config = [
        'ssl' => true
    ];

    /**
     * Input instance
     *
     * @var Input
     */
    protected $input;

    /**
     * URLBuilder instance
     *
     * @var URLBuilder
     */
    protected $url;

    /**
     * Router instance
     *
     * @var null|RouterInterface
     */
    protected $router = null;

    /**
     * URI constructor
     *
     * @param Config $config
     * @param Input $input
     * @param bool $console_mode
     * @throws URIException
     */
    public function __construct(Config $config, Input $input, $console_mode)
    {
        $this->config['ssl'] = $config->get('app.secure', true);

        $this->input = $input;

        if ($console_mode) {
            $this->url = URLBuilder::fromString($config->get('app.base_url'))->secure($this->config['ssl']);
        } else {
            $this->url = (new URLBuilder)->secure($this->input->detectSSL());
        }

        // handle host
        if (($host = $this->input->header('Host')) !== null) {
            $this->url->host($host);
        }

        // handle path
        $this->url->path($this->getPathFromInput($console_mode));

        // handle query
        $query = $this->input->get();
        if (isset($query[self::QUERY_STRING_KEY])) {
            unset($query[self::QUERY_STRING_KEY]);
        }
        $this->url->query($query);
    }

    /**
     * Determine path using Input data
     *
     * @param bool $console_mode
     * @return string
     *
     * @throws URIException
     */
    protected function getPathFromInput($console_mode)
    {
        if ($this->input->has(self::QUERY_STRING_KEY)) {
            return $this->input->request(self::QUERY_STRING_KEY);
        }
        if (($path = $this->input->server('PATH_INFO')) !== null) {
            return $path;
        }
        if (($path = $this->input->server('REQUEST_URI')) !== null) {
            return ltrim($path, '/');
        }
        if ($console_mode) {
            return '';
        }
        throw new URIException('Unable to determine uri path');
    }

    /**
     * Get request URLBuilder instance
     *
     * @return URLBuilder
     */
    public function getBuilder()
    {
        return $this->url;
    }

    /**
     * Get clone of request URLBuilder instance
     *
     * @return URLBuilder
     */
    public function newBuilder()
    {
        $builder = clone $this->url;
        return $builder;
    }

    /**
     * Gets count of segments
     *
     * @return int
     */
    public function segmentCount()
    {
        return $this->url->getSegmentCount();
    }

    /**
     * Gets segments between a start and end position
     *
     * @param int $start
     * @param null|int $end
     *
     * @return array
     */
    public function segments($start = 0, $end = null)
    {
        return $this->url->getSegments($start, $end);
    }

    /**
     * Determines if segment exists
     *
     * @param int $index
     * @return bool
     */
    public function has($index)
    {
        return $this->url->getSegment($index) !== null;
    }

    /**
     * Get segment value
     *
     * @param int $index
     * @param bool $default
     * @return bool
     */
    public function get($index, $default = null)
    {
        return $this->url->getSegment($index, $default);
    }

    /**
     * Get segment value
     *
     * Alias of get() method
     *
     * @param int $index
     * @param null $default
     * @return bool
     */
    public function segment($index, $default = null)
    {
        return $this->get($index, $default);
    }

    /**
     * Get full base URL
     *
     * @param null|bool $secure
     * @return string
     */
    public function base($secure = null)
    {
        $secure = (!$this->config['ssl'] ? false : ($secure === null ? $this->input->detectSSL() : $secure));
        return $this->newBuilder()->secure($secure)->build(URLBuilder::PART_BASE);
    }

    /**
     * Get URI path
     *
     * @return null|string
     */
    public function path()
    {
        return $this->url->getPath();
    }

    /**
     * Determine if pattern(s) match current URI path
     *
     * @param string|array $pattern
     * @param bool $regex
     * @return bool
     */
    public function matches($pattern, $regex = false)
    {
        return Str::matches($pattern, ltrim($this->path(), '/'), $regex);
    }

    /**
     * Create URL
     *
     * @param Closure|null $closure
     * @return URLBuilder|string
     */
    public function create(Closure $closure = null)
    {
        $builder = $this->newBuilder()->path(null)->query([]);
        if ($closure !== null) {
            return $closure($builder);
        }
        return $builder;
    }

    /**
     * Get current URL
     *
     * @param Closure|null $closure
     * @return URLBuilder|string
     */
    public function current(Closure $closure = null)
    {
        $builder = $this->newBuilder();
        if ($closure !== null) {
            return $closure($builder);
        }
        return $builder;
    }

    /**
     * Set Router instance
     *
     * @param RouterInterface $router
     */
    public function setRouter(RouterInterface $router)
    {
        $this->router = $router;
    }

    /**
     * Get route URL
     *
     * @param string $name
     * @param array $data
     * @param Closure|null $closure
     * @return URLBuilder|string
     *
     * @throws URIException
     */
    public function route($name, array $data = [], Closure $closure = null)
    {
        if ($this->router === null) {
            throw new URIException('Router not available');
        }
        if (($route = $this->router->getNamedRoute($name)) === false) {
            throw new URIException('Unable to find named route: %s', $name);
        }
        $path = $route->fillPath($data);
        return $this->create(function ($uri) use ($path, $closure) {
            $uri->path($path);
            if ($closure !== null) {
                $closure($uri);
            }
            return $uri;
        });
    }
}
