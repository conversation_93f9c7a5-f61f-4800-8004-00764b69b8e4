<?php

namespace Core\Components\Http\Interfaces;

/**
 * Interface RouteInterface
 *
 * @package Core\Components\Http\Interfaces
 */
interface RouteInterface
{
    /**
     * @return string
     */
    public function getName();

    /**
     * @return string
     */
    public function getPath();

    /**
     * @return array
     */
    public function getBindings();

    /**
     * @return array
     */
    public function getParsedPath();

    /**
     * @return array
     */
    public function getMethods();

    /**
     * @return \Core\Components\Http\Classes\MiddlewareStack
     */
    public function getMiddleware();

    /**
     * @return array
     */
    public function getController();

    /**
     * @param null|string $key
     * @param null|mixed $default
     * @return mixed
     */
    public function input($key = null, $default = null);
}
