<?php

declare(strict_types=1);

namespace Core\Components\Queue\Classes;

use Core\Components\Queue\Interfaces\WorkerOptionsInterface;

/**
 * Class WorkerOptions
 *
 * @package Core\Components\Queue\Classes
 */
class WorkerOptions extends DaemonOptions implements WorkerOptionsInterface
{
    /**
     * WorkerOptions constructor
     *
     * @param float $sleep - Number of seconds to wait between cycles
     * @param int $max_memory - Maximum amount of memory (in MB) the worker can use before being killed
     * @param int $max_time - Maximum amount of time (in seconds) the worker can run before being killed
     * @param int $max_tries - Maximum number of tries before failing a job, individual jobs can override this
     * @param int $max_jobs - Maximum amount of jobs to process before the worker is killed
     * @param float $rest - Number of seconds to rest between jobs
     * @param int $timeout - Number of seconds a job is allowed to process before being killed
     * @param bool $sequential - Determines if jobs must run in the order they were defined
     * @param array|null $backoff - List of backoff times (in seconds) for each attempt in sequence
     * @param bool $stop_when_empty - Stop worker once all jobs are finished
     */
    public function __construct(
        protected float $sleep = 1.0,
        protected int $max_memory = 128,
        protected int $max_time = 14400,
        protected int $max_tries = 5,
        protected int $max_jobs = 1000,
        protected float $rest = 0.0,
        protected int $timeout = 60,
        protected bool $sequential = false,
        protected ?array $backoff = null,
        protected bool $stop_when_empty = false
    ) {}

    /**
     * Set max tries
     *
     * @param int $tries
     */
    public function setMaxTries(int $tries): void
    {
        $this->max_tries = $tries;
    }

    /**
     * Get max tries
     *
     * @return int
     */
    public function getMaxTries(): int
    {
        return $this->max_tries;
    }

    /**
     * Set max jobs
     *
     * @param int $jobs
     */
    public function setMaxJobs(int $jobs): void
    {
        $this->max_jobs = $jobs;
    }

    /**
     * Get max jobs
     *
     * @return int
     */
    public function getMaxJobs(): int
    {
        return $this->max_jobs;
    }

    /**
     * Set rest time between jobs (in seconds)
     *
     * @param float $rest
     */
    public function setRest(float $rest): void
    {
        $this->rest = $rest;
    }

    /**
     * Get rest time
     *
     * @return float
     */
    public function getRest(): float
    {
        return $this->rest;
    }

    /**
     * Set timeout for job execution (in seconds)
     *
     * @param int $timeout
     */
    public function setTimeout(int $timeout): void
    {
        $this->timeout = $timeout;
    }

    /**
     * Get timeout
     *
     * @return int
     */
    public function getTimeout(): int
    {
        return $this->timeout;
    }

    /**
     * Set if jobs should be handled sequentially
     *
     * @param bool $sequential
     */
    public function setSequential(bool $sequential): void
    {
        $this->sequential = $sequential;
    }

    /**
     * Get sequential setting
     *
     * @return bool
     */
    public function getSequential(): bool
    {
        return $this->sequential;
    }

    /**
     * Get backoff times (array of seconds)
     *
     * Each item in the array corresponds to a try attempt starting from 0 index.
     *
     * @param array|null $backoff
     */
    public function setBackoff(?array $backoff): void
    {
        $this->backoff = $backoff;
    }

    /**
     * Get backoff config
     *
     * @return array|null
     */
    public function getBackoff(): ?array
    {
        return $this->backoff;
    }

    /**
     * Set if worker stops when all jobs are done
     *
     * @param bool $stop
     */
    public function setStopWhenEmpty(bool $stop): void
    {
        $this->stop_when_empty = $stop;
    }

    /**
     * Get stop when empty setting
     *
     * @return bool
     */
    public function getStopWhenEmpty(): bool
    {
        return $this->stop_when_empty;
    }

    /**
     * Convert options into array format
     *
     * @return array
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'rest' => $this->getRest(),
            'timeout' => $this->getTimeout(),
            'max_tries' => $this->getMaxTries(),
            'max_jobs' => $this->getMaxJobs(),
            'sequential' => $this->getSequential(),
            'backoff' => $this->getBackoff(),
            'stop_when_empty' => $this->getStopWhenEmpty()
        ]);
    }

    /**
     * Output array format to JSON
     *
     * @return array
     */
    public function jsonSerialize(): array
    {
        return $this->toArray();
    }
}
