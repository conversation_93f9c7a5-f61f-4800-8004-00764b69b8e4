<?php

declare(strict_types=1);

namespace Core\Components\Resource\Classes;

use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Resource\Exceptions\BatchHandleException;
use Core\Components\Resource\Exceptions\BatchPrepareException;
use Core\Components\Resource\Exceptions\ResourceException;
use Core\Components\Resource\Interfaces\BatchableRequestInterface;
use Core\Components\Resource\Traits\CallbacksTrait;
use Core\Components\Resource\Traits\Request\BatchableTrait;
use Core\Components\Resource\Traits\Request\StorageTrait;
use Core\Components\Resource\Traits\Request\TransactionTrait;
use Core\Exceptions\AppException;
use Throwable;

/**
 * Class BatchRequest
 *
 * @package Core\Components\Resource\Classes
 */
class BatchRequest implements BatchableRequestInterface
{
    use BatchableTrait;
    use CallbacksTrait;
    use StorageTrait;
    use TransactionTrait;

    protected bool $atomic = false;

    protected bool $sequential = false;

    /**
     * @var BatchableRequestInterface[]
     */
    protected array $requests = [];

    protected ?bool $nested_transaction = null;

    protected bool $prepared = false;

    /**
     * @var BatchableRequestInterface[]
     */
    protected array $completed_requests = [];

    protected bool $success = true;

    protected ?Throwable $exception = null;

    protected ?array $response = null;

    /**
     * Create new instance
     *
     * @return static
     */
    public static function make()
    {
        return new static();
    }

    /**
     * Add request
     *
     * @param BatchableRequestInterface $request
     * @param string|int|null $name
     * @return $this
     */
    public function add(BatchableRequestInterface $request, $name = null): self
    {
        $request->inBatch();
        $idx = $name === null ? count($this->requests) : $name;
        $this->requests[$idx] = $request;

        $request->setParentBatchRequest($this);

        return $this;
    }

    public function atomic(bool $bool = true): self
    {
        $this->atomic = $bool;
        return $this;
    }

    public function isAtomic(): bool
    {
        return $this->atomic;
    }

    public function sequential(bool $bool = true): self
    {
        $this->sequential = $bool;
        $this->atomic($bool);
        return $this;
    }

    public function isSequential(): bool
    {
        return $this->sequential;
    }

    public function getRequests(): array
    {
        return $this->requests;
    }

    /**
     * Get request by name
     *
     * Works in conjunction with the name param of the add() method.
     *
     * @param string $name
     * @return BatchableRequestInterface
     * @throws AppException
     */
    public function getRequest($name): BatchableRequestInterface
    {
        if (!isset($this->requests[$name])) {
            throw new AppException('Unable to find request: %s', $name);
        }
        return $this->requests[$name];
    }

    /**
     * Prepare batch request
     *
     * Run prepare on all requests if not in sequential mode.
     *
     * @throws AppException
     * @throws BatchPrepareException
     */
    public function prepare(): void
    {
        if ($this->prepared) {
            throw new AppException('Request should not be prepared more than once');
        }

        // if use transaction is defined, we use it. otherwise, if batch is atomic we disable transactions for nested
        // class to reduce nested transactions. if not atomic, we leave it for the request to determine if it uses
        // a transaction or not.
        $this->nested_transaction = $this->useTransaction() ?? ($this->isAtomic() ? false : null);

        // if we are not in sequential mode meaning the requests aren't dependent on each other, then we run
        // prepare() on each one to handle any validation in bulk.
        if (!$this->isSequential()) {
            $has_error = false;
            foreach ($this->requests as &$request) {
                $start_time = microtime(true);
                try {
                    $request = $this->applyCallback('request', $request);
                    $request->setUseTransaction($this->nested_transaction);
                    $request->prepare();
                } catch (Throwable $e) {
                    $request->setException($e);
                    $has_error = true;
                } finally {
                    $request->storage('_time', (int) round(((microtime(true) - $start_time) * 1000000) / 1000));
                }
                unset($request);
            }
            // if an error occurred and this is atomic (meaning they all have to pass before moving on) then we throw an
            // exception to stop processing
            if ($has_error && $this->isAtomic()) {
                $errors = [];
                foreach ($this->requests as $i => $request) {
                    if ($request->success()) {
                        continue;
                    }
                    $errors[$i] = ResourceException::toMessage($request->exception());
                }
                throw (new BatchPrepareException('Unable to prepare requests'))->setErrors($errors);
            }
        }

        $this->runCallback('prepare.after', $this);

        $this->prepared = true;
    }

    /**
     * Run through all requests and handle them
     *
     * Requests will be prepared in order if sequential mode is enabled. If atomic mode is enabled, the first request to
     * fail will stop processing for all requests after it.
     *
     * @throws Throwable
     */
    protected function processRequests(): void
    {
        $sequential = $this->isSequential();
        $last_request = null;
        foreach ($this->requests as $request) {
            // if request failed in the prepare stage, then skip. only used when sequential and atomic are disabled.
            if (!$sequential && !$request->success()) {
                continue;
            }
            $start_time = microtime(true);
            try {
                if ($sequential) {
                    if ($last_request !== null) {
                        $request->batchLastRequest($last_request);
                    }
                    $request->setUseTransaction($this->nested_transaction);
                    $request->prepare();
                }
                $request->handle();
                $last_request = $request;
                $this->completed_requests[] = $request;
            } catch (Throwable $e) {
                $request->setException($e);
                if ($this->isAtomic()) {
                    throw $e;
                }
            } finally {
                $request->store('_time', $request->storage('_time', 0) + (int) round(((microtime(true) - $start_time) * 1000000) / 1000));
            }
        }
    }

    /**
     * Handle batch request
     *
     * If atomic and a transaction has been requested, then we process all requests within a transaction. If exception is
     * thrown we run rollbacks for each completed request.
     *
     * @throws AppException
     */
    public function handle(): void
    {
        if (!$this->prepared) {
            throw new AppException('Cannot handle request which has not been prepared');
        }

        $start_time = microtime(true);
        try {
            if ($this->isAtomic() && ($this->useTransaction() ?? true)) {
                DB::transaction(function () {
                    $this->processRequests();
                });
            } else {
                $this->processRequests();
            }
        } catch (Throwable $e) {
            $this->rollback();
            $exception = (new BatchHandleException('Unable to handle batch'))->setLastException($e);
            $this->setException($exception);
            if ($this->isAtomic()) {
                throw $exception;
            }
        } finally {
            $this->store('_time', (int) round(((microtime(true) - $start_time) * 1000000) / 1000));
        }

        $this->setResponse($this->getRequests());
    }

    /**
     * Call rollback handler for every completed request
     */
    public function rollback(): void
    {
        $this->runCallback('rollback', $this);
        if (count($this->completed_requests) === 0) {
            return;
        }
        foreach ($this->completed_requests as $request) {
            try {
                $request->rollback();
            } catch (Throwable $e) {
                Resource::getLogger()->error('Unable to rollback batch request', [
                    'exception' => $e
                ]);
            }
        }
    }

    public function success(): bool
    {
        return $this->success;
    }

    public function setException(Throwable $e): self
    {
        $this->success = false;
        $this->exception = $e;
        return $this;
    }

    public function exception(): ?Throwable
    {
        return $this->exception;
    }

    protected function setResponse(array $data): void
    {
        $this->success = true;
        $this->response = $data;
    }

    public function response(): ?array
    {
        return $this->response;
    }

    /**
     * Run close on all completed requests
     */
    public function close(): void
    {
        $this->runCallback('close', $this);
        if (count($this->completed_requests) === 0) {
            return;
        }
        foreach ($this->completed_requests as $request) {
            try {
                $request->close();
            } catch (Throwable $e) {
                Resource::getLogger()->error('Unable to close batch request', [
                    'exception' => $e
                ]);
            }
        }
    }
}
