<?php

namespace Core\Components\Resource\Classes;

class Collection extends \Illuminate\Support\Collection
{
    protected $pagination = null;

    public static function fromArray(array $data)
    {
        $collection = new static;
        foreach ($data as $datum) {
            if (is_object($datum) && $datum instanceof Entity) {
                $entity = $datum;
            } else {
                if (!is_array($datum)) {
                    $datum = [];
                }
                $entity = Entity::make($datum);
            }
            $collection->push($entity);
        }
        return $collection;
    }

    public function setPagination($pagination)
    {
        $this->pagination = $pagination;
    }

    public function pagination()
    {
        return $this->pagination;
    }
}
