<?php

namespace Core\Components\Resource\Classes;

use Closure;
use Core\Classes\Str;

class Hooks
{
    protected $delegates = [];
    protected $delegate_instances = [];
    protected $attachments;

    public function delegate($parent, $class)
    {
        if (isset($this->delegates[$parent])) {
            $this->delegates[$parent] = [];
        }
        $this->delegates[$parent][] = $class;
        return $this;
    }

    public function getDelegates($parent = null)
    {
        if ($parent === null) {
            return $this->delegates;
        }
        return isset($this->delegates[$parent]) ? $this->delegates[$parent] : [];
    }

    public function getDelegateInstance($parent, $class)
    {
        if (!isset($this->delegate_instances[$parent][$class])) {
            if (!isset($this->delegate_instances[$parent])) {
                $this->delegate_instances[$parent] = [];
            }
            $this->delegate_instances[$parent][$class] = new $class;
        }
        return $this->delegate_instances[$parent][$class];
    }

    public function attach($parent, $name, Closure $closure)
    {
        if (!is_array($name)) {
            $name = [$name];
        }
        foreach ($name as $_name) {
            if (!isset($this->attachments[$parent])) {
                $this->attachments[$parent] = [];
            }
            if (!isset($this->attachments[$parent][$_name])) {
                $this->attachments[$parent][$_name] = [];
            }
            $this->attachments[$parent][$_name][] = $closure;
        }
        return $this;
    }

    public function run($parent, $name, ...$args)
    {
        if (!is_array($name)) {
            $name = [$name];
        }
        foreach ($name as $_name) {
            $method = Str::toCamelCase($_name);
            foreach ($this->getDelegates($parent) as $delegate) {
                $instance = $this->getDelegateInstance($parent, $delegate);
                if (!method_exists($instance, $method)) {
                    continue;
                }
                $instance->{$method}(...$args);
            }
            if (!isset($this->attachments[$parent][$_name])) {
                continue;
            }
            foreach ($this->attachments[$parent][$_name] as $attachment) {
                $attachment(...$args);
            }
        }
    }

    public function runFilter($parent, $name, $value, ...$args)
    {
        if (!is_array($name)) {
            $name = [$name];
        }
        foreach ($name as $_name) {
            $method = Str::toCamelCase($_name);
            foreach ($this->getDelegates($parent) as $delegate) {
                $instance = $this->getDelegateInstance($parent, $delegate);
                if (!method_exists($instance, $method)) {
                    continue;
                }
                $value = $instance->{$method}($value, ...$args);
            }
            if (!isset($this->attachments[$parent][$_name])) {
                continue;
            }
            foreach ($this->attachments[$parent][$_name] as $attachment) {
                $value = $attachment($value, ...$args);
            }
        }
        return $value;
    }

    public function runBool($parent, $name, $default, $stop_value, ...$args)
    {
        if (!is_array($name)) {
            $name = [$name];
        }
        $value = null;
        foreach ($name as $_name) {
            $method = Str::toCamelCase($_name);
            foreach ($this->getDelegates($parent) as $delegate) {
                $instance = $this->getDelegateInstance($parent, $delegate);
                if (!method_exists($instance, $method)) {
                    continue;
                }
                $value = (bool) $instance->{$method}(...$args);
                if ($value === $stop_value) {
                    return $stop_value;
                }
            }
            if (!isset($this->attachments[$parent][$_name])) {
                continue;
            }
            foreach ($this->attachments[$parent][$_name] as $attachment) {
                $value = (bool) $attachment(...$args);
                if ($value === $stop_value) {
                    return $stop_value;
                }
            }
        }
        if ($value === null) {
            return $default;
        }
        return $value;
    }
}
