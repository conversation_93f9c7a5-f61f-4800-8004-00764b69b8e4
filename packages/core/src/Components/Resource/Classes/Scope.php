<?php

namespace Core\Components\Resource\Classes;

use Closure;
use Core\Classes\Arr;
use Core\Components\Resource\Traits\CallbacksTrait;
use Core\Exceptions\AppException;
use InvalidArgumentException;

/**
 * Class Scope
 *
 * @package Core\Components\Resource\Classes
 */
class Scope
{
    use CallbacksTrait;

    const FORMAT_JSON = 'json';

    const SORT_ASC = 'asc';
    const SORT_DESC = 'desc';

    /**
     * @var null|self
     */
    protected $parent = null;

    /**
     * @var null|mixed
     */
    protected $format = null;

    /**
     * @var array List of requested fields
     */
    protected $fields = [];

    /**
     * @var bool Determines if no fields should be returned, overriding fields list above
     */
    protected $no_fields = false;

    /**
     * @var bool Determines if filters will be added to query
     */
    protected $filters_enabled = true;

    /**
     * @var array List of filters
     */
    protected $filters = [];

    /**
     * @var null|string Term to search
     */
    protected $search = null;

    /**
     * @var bool Determines if sorts are added to query
     */
    protected $sorts_enabled = true;

    /**
     * @var array List of sorts
     */
    protected $sorts = [];

    /**
     * @var bool Determines if pagination is allowed
     */
    protected $pagination_enabled = true;

    /**
     * @var int Current page for pagination
     */
    protected $page = 1;

    /**
     * @var null|int Per page limit for pagination
     */
    protected $per_page = null;

    /**
     * @var bool Determines if query closure is allowed
     */
    protected $query_enabled = true;

    /**
     * @var null|Closure
     */
    protected $query = null;

    /**
     * @var bool Determines if relationships are allowed
     */
    protected $relations_enabled = true;

    /**
     * @var bool Determines if eager loading is allowed
     */
    protected $eager_load = true;

    /**
     * @var Scope[]
     */
    protected $relations = [];

    /**
     * @var array Holding array for relationships
     */
    protected $with = [];

    /**
     * @var array List of poly scopes keyed by type
     */
    protected $poly_scopes = [];

    /**
     * Get sort directions
     *
     * @return array
     */
    public static function getSortDirections()
    {
        return [self::SORT_ASC, self::SORT_DESC];
    }

    /**
     * Create new instance
     *
     * @return Scope
     */
    public static function make()
    {
        return new static;
    }

    /**
     * Set scope parent
     *
     * @param Scope $parent
     * @return $this
     */
    protected function setParent(Scope $parent)
    {
        $this->parent = $parent;
        return $this;
    }

    /**
     * Get scope parent
     *
     * @return Scope|null
     */
    public function parent()
    {
        return $this->parent;
    }

    /**
     * Determine if scope has format defined
     *
     * @return bool
     */
    public function hasFormat()
    {
        return isset($this->format);
    }

    /**
     * Set format
     *
     * @param mixed|null $format
     * @return $this
     */
    public function format($format)
    {
        $this->format = $format;
        return $this;
    }

    /**
     * Get format, or return default json format
     *
     * @return mixed
     */
    public function getFormat()
    {
        return isset($this->format) ? $this->format : self::FORMAT_JSON;
    }

    /**
     * Determine if scope is a specific format
     *
     * @param mixed $format
     * @return bool
     */
    public function isFormat($format)
    {
        return $this->getFormat() === $format;
    }

    /**
     * Clear all defined fields
     *
     * @return $this
     */
    public function clearFields()
    {
        $this->fields = [];
        return $this;
    }

    /**
     * Add field
     *
     * @param string $field
     * @return $this
     */
    public function field($field)
    {
        $this->fields[] = $field;
        return $this;
    }

    /**
     * Add multiple fields
     *
     * @param array $fields
     * @param bool $overwrite
     * @return $this
     */
    public function fields(array $fields, $overwrite = false)
    {
        if ($overwrite) {
            $this->clearFields();
        }
        foreach ($fields as $field) {
            $this->field($field);
        }
        return $this;
    }

    /**
     * Get fields list
     *
     * If no fields is enabled, then empty array is returned
     *
     * @return array
     */
    public function getFields()
    {
        return $this->no_fields ? [] : $this->fields;
    }

    /**
     * Control if no fields will be returned from scope
     *
     * @param bool $bool
     * @return $this
     */
    public function noFields($bool = true)
    {
        $this->no_fields = $bool;
        return $this;
    }

    /**
     * Determine if no fields setting is enabled
     *
     * @return bool
     */
    public function isNoFields()
    {
        return $this->no_fields;
    }

    /**
     * Control status of allowing filters
     *
     * @param bool $bool
     * @return $this
     */
    public function enableFilters($bool = true)
    {
        $this->filters_enabled = $bool;
        return $this;
    }

    /**
     * Disable allowing filters
     *
     * @return Scope
     */
    public function disableFilters()
    {
        return $this->enableFilters(false);
    }

    /**
     * Determine if filters are enabled
     *
     * @return bool
     */
    public function isFiltersEnabled()
    {
        return $this->filters_enabled;
    }

    /**
     * Clear all filters
     *
     * @return $this
     */
    public function clearFilters()
    {
        $this->filters = [];
        return $this;
    }

    /**
     * Add filter
     *
     * @param string $field
     * @param string $operator
     * @param mixed $value
     * @param int $field_type
     * @return $this
     */
    public function filter($field, $operator, $value, $field_type = Field::TYPE_DEFAULT)
    {
        if (!in_array($operator, ['eq', 'not-eq', 'lt', 'lte', 'gt', 'gte', 'in', 'not-in', 'between', 'not-between', 'null', 'not-null'])) {
            throw new InvalidArgumentException('Invalid operator');
        }
        if (!in_array($field_type, Field::getTypes())) {
            throw new InvalidArgumentException('Invalid field type');
        }
        $this->filters[] = compact('field', 'operator', 'value', 'field_type');
        return $this;
    }

    /**
     * Add multiple filters
     *
     * @param array $filters
     * @param bool $overwrite
     * @return $this
     * @throws AppException
     */
    public function filters(array $filters, $overwrite = false)
    {
        if ($overwrite) {
            $this->clearFilters();
        }
        foreach ($filters as $filter) {
            $missing = [];
            if (!Arr::hasKey($filter, ['field', 'operator', 'value'], $missing)) {
                throw new AppException('Filter missing the following items: %s', implode(', ', $missing));
            }
            if (!isset($filter['field_type'])) {
                $filter['field_type'] = Field::TYPE_DEFAULT;
            }
            $this->filter($filter['field'], $filter['operator'], $filter['value'], $filter['field_type']);
        }
        return $this;
    }

    /**
     * Get all filters
     *
     * @return array
     */
    public function getFilters()
    {
        return $this->filters;
    }

    /**
     * Set search term
     *
     * @param string|null $term
     * @return $this
     */
    public function search($term)
    {
        if ($term !== null && ($term = trim($term)) === '') {
            $term = null;
        }
        $this->search = $term;
        return $this;
    }

    /**
     * Get search term
     *
     * @return string|null
     */
    public function getSearch()
    {
        return $this->search;
    }

    /**
     * Determines if search term has been defined
     *
     * @return bool
     */
    public function isSearching()
    {
        return $this->search !== null;
    }

    /**
     * Control status of allowing sorting
     *
     * @param bool $bool
     * @return $this
     */
    public function enableSorts($bool = true)
    {
        $this->sorts_enabled = $bool;
        return $this;
    }

    /**
     * Disable sorting
     *
     * @return Scope
     */
    public function disableSorts()
    {
        return $this->enableSorts(false);
    }

    /**
     * Determine if sorting is enabled
     *
     * @return bool
     */
    public function isSortsEnabled()
    {
        return $this->sorts_enabled;
    }

    /**
     * Clear all sorts
     *
     * @return $this
     */
    public function clearSorts()
    {
        $this->sorts = [];
        return $this;
    }

    /**
     * Add sort
     *
     * @param string $field
     * @param string $direction
     * @param array $config
     * @return $this
     */
    public function sort($field, $direction = 'asc', array $config = [])
    {
        if (!in_array($direction, self::getSortDirections())) {
            throw new InvalidArgumentException('Invalid direction');
        }
        $config['direction'] = $direction;
        $this->sorts[$field] = $config;
        return $this;
    }

    /**
     * Add multiple sorts
     *
     * @param array $sorts
     * @param bool $overwrite
     * @return $this
     */
    public function sorts(array $sorts, $overwrite = false)
    {
        if ($overwrite) {
            $this->clearSorts();
        }
        foreach ($sorts as $field => $config) {
            if (is_string($config)) {
                $config = ['direction' => $config];
            } else if (is_array($config) && !isset($config['direction'])) {
                $config['direction'] = 'asc';
            }
            $this->sort($field, $config['direction'], $config);
        }
        return $this;
    }

    /**
     * Get all sorts
     *
     * @return array
     */
    public function getSorts()
    {
        return $this->sorts;
    }

    /**
     * Control status of allowing pagination
     *
     * @param bool $bool
     * @return $this
     */
    public function enablePagination($bool = true)
    {
        $this->pagination_enabled = $bool;
        return $this;
    }

    /**
     * Disable pagination
     *
     * @return Scope
     */
    public function disablePagination()
    {
        return $this->enablePagination(false);
    }

    /**
     * Determine if pagination is enabled
     *
     * @return bool
     */
    public function isPaginating()
    {
        return $this->pagination_enabled;
    }

    /**
     * Set current page for pagination
     *
     * @param int $page
     * @return $this
     */
    public function page($page)
    {
        if (!is_numeric($page)) {
            $page = 1;
        }
        $this->page = (int) $page;
        if ($this->page < 1) {
            $this->page = 1;
        }
        return $this;
    }

    /**
     * Get current page
     *
     * @return int
     */
    public function getPage()
    {
        return $this->page;
    }

    /**
     * Set per page limit for pagination
     *
     * @param int|null $per_page
     * @return $this
     */
    public function perPage($per_page)
    {
        if ($per_page !== null) {
            if (!is_numeric($per_page)) {
                $per_page = 25;
            }
            $per_page = (int) $per_page;
            if ($per_page < 1) {
                $per_page = 1;
            }
        }
        $this->per_page = $per_page;
        return $this;
    }

    /**
     * Get per page limit
     *
     * @return int|null
     */
    public function getPerPage()
    {
        return $this->per_page;
    }

    /**
     * Add query callback
     *
     * @param Closure $closure
     * @return $this
     */
    public function query(Closure $closure)
    {
        $this->attach('query', $closure);
        return $this;
    }

    /**
     * Controls status of allowing query customization
     *
     * @param bool $bool
     * @return $this
     */
    public function enableQuery($bool = true)
    {
        $this->query_enabled = $bool;
        return $this;
    }

    /**
     * Disable query customization
     *
     * @return Scope
     */
    public function disableQuery()
    {
        return $this->enableQuery(false);
    }

    /**
     * Determine if query customization is allowed
     *
     * @return bool
     */
    public function isQueryEnabled()
    {
        return $this->query_enabled;
    }

    /**
     * Control status of allowing relations
     *
     * @param bool $bool
     * @return $this
     */
    public function enableRelations($bool = true)
    {
        $this->relations_enabled = $bool;
        return $this;
    }

    /**
     * Disable relations
     *
     * @return Scope
     */
    public function disableRelations()
    {
        return $this->enableQuery(false);
    }

    /**
     * Determine if relations are enabled
     *
     * @return bool
     */
    public function isRelationsEnabled()
    {
        return $this->relations_enabled;
    }

    /**
     * Add relations using the same syntax as the Eloquent with() method
     *
     * @param array ...$relations
     * @return $this
     */
    public function with(...$relations)
    {
        $count = count($relations);
        if ($count === 0) {
            throw new InvalidArgumentException('At least one relationship required');
        }
        if ($count === 1 && is_array($relations[0])) {
            $relations = $relations[0];
        }
        foreach ($relations as $name => $item) {
            if (is_object($item) && $item instanceof self) {
                $scope = $item;
            } else {
                if (is_integer($name)) {
                    $name = $item;
                    $item = [];
                } else if (is_object($item) && $item instanceof Closure) {
                    $item = ['query' => $item];
                } else if (!is_array($item)) {
                    $item = [];
                }
                $scope = Scope::fromArray($item);
            }
            // if name is a nested relationship in dot syntax, inject 'relations' keys between each one
            // to make storage of the scopes easy
            if (strpos($name, '.') !== false) {
                $name = explode('.', $name);
                $_name = [];
                for ($i = 0, $c = count($name); $i < $c; $i++) {
                    if ($i !== 0) {
                        $_name[] = 'relations';
                    }
                    $_name[] = $name[$i];
                }
                $name = implode('.', $_name);
            }
            Arr::set($this->with, "{$name}.scope", $scope);
        }
        return $this;
    }

    /**
     * Configure internal with setup
     *
     * Takes with config returned from ->toArray() method and converts scopes back into Scope entities
     *
     * @param array $with
     */
    public function configureWith(array $with)
    {
        $this->with = $this->importWith($with);
    }

    /**
     * Add relation
     *
     * If no scope is provided and empty one will be added automatically
     *
     * @param $name
     * @param self|null $scope
     * @return $this
     */
    public function relation($name, self $scope = null)
    {
        if ($scope === null) {
            $scope = new static;
        }
        $scope->setParent($this);
        $this->relations[$name] = $scope;
        return $this;
    }

    /**
     * Add multiple relations
     *
     * Can add by either array value or a key => value pair. If array value is passed, then it will be used as the name.
     * Otherwise, the key is the name and it's associated value is the scope.
     *
     * @param array $relations
     * @return $this
     */
    public function relations(array $relations)
    {
        foreach ($relations as $name => $scope) {
            if (is_integer($name)) {
                $name = $scope;
                $scope = null;
            }
            $this->relation($name, $scope);
        }
        return $this;
    }

    /**
     * Configure internal relations
     *
     * Takes relations compiled by ->toArray() method and converts them back into proper scope instances
     *
     * @param array $relations
     */
    public function configureRelations(array $relations)
    {
        foreach ($relations as $name => $scope) {
            $this->relation($name, Scope::fromArray($scope));
        }
    }

    /**
     * Determine if any relations have been defined
     *
     * @return bool
     */
    public function hasRelations()
    {
        return count($this->relations) > 0;
    }

    /**
     * Get relations
     *
     * @return Scope[]
     */
    public function getRelations()
    {
        return $this->relations;
    }

    /**
     * Add scope for polymorphic relation
     *
     * @param mixed $type
     * @param Scope|array $scope
     * @return $this
     */
    public function polyScope($type, $scope)
    {
        if (!($scope instanceof Scope)) {
            $scope = Scope::fromArray($scope);
        }
        $this->poly_scopes[$type] = $scope;
        return $this;
    }

    /**
     * Get scope for polymorphic relation type
     *
     * @param mixed $type
     * @return Scope|null
     */
    public function getPolyScope($type)
    {
        if (!isset($this->poly_scopes[$type])) {
            return null;
        }
        return $this->poly_scopes[$type];
    }

    /**
     * Set eager load status
     *
     * @param bool $bool
     * @return $this
     */
    public function eagerLoad($bool = false)
    {
        $this->eager_load = $bool;
        return $this;
    }

    /**
     * Disable eager loading
     *
     * @return Scope
     */
    public function disableEagerLoad()
    {
        return $this->eagerLoad(false);
    }

    /**
     * Determines if relation assigned this scope can be eager loaded by Eloquent via with()
     *
     * @return bool
     */
    public function canEagerLoad()
    {
        return $this->eager_load;
    }

    /**
     * Convert exported with config back into proper format
     *
     * Scopes converted to arrays are initialized again
     *
     * @param array $with
     * @return array
     */
    protected function importWith(array $with)
    {
        foreach ($with as $name => &$item) {
            if (isset($item['scope'])) {
                $item['scope'] = Scope::fromArray($item['scope']);
            }
            if (isset($item['relations'])) {
                $item['relations'] = $this->importWith($item['relations']);
            }
        }
        return $with;
    }

    /**
     * Convert internal with config into array format
     *
     * All stored scopes are converted to an array
     *
     * @param array $with
     * @return array
     */
    protected function exportWith(array $with)
    {
        foreach ($with as $name => &$item) {
            if (isset($item['scope'])) {
                $item['scope'] = $item['scope']->toArray();
            }
            if (isset($item['relations'])) {
                $item['relations'] = $this->exportWith($item['relations']);
            }
        }
        return $with;
    }

    /**
     * Recursively assign 'with' relation data to scopes
     *
     * This takes the format which with() data is saved and sends the relations to each respective scope (creating one
     * if none was defined)
     *
     * @param Scope $scope
     * @param array $with
     * @return Scope
     */
    protected function handleWith(Scope $scope, array $with)
    {
        foreach ($with as $name => $item) {
            if (!isset($item['scope'])) {
                $item['scope'] = new static;
            }
            if (isset($item['relations'])) {
                $item['scope'] = $this->handleWith($item['scope'], $item['relations']);
            }
            $scope->relation($name, $item['scope']);
        }
        return $scope;
    }

    /**
     * Compile with data into proper relations
     *
     * Creates/assigns scopes to the proper relations down the with chain
     */
    public function compileWith()
    {
        $this->handleWith($this, $this->with);
    }

    /**
     * Convert scope data to array
     */
    public function toArray()
    {
        $scope = [];
        $keys = [
            'format', 'fields', 'no_fields', 'filters', 'filters_enabled', 'search', 'sorts', 'sorts_enabled',
            'pagination_enabled', 'page', 'per_page', 'query_enabled', 'relations', 'with', 'poly_scopes', 'eager_load'
        ];
        foreach ($keys as $key) {
            switch ($key) {
                case 'relations':
                    $key = 'relation_config';
                    $value = array_map(function (self $scope) {
                        return $scope->toArray();
                    }, $this->relations);
                    break;
                case 'with':
                    $key = 'with_config';
                    $value = $this->exportWith($this->with);
                    break;
                case 'poly_scopes':
                    $value = array_map(function (self $scope) {
                        return $scope->toArray();
                    }, $this->poly_scopes);
                    break;
                default:
                    $value = $this->{$key};
                    break;
            }
            $scope[$key] = $value;
        }
        return $scope;
    }

    /**
     * Create new scope instance from an array syntax
     *
     * @param array $item
     * @return Scope
     */
    public static function fromArray(array $item)
    {
        $scope = new static;
        $keys = [
            'format', 'fields', 'no_fields' => 'noFields', 'filters_enabled' => 'enableFilters', 'filters', 'search',
            'query', 'query_enabled' => 'enableQuery', 'sorts', 'sorts_enabled' => 'enableSorts', 'page',
            'per_page' => 'perPage', 'relation_config' => 'configureRelations', 'relations_enabled' => 'enableRelations',
            'with', 'with_config' => 'configureWith', 'eager_load' => 'eagerLoad', 'poly_scopes'
        ];
        foreach ($keys as $key => $func) {
            if (is_numeric($key)) {
                $key = $func;
            }
            if (!isset($item[$key])) {
                continue;
            }
            switch ($key) {
                case 'poly_scopes':
                    foreach ($item[$key] as $type => $_scope) {
                        $scope->polyScope($type, $_scope);
                    }
                    break;
                default:
                    $scope->{$func}($item[$key]);
                    break;
            }
        }
        return $scope;
    }
}
