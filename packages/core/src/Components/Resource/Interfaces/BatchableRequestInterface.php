<?php

namespace Core\Components\Resource\Interfaces;

use Core\Components\Resource\Classes\BatchRequest;

interface BatchableRequestInterface extends RequestInterface
{
    public function inBatch($bool = true);

    public function isInBatch();

    public function batchLastRequest(BatchableRequestInterface $request);

    public function setParentBatchRequest(BatchRequest $request);

    public function getParentBatchRequest();

    public function setUseTransaction(?bool $use_transaction);

    public function useTransaction(): ?bool;
}
