<?php

namespace Core\Components\Resource\Interfaces;

use Core\Components\Http\Interfaces\ResponseInterface;
use Core\Components\Resource\Classes\MediaTypeVersion;

/**
 * Interface MediaHandlerInterface
 *
 * @package Core\Components\Resource\Interfaces
 */
interface MediaHandlerInterface
{
    /**
     * Set resource for media handler
     *
     * @param ResourceMediaInterface $resource
     */
    public function setResource(ResourceMediaInterface $resource);

    /**
     * Set MediaTypeVersion instance associated with handler
     *
     * @param MediaTypeVersion $version
     */
    public function setMediaTypeVersion(MediaTypeVersion $version);

    /**
     * Get response for media controller
     *
     * @param mixed $id
     * @param array $config
     * @return ResponseInterface
     */
    public function getResponse(mixed $id, array $config = []): ResponseInterface;
}
