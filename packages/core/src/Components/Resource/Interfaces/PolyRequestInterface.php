<?php

namespace Core\Components\Resource\Interfaces;

/**
 * Interface PolyRequestInterface
 *
 * @package Core\Components\Resource\Interfaces
 */
interface PolyRequestInterface
{
    /**
     * @return \Core\Components\Resource\Classes\Entity
     */
    public function getMainEntity();

    /**
     * @return array
     */
    public function getPolyEntities();

    /**
     * @param string $name
     * @return array
     */
    public function getPolyEntity($name);

    /**
     * @return array
     */
    public function getMediaEntities();

    /**
     * @param string $name
     * @return array
     */
    public function getMediaEntity($name);
}
