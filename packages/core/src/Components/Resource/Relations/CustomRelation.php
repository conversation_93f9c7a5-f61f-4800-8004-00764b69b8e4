<?php

namespace Core\Components\Resource\Relations;

use Closure;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Relation;
use Core\Components\Resource\Classes\Scope;
use Illuminate\Database\Eloquent\Model;

/**
 * Class CustomRelation
 *
 * @package Core\Components\Resource\Relations
 */
class CustomRelation extends Relation
{
    /**
     * Set callback for scope configuration
     *
     * @param Closure $closure
     * @return $this
     */
    public function scopeCallback(Closure $closure)
    {
        $this->set('scope', $closure);
        return $this;
    }

    /**
     * Set callback for data processing
     *
     * @param Closure $closure
     * @return $this
     */
    public function dataCallback(Closure $closure)
    {
        $this->set('data', $closure);
        return $this;
    }

    /**
     * Set scope
     *
     * If no scope callback is defined, we disable eager loading automatically
     *
     * @param Scope $scope
     * @return Relation
     */
    public function scope(Scope $scope)
    {
        if (isset($this->config['scope'])) {
            $scope = $this->config['scope']($scope, $this);
        } else {
            $scope->disableEagerLoad();
        }

        return parent::scope($scope);
    }

    /**
     * Handle entity
     *
     * If data processing callback is defined, model and entity are passed to it to let the user change the data
     *
     * @param Model $model
     * @param Entity $entity
     * @return Entity
     */
    public function handleEntity(Model $model, Entity $entity)
    {
        if (isset($this->config['data'])) {
            $entity = $this->config['data']($model, $entity, $this);
        }
        return $entity;
    }
}
