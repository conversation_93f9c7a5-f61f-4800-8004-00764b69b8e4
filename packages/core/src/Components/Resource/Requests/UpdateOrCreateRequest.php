<?php

namespace Core\Components\Resource\Requests;

use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Request;
use Core\Components\Resource\Classes\Resource;
use Core\Components\Resource\Exceptions\EntityNotFoundException;
use Core\Components\Resource\Interfaces\BatchableRequestInterface;
use Core\Components\Resource\Interfaces\ValidatedRequestInterface;
use Core\Components\Resource\Traits\Request\BatchableTrait;
use Core\Components\Resource\Traits\Request\EntityTrait;
use Core\Components\Resource\Traits\Request\NestedTrait;
use Core\Components\Resource\Traits\Request\SaveProxyTrait;
use Core\Components\Resource\Traits\Request\ScopeTrait;
use Core\Components\Resource\Traits\Request\TransactionTrait;

/**
 * Class UpdateOrCreateRequest
 *
 * Request that proxies an update or create request depending on if an update cannot be performed
 *
 * @package Core\Components\Resource\Requests
 */
class UpdateOrCreateRequest extends Request implements BatchableRequestInterface, ValidatedRequestInterface
{
    use BatchableTrait;
    use EntityTrait;
    use NestedTrait;
    use SaveProxyTrait;
    use ScopeTrait;
    use TransactionTrait;

    public function __construct(Resource $resource, Entity $entity)
    {
        parent::__construct($resource);

        $this->setEntity($entity);
    }

    /**
     * Setup request, copy any settings set on this request to the actual request
     *
     * @param Request $request
     * @return Request
     */
    protected function setupRequest(Request $request)
    {
        if ($this->isNested()) {
            $request->nested();
        }
        if ($this->hasScope()) {
            $request->scope($this->getScope());
        }
        if (!$this->isActionCheckEnabled()) {
            $request->disableActionCheck();
        }
        $request = $this->applyCallback('request', $request);
        $request->setUseTransaction($this->useTransaction());
        return $request;
    }

    /**
     * Create new update request using passed entity
     *
     * @return UpdateRequest
     */
    protected function newUpdateRequest()
    {
        return $this->resource->update(clone $this->getEntity());
    }

    /**
     * Create new create request using
     *
     * @return CreateRequest
     */
    protected function newCreateRequest()
    {
        return $this->resource->create(clone $this->getEntity());
    }

    /**
     * Prepare request
     *
     * @throws \Core\Exceptions\AppException
     */
    public function prepare()
    {
        parent::prepare();

        $request = $this->setupRequest($this->newUpdateRequest());
        $request = $this->applyCallback('update_request', $request);
        // force disable partial updates since they will not work for this type of request. all the information is
        // need to create so allowing a partial update doesn't make sense because the create request will fail.
        $request->partial(false);

        // since this is a proxy request, we set the update request as the source so any calls made to
        // this instance will be passed on to it
        $this->setRequest($request);

        try {
            $request->prepare();
        } catch (EntityNotFoundException $e) {
            // if update request failed because it couldn't find an entity, then we try a create request
            $request = $this->setupRequest($this->newCreateRequest());
            $request = $this->applyCallback('create_request', $request);

            // we override the request for proxy methods to now be the create request instead of update
            $this->setRequest($request);

            $request->prepare();
        }

        $this->prepared = true;
    }

    /**
     * Handle request
     *
     * Runs handle on nested request which was determined by prepare()
     *
     * @throws \Core\Exceptions\AppException
     */
    public function handle()
    {
        parent::handle();

        $this->request->handle();
    }
}
