<?php

namespace Core\Components\Resource\Traits\Request;

use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\MediaField;
use Core\Components\Resource\Classes\PolyField;

trait PolyTrait
{
    /**
     * Main entity which all poly and media entities are pulled from
     *
     * @var null|Entity
     */
    protected $main_entity = null;

    /**
     * Poly entities found within main entity
     *
     * @var array
     */
    protected $poly_entities = [];

    /**
     * Media entities found within main entity
     *
     * @var array
     */
    protected $media_entities = [];

    /**
     * Set main entity
     *
     * @param Entity $entity
     */
    protected function setMainEntity(Entity $entity)
    {
        $this->main_entity = $entity;
    }

    /**
     * @return Entity
     */
    public function getMainEntity()
    {
        return $this->main_entity;
    }

    protected function addPolyEntity($name, PolyField $field, Entity $entity)
    {
        $this->poly_entities[$name] = compact('field', 'entity');
    }

    public function getPolyEntities()
    {
        return $this->poly_entities;
    }

    public function getPolyEntity($name)
    {
        return isset($this->poly_entities[$name]) ? $this->poly_entities[$name] : null;
    }

    protected function addMediaEntity($name, MediaField $field, Entity $entity)
    {
        $this->media_entities[$name] = compact('field', 'entity');
    }

    public function getMediaEntities()
    {
        return $this->media_entities;
    }

    public function getMediaEntity($name)
    {
        return isset($this->media_entities[$name]) ? $this->media_entities[$name] : null;
    }
}
