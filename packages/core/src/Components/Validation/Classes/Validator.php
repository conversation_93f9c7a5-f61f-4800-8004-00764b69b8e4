<?php

declare(strict_types=1);

namespace Core\Components\Validation\Classes;

use Core\Classes\Arr;

/**
 * Class Validator
 *
 * @package Core\Components\Validation\Classes
 */
class Validator
{
    /**
     * ErrorContainer instance
     *
     * @var ErrorContainer
     */
    protected ErrorContainer $errors;

    /**
     * Valid status
     *
     * @var bool
     */
    protected bool $valid = false;

    /**
     * Validator constructor
     *
     * @param FieldConfig $config
     * @param Rules $rules
     * @param array $data
     */
    public function __construct(protected FieldConfig $config, protected Rules $rules, protected array $data)
    {
        $this->errors = new ErrorContainer();

        $this->validate();
    }

    /**
     * Get FieldConfig instance
     *
     * @return FieldConfig
     */
    public function getConfig(): FieldConfig
    {
        return $this->config;
    }

    /**
     * Get data value by key
     *
     * @param null|string $key
     * @param null|mixed $default
     * @return mixed
     */
    public function data(?string $key = null, mixed $default = null): mixed
    {
        return Arr::get($this->data, $key, $default);
    }

    /**
     * Get data with optional wildcards
     *
     * @param string $key
     * @return array
     */
    protected function expandedData(string $key): array
    {
        return Arr::expandedGet($this->data, $key);
    }

    /**
     * Set data value by key
     *
     * @param string $key
     * @param mixed $value
     */
    public function setData(string $key, mixed $value): void
    {
        Arr::set($this->data, $key, $value);
    }

    /**
     * Run validation on data
     */
    protected function validate(): void
    {
        foreach ($this->config->getFields() as $field) {
            if (!$field->hasRules()) {
                continue;
            }
            $key = $field->getKey();
            list($should_expand, $expanded, $field_value) = $this->expandedData($key);
            $validate_config = array_merge($field->toArray(), ['run_args' => [$this]]);

            $run_validation = true;
            if ($should_expand) {
                if (!$expanded) {
                    $this->errors->add(
                        $field->get('group_error_key', $key),
                        $this->rules->getMessage('type_array', ['label' => $validate_config['label']])
                    );
                    $run_validation = false;
                } elseif ($field->hasGroupRules()) {
                    $group_value = array_map(function ($value) {
                        return $value['value'];
                    }, $field_value);
                    $validate = $this->rules->validate($field->getGroupRules(), $group_value, $validate_config);
                    if (!$validate['valid']) {
                        $this->errors->add($field->get('group_error_key', $key), $validate['message']);
                    }
                }
            } else {
                $field_value = [$field_value];
            }

            if ($run_validation) {
                foreach ($field_value as $_field_value) {
                    $validate = $this->rules->validate($field->getRules(), $_field_value['value'], $validate_config);
                    if (!$validate['valid']) {
                        $this->errors->add($field->get('error_key', $_field_value['key']), $validate['message']);
                    }
                    $this->setData($_field_value['key'], $validate['value']);
                }
            }
        }
        if ($this->errors->count() === 0) {
            $this->valid = true;
        }
    }

    /**
     * Get valid status
     *
     * @return bool
     */
    public function valid(): bool
    {
        return $this->valid;
    }

    /**
     * @return ErrorContainer
     */
    public function errors(): ErrorContainer
    {
        return $this->errors;
    }
}
